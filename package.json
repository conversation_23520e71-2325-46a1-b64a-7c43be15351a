{"name": "mc-web-packages", "version": "0.0.1", "private": true, "scripts": {"start": "pnpm --filter=!mc-headless --filter=!aimi-web --parallel -r run start", "docs:start": "pnpm --filter=mc-docs --parallel run /^docs:.*/", "headless:start": "pnpm --filter=!mc-web --parallel -r run start", "aimi:start": "pnpm --filter=!mc-web --filter=!mc-headless --parallel -r run start", "scan": "pnpm --filter=mc-web run scan", "build": "pnpm -r run build", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "stylelint:fix": "pnpm stylelint --fix", "lint": "pnpm oxlint && pnpm stylelint", "lint:fix": "pnpm oxlint:fix && pnpm stylelint:fix", "oxlint": "oxlint --config=./.oxlintrc.json --tsconfig=./tsconfig.base.json", "oxlint:fix": "oxlint --fix --fix-suggestions --config=./.oxlintrc.json --tsconfig=./tsconfig.base.json ", "prepare": "husky", "genOAPI": "node ./scripts/genOAPI.js", "genAI": "node ./scripts/genAi.js", "genAimi": "node ./scripts/genAimi.js", "genPlugin": "node ./scripts/genPlugin.js", "genMC": "node ./scripts/genMC.js && node ./scripts/genScheduler.js && node ./scripts/genLog.js && node ./scripts/genAi.js", "clean": "rimraf pnpm-lock.yaml && rimraf .ice && rimraf node_modules", "svgr": "pnpm --filter=./{packages/mc-uikit,packages/mc-web,packages/mc-icons,packages/aimi-web} --parallel -r run svgr"}, "devDependencies": {"@ali/mc-uikit": "workspace:*", "@ant-design/icons": "^5.6.1", "@applint/spec": "^1.2.3", "@changesets/cli": "^2.29.4", "@faker-js/faker": "^8.4.1", "@ice/app": "^3.6.1", "@ice/pkg": "^1.6.2", "@ice/plugin-icestark": "^1.2.1", "@svgr/webpack": "^8.1.0", "@types/yargs-parser": "^21.0.3", "antd": "^5.25.4", "chalk": "^5.4.1", "dayjs": "^1.11.13", "events": "^3.3.0", "handlebars": "^4.7.8", "husky": "^9.1.7", "less": "^4.3.0", "oxlint": "^0.16.12", "prettier": "3.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "rimraf": "^4.4.1", "stylelint": "^15.11.0", "svgo": "^3.3.2", "swagger-typescript-api": "13.0.21", "url-loader": "^4.1.1"}, "lint-staged": {"**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,astro,svelte}": "ox<PERSON>"}, "pnpm": {"overrides": {"react": "^18.3.1", "react-dom": "^18.3.1", "antd": "^5.24.7", "@ice/app": "^3.6.1", "@ice/runtime": "^1.5.2", "@ice/jsx-runtime": "0.3.1", "swagger-typescript-api": "13.0.21", "react-router-dom": "6.26.2", "@ice/route-manifest": "1.3.0", "@antv/g2": "5.2.10", "get-tsconfig": "4.8.1", "copy-to-clipboard": "^3.3.3"}}, "repository": "http://gitlab.alibaba-inc.com/mtl-cloud/mc-web-packages.git", "packageManager": "pnpm@8.6.0", "dependencies": {"copy-to-clipboard": "^3.3.3"}}