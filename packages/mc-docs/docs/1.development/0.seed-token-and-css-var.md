# Seed Token 和 CSS 变量

## 前置阅读
请确保已经完整阅读 AntD 关于定制主题部分的文档，并初步理解他的工作机制。
[定制主题 - Ant Design](https://ant-design.antgroup.com/docs/react/customize-theme-cn)
## 开发规范
由于涉及到暗黑没事和紧凑模式的切换，原则上通用场景下，代码中严禁将色值、尺寸、边距相关的数值进行硬编码，相关的 style 和 less（css）中，全部通过 AntD 的 seed token 和 CSS 变量来处理。
## 常用 Token 变量
### 文本颜色、尺寸
一般情况下，文本颜色不需要额外设置

| 用途 | Token | CSS Var | 说明 |
| --- | --- | --- | --- |
| 默认文本颜色。 默认情况下，文本颜色不需要额外指定，如果你需要使用主颜色覆盖某个场景的其他颜色设置，请使用这个 token | colorText | --mc-color-text ||
| 二级文本色。 一般用在不那么需要强化文本颜色的场景，例如 Label 文本、Menu 的文本选中态等场景 | colorTextSecondary | --mc-color-text-secondary ||
| 三级文本色。 一般用于描述性文本，例如表单的中的补充说明文本、列表的描述性文本等场景 | colorTextTertiary | --mc-color-text-tertiary | |
| 四级文本色。 最浅的文本色，例如表单的输入提示文本、禁用色文本等 | colorTextQuaternary | --mc-color-text-quaternary |  |
| H1 标签所使用的字号 | fontSizeHeading1 | | 默认值 38px |
| h2 标签所使用的字号 | fontSizeHeading2 | | 默认值 30px |
| h3 标签使用的字号 | fontSizeHeading3 |  | 默认值 24px |
| h4 标签使用的字号 | fontSizeHeading4 |  | 默认值 20px |
| h5 标签使用的字号 | fontSizeHeading5 |  | 默认值 16px |
| 大号字体大小 | fontSizeLG |  | 默认值 16px |
| 小号字体大小 | fontSizeSM |  | 默认值 12px |
| 超大号字体大小 | fontSizeXL |  | 默认值 20px |

### 尺寸
| 用途 | Token | CSS Var | 说明 |
| --- | --- | --- | --- |
| 默认尺寸 | size |  | 16 |
|  | sizeLG |  | 24 |
|  | sizeMD |  | 20 |
|  | sizeMS |  | 16 |
|  | sizeSM |  | 12 |
|  | sizeXL |  | 32 |
|  | sizeXS |  | 8 |
|  | sizeXXL |  | 48 |
|  | sizeXXS |  | 4 |

### 内间距
| 用途 | Token | CSS Var | 说明 |
| --- | --- | --- | --- |
| 元素水平内间距 | controlPaddingHorizontal | --mc-control-padding-horizontal | 默认值 12px |
| 元素中小尺寸水平内间距 | controlPaddingHorizontalSM | --mc-control-padding-horizontal-sm | 默认值 8px |
| 元素的内间距 | padding |  | 默认值 16 px |
| 内容元素水平内间距 | paddingContentHorizontal |  | 默认值 16 px |
| 内容元素水平内间距，适用于大屏幕设备 | paddingContentHorizontalLG |  | 默认值 24px |
| 内容元素水平内间距，适用于小屏幕设备 | paddingContentHorizontalSM |  | 默认值 16px |
| 内容元素垂直内间距 | paddingContentVertical |  | 默认值 12px |
| 内容元素垂直内间距，适用于大屏幕设备 | paddingContentVerticalLG |  | 默认值 16px |
| 内容元素垂直内间距，适用于小屏幕设备 | paddingContentVerticalSM |  | 默认值 8px |
| 元素的大内间距 | paddingLG |  | 默认值 24px |
| 元素的中等内间距 | paddingMD |  | 默认值 20px |
| 元素的小内间距 | paddingSM |  | 默认值 12px |
| 元素的特大内间距 | paddingXL |  | 默认值 32px |
| 元素的特小内间距 | paddingXS |  | 默认值 8px |
| 元素的极小内间距 | paddingXXS |  | 默认值 4px |

### 外边距
| 用途 | Token | CSS Var | 说明 |
| --- | --- | --- | --- |
| 元素外边距，中等尺寸 | margin |  | 默认值 16px |
| 控制元素外边距，大尺寸 | marginLG | | 默认值 24px |
| 控制元素外边距，中大尺寸 | marginMD | | 默认值 20px |
| 控制元素外边距，中小尺寸 | marginSM | | 默认值 12px |
| 控制元素外边距，超大尺寸 | marginXL | | 默认值 32px |
| 控制元素外边距，小尺寸 | marginXS |  | 默认值 8 px |
| 控制元素外边距，最大尺寸 | marginXXL |  | 默认值 48px |
| 控制元素外边距，最小尺寸 | marginXXS |  | 默认值 4 px |

### 颜色
| 用途 | Token | CSS Var | 说明 |
| --- | --- | --- | --- |
| 元素外边距，中等尺寸 | margin |  | 默认值 16px |
| 控制元素外边距，大尺寸 | marginLG | | 默认值 24px |
| 控制元素外边距，中大尺寸 | marginMD | | 默认值 20px |
| 控制元素外边距，中小尺寸 | marginSM | | 默认值 12px |
| 控制元素外边距，超大尺寸 | marginXL | | 默认值 32px |
| 控制元素外边距，小尺寸 | marginXS |  | 默认值 8 px |
| 控制元素外边距，最大尺寸 | marginXXL |  | 默认值 48px |
| 控制元素外边距，最小尺寸 | marginXXS |  | 默认值 4 px |