# StatusBadge 状态徽标
常见的 CI、Cov 状态徽标

## 代码演示

```tsx preview
import React from 'react';
import { Flex } from 'antd';
import { StatusBadge } from '@ali/mc-uikit';
import { DemoLayout } from '@ali/mc-docs';

export default function StaticBadgeDemo() {
  return (
    <DemoLayout>
      <Flex
        gap="small"
        wrap
      >
        <StatusBadge
          label="工程标准"
          status="magenta"
          color="magenta"
        />
        <StatusBadge
          label="工程标准"
          status="red"
          color="red"
        />
        <StatusBadge
          label="工程标准"
          status="volcano"
          color="volcano"
        />
        <StatusBadge
          label="工程标准"
          status="orange"
          color="orange"
        />
        <StatusBadge
          label="工程标准"
          status="gold"
          color="gold"
        />
        <StatusBadge
          label="工程标准"
          status="lime"
          color="lime"
        />
        <StatusBadge
          label="工程标准"
          status="green"
          color="green"
        />
        <StatusBadge
          label="工程标准"
          status="cyan"
          color="cyan"
        />
        <StatusBadge
          label="工程标准"
          status="blue"
          color="blue"
        />
        <StatusBadge
          label="工程标准"
          status="geekblue"
          color="geekblue"
        />
        <StatusBadge
          label="工程标准"
          status="purple"
          color="purple"
        />
      </Flex>
    </DemoLayout>
  );
}
```
