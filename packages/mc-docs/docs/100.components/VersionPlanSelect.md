# VersionPlanSelect 版本计划选择器

## 代码演示

```tsx preview
import React from 'react';
import { Flex } from 'antd';
import { DemoLayout } from '@ali/mc-docs';
import {VersionPlanSelect } from '@ali/mc-uikit';
import dayjs from 'dayjs';

export default () => {
  return (
    <DemoLayout>
      <Flex gap="small" vertical>
        <VersionPlanSelect applicationId={1} defaultValue={965}/>
        <VersionPlanSelect
          applicationId={1}
          showSearch
          placeholder="输入关键字搜索版本计划"
        />
        <VersionPlanSelect
          placeholder="版本计划的结束时间结束时间的区间"
          applicationId={1}
          showSearch
          lowerEndDate={dayjs().subtract(1000, 'day').valueOf()}
        />
      </Flex>
    </DemoLayout>
  );
};
```
## API
```tsx
export type VersionPlanSelectProps = {
  /**
   * 应用 ID
   */
  applicationId: number;

  /**
   * 版本计划 ID
   */
  value?: number;

  /**
   * 是否简单进行额外属性封装
   */
  isSimple?: boolean;

  /**
   * 是否按开始时间正序排序
   */
  startTimeAsc?: boolean;

  /**
   * 版本计划的结束时间大于等于该参数
   */
  lowerEndDate?: number;

  /**
   * 版本计划的开始时间小于等于该参数
   */
  higherStartDate?: number;
} & PrefixSelectProps;
```
