# VersionSelect 版本选择器
针对 Application、Module 使用的，版本列表选择器

## 代码演示

```tsx preview
import React from 'react';
import { DemoLayout } from '@ali/mc-docs';
import { VersionSelect } from '@ali/mc-uikit';

export default () => {
  return (
    <DemoLayout>
      <VersionSelect
        prefix="版本号："
        applicationId={1}
        allowClear
        placeholder="请选择版本号"
      />
    </DemoLayout>
  );
}
```

## API
```ts
type VersionSelectProps = PrefixSelectProps & {
  /**
   * 应用、模块 ID
   */
  applicationId?: number;

  /**
   * 集成区 ID
   */
  integrateAreaId?: number;

  /**
   * 变更单 ID
   */
  alterSheetId?: number;

  /**
   * 发布类型
   */
  releaseType?: QueryReleasePageParams['releaseType'];

  /**
   * 发布类型
   */
  publishType?: string;

  /**
   * 发布状态
   */
  status?: QueryReleasePageParams['statusList'];
};
```
