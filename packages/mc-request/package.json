{"name": "@ali/mc-request", "version": "1.0.0", "description": "对 ahooks 的基础封装", "files": ["esm", "es2017", "cjs", "dist"], "main": "esm/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "sideEffects": ["dist/*"], "scripts": {"start": "ice-pkg start", "build": "ice-pkg build", "prepublishOnly": "npm run build"}, "keywords": ["ice", "web", "library"], "dependencies": {"@ice/jsx-runtime": "^0.2.0", "@swc/helpers": "^0.5.1", "ahooks": "^3.7.8", "core-js": "^3.36.0"}, "devDependencies": {"@ice/pkg": "^1.0.0"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1", "dayjs": "^1"}, "overrides": {"react": "18.3.1", "react-dom": "18.3.1"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "license": "MIT"}