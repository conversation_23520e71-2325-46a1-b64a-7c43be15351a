{"name": "@ali/mc-icons", "version": "1.4.0", "description": "摩天轮图标", "files": ["esm", "dist"], "main": "esm/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "sideEffects": ["dist/*"], "scripts": {"start": "ice-pkg start", "build": "ice-pkg build", "svgr:outlined": "svgr --config-file=./config/outlined.config.js ./icons/outlined --out-dir ./src/outlined", "svgr:filled": "svgr --config-file=./config/filled.config.js ./icons/filled --out-dir ./src/filled", "svgr": "pnpm run svgr:outlined && pnpm run svgr:filled"}, "keywords": ["ice", "library"], "dependencies": {"@ice/jsx-runtime": "^0.2.0", "@swc/helpers": "^0.5.1", "classnames": "^2.5.1"}, "devDependencies": {"@babel/parser": "^7.24.8", "@babel/traverse": "^7.24.8", "@ice/pkg": "^1.0.0", "@svgr/cli": "^8.1.0", "lodash": "^4.17.21"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}, "overrides": {"react": "18.3.1", "react-dom": "18.3.1"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "license": "MIT"}