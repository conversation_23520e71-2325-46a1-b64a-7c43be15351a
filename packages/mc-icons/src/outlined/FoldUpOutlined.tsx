import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function FoldUpOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-fold-up-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_05026">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_05026)">
          <path
            d="M7.823215953979492,1.6767400323486328L4.9267802539794925,4.573187532348633C4.769289953979492,4.730677532348633,4.880829853979492,4.999957532348633,5.103549953979492,4.999957532348633L7.249995953979492,4.999957532348633L7.249995953979492,8.235667532348632C7.249995953979492,8.649887532348632,7.585785953979492,8.985667532348632,7.999995953979492,8.985667532348632C8.414205953979492,8.985667532348632,8.749995953979493,8.649887532348632,8.749995953979493,8.235667532348632L8.749995953979493,4.999957532348633L10.896395953979493,4.999957532348633C11.119195953979492,4.999957532348633,11.230695953979492,4.730677532348633,11.073195953979493,4.573187532348633L8.176775953979492,1.6767400323486328C8.079145953979491,1.579110032348633,7.920855953979492,1.579110032348633,7.823215953979492,1.6767400323486328Z"
            fill="currentColor"
          />
          <path
            d="M13.75,11C13.3358,11,13,11.3358,13,11.75C13,12.1642,13.3358,12.5,13.75,12.5L14.25,12.5C14.6642,12.5,15,12.1642,15,11.75C15,11.3358,14.6642,11,14.25,11L13.75,11Z"
            fill="currentColor"
          />
          <path
            d="M10,11.75C10,11.3358,10.3358,11,10.75,11L11.25,11C11.6642,11,12,11.3358,12,11.75C12,12.1642,11.6642,12.5,11.25,12.5L10.75,12.5C10.3358,12.5,10,12.1642,10,11.75Z"
            fill="currentColor"
          />
          <path
            d="M7.75,11C7.33579,11,7,11.3358,7,11.75C7,12.1642,7.33579,12.5,7.75,12.5L8.25,12.5C8.66421,12.5,9,12.1642,9,11.75C9,11.3358,8.66421,11,8.25,11L7.75,11Z"
            fill="currentColor"
          />
          <path
            d="M4,11.75C4,11.3358,4.33579,11,4.75,11L5.25,11C5.66421,11,6,11.3358,6,11.75C6,12.1642,5.66421,12.5,5.25,12.5L4.75,12.5C4.33579,12.5,4,12.1642,4,11.75Z"
            fill="currentColor"
          />
          <path
            d="M1.75,11C1.33579,11,1,11.3358,1,11.75C1,12.1642,1.33579,12.5,1.75,12.5L2.25,12.5C2.6642099999999997,12.5,3,12.1642,3,11.75C3,11.3358,2.6642099999999997,11,2.25,11L1.75,11Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
