import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function DependencyAddOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-dependency-add-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_3690_08533">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_3690_08533)">
          <path
            d="M0,9.25L0,2.75C0,1.783502,0.783502,1,1.75,1L10.25,1C11.2165,1,12,1.783502,12,2.75L12,5.75C12,6.16421,11.6642,6.5,11.25,6.5C10.8358,6.5,10.5,6.16421,10.5,5.75L10.5,2.75Q10.5,2.5,10.25,2.5L1.75,2.5Q1.64645,2.5,1.57324,2.57324Q1.50002,2.64647,1.5,2.75L1.5,9.25Q1.5,9.35355,1.57324,9.42676Q1.64647,9.49998,1.75,9.5L2.25,9.5C2.66421,9.5,3,9.83579,3,10.25C3,10.66421,2.66421,11,2.25,11L1.75,11C0.783502,11,0,10.2165,0,9.25Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M4,10.25L4,13.25C4,14.2165,4.783502,15,5.75,15L14.25,15C15.2165,15,16,14.2165,16,13.25L16,6.75C16,5.783502,15.2165,5,14.25,5L13.75,5C13.33579,5,13,5.335786,13,5.75C13,6.16421,13.33579,6.5,13.75,6.5L14.25,6.5Q14.5,6.5,14.5,6.75L14.5,13.25Q14.5,13.5,14.25,13.5L5.75,13.5Q5.64645,13.5,5.57324,13.42676Q5.50002,13.35353,5.5,13.25L5.5,10.25C5.5,9.83579,5.16421,9.5,4.75,9.5C4.335786,9.5,4,9.83579,4,10.25"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M7.25,5.5Q7.25,5.4261315,7.26441,5.353682Q7.27882,5.281233,7.3070900000000005,5.212987Q7.33536,5.144742,7.3764,5.083322Q7.41744,5.021903,7.46967,4.96967Q7.5219000000000005,4.917437,7.5833200000000005,4.876398Q7.6447400000000005,4.835359,7.71299,4.80709Q7.78123,4.778822,7.853680000000001,4.764411Q7.926130000000001,4.75,8,4.75Q8.07387,4.75,8.14632,4.764411Q8.21877,4.778822,8.28701,4.80709Q8.35526,4.835359,8.41668,4.876398Q8.4781,4.917437,8.53033,4.96967Q8.58256,5.021903,8.6236,5.083322Q8.66464,5.144742,8.69291,5.212987Q8.72118,5.281233,8.73559,5.353682Q8.75,5.4261315,8.75,5.5L8.75,7.25L10.5,7.25Q10.57387,7.25,10.64632,7.26441Q10.71877,7.27882,10.78701,7.3070900000000005Q10.855260000000001,7.33536,10.91668,7.3764Q10.978100000000001,7.41744,11.03033,7.46967Q11.08256,7.5219000000000005,11.1236,7.5833200000000005Q11.16464,7.6447400000000005,11.192910000000001,7.71299Q11.22118,7.78123,11.23559,7.853680000000001Q11.25,7.926130000000001,11.25,8Q11.25,8.07387,11.23559,8.14632Q11.22118,8.21877,11.192910000000001,8.28701Q11.16464,8.35526,11.1236,8.41668Q11.08256,8.4781,11.03033,8.53033Q10.978100000000001,8.58256,10.91668,8.6236Q10.855260000000001,8.66464,10.78701,8.69291Q10.71877,8.72118,10.64632,8.73559Q10.57387,8.75,10.5,8.75L8.75,8.75L8.75,10.5Q8.75,10.57387,8.73559,10.64632Q8.72118,10.71877,8.69291,10.78701Q8.66464,10.855260000000001,8.6236,10.91668Q8.58256,10.978100000000001,8.53033,11.03033Q8.4781,11.08256,8.41668,11.1236Q8.35526,11.16464,8.28701,11.192910000000001Q8.21877,11.22118,8.14632,11.23559Q8.07387,11.25,8,11.25Q7.926130000000001,11.25,7.853680000000001,11.23559Q7.78123,11.22118,7.71299,11.192910000000001Q7.6447400000000005,11.16464,7.5833200000000005,11.1236Q7.5219000000000005,11.08256,7.46967,11.03033Q7.41744,10.978100000000001,7.3764,10.91668Q7.33536,10.855260000000001,7.3070900000000005,10.78701Q7.27882,10.71877,7.26441,10.64632Q7.25,10.57387,7.25,10.5L7.25,8.75L5.5,8.75Q5.4261315,8.75,5.353682,8.73559Q5.281233,8.72118,5.212987,8.69291Q5.144742,8.66464,5.083322,8.6236Q5.021903,8.58256,4.96967,8.53033Q4.917437,8.4781,4.876398,8.41668Q4.835359,8.35526,4.80709,8.28701Q4.778822,8.21877,4.764411,8.14632Q4.75,8.07387,4.75,8Q4.75,7.926130000000001,4.764411,7.853680000000001Q4.778822,7.78123,4.80709,7.71299Q4.835359,7.6447400000000005,4.876398,7.5833200000000005Q4.917437,7.5219000000000005,4.96967,7.46967Q5.021903,7.41744,5.083322,7.3764Q5.144742,7.33536,5.212987,7.3070900000000005Q5.281233,7.27882,5.353682,7.26441Q5.4261315,7.25,5.5,7.25L7.25,7.25L7.25,5.5Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
