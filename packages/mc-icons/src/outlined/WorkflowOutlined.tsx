import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function WorkflowOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-workflow-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_03850">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_03850)">
          <path
            d="M0,1.75C0,0.783502,0.783502,0,1.75,0L5.25,0C6.2165,0,7,0.783502,7,1.75L7,5.25C7,6.2165,6.2165,7,5.25,7L4,7L4,11C4,11.5523,4.44772,12,5,12L9,12L9,10.75C9,9.7835,9.7835,9,10.75,9L14.25,9C15.2165,9,16,9.7835,16,10.75L16,14.25C16,15.2165,15.2165,16,14.25,16L10.75,16C9.7835,16,9,15.2165,9,14.25L9,13.5L5,13.5C3.61929,13.5,2.5,12.3807,2.5,11L2.5,7L1.75,7C0.783502,7,0,6.2165,0,5.25L0,1.75ZM1.75,1.5C1.61193,1.5,1.5,1.61193,1.5,1.75L1.5,5.25C1.5,5.38807,1.61193,5.5,1.75,5.5L5.25,5.5C5.38807,5.5,5.5,5.38807,5.5,5.25L5.5,1.75C5.5,1.61193,5.38807,1.5,5.25,1.5L1.75,1.5ZM10.75,10.5C10.6119,10.5,10.5,10.6119,10.5,10.75L10.5,14.25C10.5,14.3881,10.6119,14.5,10.75,14.5L14.25,14.5C14.3881,14.5,14.5,14.3881,14.5,14.25L14.5,10.75C14.5,10.6119,14.3881,10.5,14.25,10.5L10.75,10.5Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
