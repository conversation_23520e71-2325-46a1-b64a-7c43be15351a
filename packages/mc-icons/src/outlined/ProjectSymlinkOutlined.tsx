import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function ProjectSymlinkOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-project-symlink-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1860_03831">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1860_03831)">
          <path
            d="M0,1.75C0,0.783502,0.783502,0,1.75,0L14.25,0C15.2165,0,16,0.783502,16,1.75L16,14.25C16,15.2165,15.2165,16,14.25,16L5.75,16C5.33579,16,5,15.6642,5,15.25C5,14.8358,5.33579,14.5,5.75,14.5L14.25,14.5C14.3881,14.5,14.5,14.3881,14.5,14.25L14.5,6.5L1.5,6.5L1.5,8.25C1.5,8.66421,1.16421,9,0.75,9C0.335786,9,0,8.66421,0,8.25L0,1.75ZM1.5,5L5,5L5,1.5L1.75,1.5C1.61193,1.5,1.5,1.61193,1.5,1.75L1.5,5ZM6.5,5L14.5,5L14.5,1.75C14.5,1.61193,14.3881,1.5,14.25,1.5L6.5,1.5L6.5,5Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M1.5,13.737402543640137C1.5,12.489902543640136,2.51488,11.480702543640136,3.76245,11.487602543640136L3.99997,11.489002543640137L3.99997,13.426702543640136C3.99997,13.645002543640135,4.26012,13.758402543640138,4.42009,13.609902543640136L7.30268,10.933202543640137C7.4092,10.834302543640137,7.4092,10.665702543640137,7.30268,10.566802543640136L4.42008,7.890100043640136C4.26012,7.741559943640136,3.99997,7.855010043640137,3.99997,8.073300543640137L3.99997,9.988942543640137L3.77075,9.987672543640137C1.69169,9.976162543640136,0,11.658102543640137,0,13.737402543640137L0,14.750002543640136C0,15.164202543640137,0.335786,15.500002543640136,0.75,15.500002543640136C1.16421,15.500002543640136,1.5,15.164202543640137,1.5,14.750002543640136L1.5,13.737402543640137Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
