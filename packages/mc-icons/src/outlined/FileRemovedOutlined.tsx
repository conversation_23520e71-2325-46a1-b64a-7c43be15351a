import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function FileRemovedOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-file-removed-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_04071">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_04071)">
          <path
            d="M3.75,1.5C3.61193,1.5,3.5,1.61193,3.5,1.75L3.5,14.25C3.5,14.3881,3.61193,14.5,3.75,14.5L13.25,14.5C13.3881,14.5,13.5,14.3881,13.5,14.25L13.5,4.66421C13.5,4.59791,13.4737,4.53432,13.4268,4.48744L10.5126,1.57322C10.4657,1.52634,10.4021,1.5,10.3358,1.5L3.75,1.5ZM2,1.75C2,0.783502,2.7835,0,3.75,0L10.3358,0C10.7999,0,11.245,0.184374,11.5732,0.512563L14.4874,3.42678C14.8156,3.75497,15,4.20008,15,4.66421L15,14.25C15,15.2165,14.2165,16,13.25,16L3.75,16C2.7835,16,2,15.2165,2,14.25L2,1.75ZM8.25,7.5L10.4922,7.5C10.9064,7.5,11.2422,7.83578,11.2422,8.25C11.2422,8.66421,10.9064,9,10.4922,9L8.25254,9L5.997809999999999,9.01498C5.58361,9.01773,5.2456,8.68418,5.24284,8.26998C5.24009,7.85577,5.57364,7.51776,5.98785,7.51501L8.25,7.5Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
