import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function MegaphoneOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-megaphone-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1860_03906">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1860_03906)">
          <path
            d="M15.5712,0.572256C15.833,0.696353,16,0.960216,16,1.25001L14.7769,0.668065L14.7769,0.668065C14.778,0.667178,14.7776,0.667521,14.7769,0.668065L14.7621,0.679557C14.7472,0.690943,14.7217,0.710013,14.6857,0.735689C14.6135,0.787045,14.4989,0.864803,14.3414,0.960366C14.0264,1.15147,13.5398,1.41379,12.8777,1.67865C11.5559,2.2073799999999997,9.52654,2.74947,6.75411,2.75001C6.50517,2.5917,6.20972,2.5,5.89286,2.5L4,2.5C1.79086,2.5,0,4.29086,0,6.5C0,8.1791,1.03458,9.61655,2.50107,10.2097C2.50036,10.223,2.5,10.2365,2.5,10.25C2.5,12.5693,2.98616,14.1807,3.31256,15.0006C3.57478,15.6593,4.20971,16,4.83032,16L6.02682,16C6.7117,16,7.25503,15.6115,7.57322,15.1427C7.88953,14.6767,8.04127,14.0532,7.88312,13.4466C7.6832,12.6798,7.50121,11.6123,7.50001,10.2635C9.89372,10.3501,11.6774,10.8413,12.8777,11.3214C13.5398,11.5862,14.0264,11.8485,14.3414,12.0397C14.4989,12.1352,14.6135,12.213,14.6857,12.2643C14.7217,12.29,14.7472,12.3091,14.7621,12.3205L14.7765,12.3316L14.7775,12.3325C15.0016,12.5143,15.3102,12.5514,15.5712,12.4278C15.833,12.3037,16,12.0398,16,11.75L16,1.25001L14.7769,0.668065L14.7769,0.668065C15.001,0.485831,15.31,0.448491,15.5712,0.572256ZM4.00209,10.5C4.03507,12.4687,4.45144,13.8059,4.70618,14.4458C4.71036,14.4563,4.71689,14.4658,4.73305,14.4759C4.75155,14.4875,4.78444,14.5,4.83032,14.5L6.02682,14.5C6.10976,14.5,6.22857,14.4529,6.33212,14.3003C6.43757,14.145,6.4672,13.9614,6.43164,13.825C6.21387,12.9898,6.02248,11.8739,6.00184,10.4964C5.96582,10.4988,5.92948,10.5,5.89286,10.5L4.00209,10.5ZM7.5,8.76255C10.1013,8.8502,12.0728,9.38384,13.4348,9.92865C13.8457,10.093,14.2006,10.2582,14.5,10.4119L14.5,2.58807C14.2006,2.74187,13.8457,2.907,13.4348,3.07137C12.0728,3.61618,10.1013,4.14982,7.5,4.23748L7.5,8.76255ZM14.7765,0.668385L14.7765,0.668385L14.7765,0.668385ZM1.5,6.5C1.5,5.11929,2.61929,4,4,4L5.89286,4C5.95203,4,6,4.047969999999999,6,4.107139999999999L6,8.89286C6,8.95203,5.95203,9,5.89286,9L4,9C2.61929,9,1.5,7.88071,1.5,6.5Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
