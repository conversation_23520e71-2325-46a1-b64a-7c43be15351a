import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function UndoOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-undo-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4042_32295">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4042_32295)">
          <path
            d="M13.6569,13.6569C15.1571,12.1566,16,10.1217,16,8C16,5.87827,15.1571,3.84344,13.6569,2.34315C12.1566,0.842855,10.1217,0,8,0C5.87827,0,3.84344,0.842855,2.34315,2.34315C0.842855,3.84344,0,5.87827,0,8C0,10.1217,0.842855,12.1566,2.34315,13.6569C3.84344,15.1571,5.87827,16,8,16C10.1217,16,12.1566,15.1571,13.6569,13.6569ZM12.5962,12.5962C13.8152,11.3772,14.5,9.72391,14.5,8C14.5,6.27609,13.8152,4.62279,12.5962,3.40381C11.3772,2.18482,9.72391,1.5,8,1.5C6.27609,1.5,4.62279,2.18482,3.40381,3.40381C2.18482,4.62279,1.5,6.27609,1.5,8C1.5,9.72391,2.18482,11.3772,3.40381,12.5962C4.62279,13.8152,6.27609,14.5,8,14.5C9.72391,14.5,11.3772,13.8152,12.5962,12.5962Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M8.75,10L6,10Q5.9261315,10,5.853682,10.01441Q5.781233,10.02882,5.712987,10.05709Q5.644742,10.08536,5.583322,10.1264Q5.521903,10.16744,5.46967,10.21967Q5.417437,10.271899999999999,5.376398,10.33332Q5.335359,10.394739999999999,5.30709,10.46299Q5.278822,10.53123,5.264411,10.60368Q5.25,10.67613,5.25,10.75Q5.25,10.82387,5.264411,10.89632Q5.278822,10.96877,5.30709,11.03701Q5.335359,11.105260000000001,5.376398,11.16668Q5.417437,11.228100000000001,5.46967,11.28033Q5.521903,11.33256,5.583322,11.3736Q5.644742,11.41464,5.712987,11.442910000000001Q5.781233,11.47118,5.853682,11.48559Q5.9261315,11.5,6,11.5L8.75,11.5Q9.99264,11.5,10.87132,10.62132Q11.75,9.74264,11.75,8.5Q11.75,7.25736,10.87132,6.37868Q9.99264,5.5,8.75,5.5L7.36364,5.5Q7.28977,5.5,7.21732,5.514411Q7.14487,5.528822,7.07662,5.55709Q7.00838,5.585359,6.946959,5.626398Q6.885539,5.667437,6.833306,5.71967Q6.781073,5.771903,6.740034,5.833322Q6.698995,5.894742,6.670727,5.962987Q6.6424579999999995,6.031233,6.6280470000000005,6.103682Q6.613636,6.1761315,6.613636,6.25Q6.613636,6.3238685,6.6280470000000005,6.396318Q6.6424579999999995,6.468767,6.670727,6.537013Q6.698995,6.605258,6.740034,6.666678Q6.781073,6.728097,6.833306,6.78033Q6.885539,6.832563,6.946959,6.873602Q7.00838,6.914641,7.07662,6.94291Q7.14487,6.971178,7.21732,6.985589Q7.28977,7,7.36364,7L8.75,7Q9.37132,7,9.81066,7.43934Q10.25,7.87868,10.25,8.5Q10.25,9.12132,9.81066,9.56066Q9.37132,10,8.75,10Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M7.017822265625,4.5Q7.017822265625,4.4261315,7.032233265625,4.353682Q7.046644265625,4.281233,7.074912265625,4.212987Q7.103181265625,4.144742,7.144220265625,4.083322Q7.185259265625,4.021903,7.237492265625,3.96967Q7.289725265625,3.917437,7.351144265625,3.876398Q7.412564265625,3.835359,7.480809265625,3.80709Q7.549055265625,3.778822,7.621504265625,3.764411Q7.693953765625,3.75,7.767822265625,3.75Q7.841690765625,3.75,7.914140265625,3.764411Q7.986589265625,3.778822,8.054835265625,3.80709Q8.123080265625,3.835359,8.184500265625,3.876398Q8.245919265625,3.917437,8.298152265625,3.96967Q8.350385265625,4.021903,8.391424265625,4.083322Q8.432463265625,4.144742,8.460732265625,4.212987Q8.489000265625,4.281233,8.503411265625001,4.353682Q8.517822265625,4.4261315,8.517822265625,4.5L8.517822265625,6.25L10.267822265625,6.25Q10.341692265625,6.25,10.414142265625,6.26441Q10.486592265625,6.27882,10.554832265625,6.3070900000000005Q10.623082265625,6.33536,10.684502265625,6.3764Q10.745922265625,6.41744,10.798152265625,6.46967Q10.850382265625,6.5219000000000005,10.891422265625,6.5833200000000005Q10.932462265625,6.6447400000000005,10.960732265625,6.71299Q10.989002265625,6.78123,11.003412265625,6.853680000000001Q11.017822265625,6.926130000000001,11.017822265625,7Q11.017822265625,7.073869999999999,11.003412265625,7.146319999999999Q10.989002265625,7.21877,10.960732265625,7.28701Q10.932462265625,7.3552599999999995,10.891422265625,7.4166799999999995Q10.850382265625,7.4780999999999995,10.798152265625,7.53033Q10.745922265625,7.58256,10.684502265625,7.6236Q10.623082265625,7.66464,10.554832265625,7.6929099999999995Q10.486592265625,7.72118,10.414142265625,7.73559Q10.341692265625,7.75,10.267822265625,7.75L8.017822265625,7.75Q7.603609265625,7.75,7.310716265625,7.45711Q7.017822265625,7.164210000000001,7.017822265625,6.75L7.017822265625,4.5Z"
            fillRule="evenodd"
            fill="currentColor"
            transform="matrix(0.7071067690849304,0.7071067690849304,-0.7071067690849304,0.7071067690849304,5.457123021435109,-4.174660165954265)"
          />
        </g>
      </svg>
    </span>
  );
}
