import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function PackageRollbackOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-package-rollback-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_2183_04599">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_2183_04599)">
          <path
            d="M6.12199,0.392200859375C6.66497,0.077274059375,7.33503,0.077274159375,7.87801,0.392200859375L13.128,3.437195859375C13.6678,3.750255859375,14,4.327025859375,14,4.951005859375L14,6.249965859375C14,6.664175859375,13.6642,6.999965859375,13.25,6.999965859375C12.8358,6.999965859375,12.5,6.664175859375,12.5,6.249965859375L12.5,5.677015859375L7.75,8.432015859375L7.75,14.500005859375L7.75,14.816005859375C7.75,15.586705859375,6.91495,16.067705859375,6.24828,15.681005859375L0.871994,12.562805859375C0.332237,12.249705859375,0,11.672905859375,0,11.049005859375L0,4.951005859375C0,4.327025859375,0.332236,3.750255859375,0.871993,3.437195859375L6.12199,0.392200859375ZM7.12543,1.689745859375L11.7552,4.374985859375L7.00001,7.132975859375L2.24485,4.374985859375L6.87457,1.689745859375C6.95214,1.644755859375,7.04786,1.644755859375,7.12543,1.689745859375ZM1.5,11.049005859375L1.5,5.677015859375L6.25,8.432015859375L6.25,13.948005859375L1.62457,11.265205859375C1.54746,11.220505859375,1.5,11.138105859375,1.5,11.049005859375Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M12,8.124282000000001L12,13.16572C12,13.391300000000001,12.275195,13.5015,12.430898,13.338280000000001L14.8354,10.81756C14.92753,10.72097,14.92753,10.56903,14.8354,10.47244L12.430898,7.9517240000000005C12.275195,7.788496,12,7.8987,12,8.124282000000001Z"
            fill="currentColor"
          />
          <path
            d="M9,13C9,11.34315,10.34315,10,12,10L12,11.5Q11.37868,11.5,10.93934,11.93934Q10.5,12.37868,10.5,13Q10.5,13.62132,10.93934,14.06066Q11.37868,14.5,12,14.5L12.25,14.5C12.66421,14.5,13,14.83579,13,15.25C13,15.66421,12.66421,16,12.25,16L12,16C10.34315,16,9,14.65685,9,13Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
