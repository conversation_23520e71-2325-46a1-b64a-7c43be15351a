import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function FoldOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-fold-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_05016">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_05016)">
          <path
            d="M10.89640118560791,2.50004L8.75000118560791,2.50004L8.75000118560791,0.75C8.75000118560791,0.335786,8.414211185607911,0,8.00000118560791,0C7.58579118560791,0,7.2500011856079105,0.335786,7.2500011856079105,0.75L7.2500011856079105,2.50004L5.10355018560791,2.50004C4.880829785607911,2.50004,4.76927998560791,2.76932,4.92678018560791,2.92681L7.82322118560791,5.82326C7.92085118560791,5.92089,8.07914118560791,5.92089,8.17677118560791,5.82326L11.07320118560791,2.92681C11.23070118560791,2.76932,11.11920118560791,2.50004,10.89640118560791,2.50004Z"
            fill="currentColor"
          />
          <path
            d="M8.749998324584961,14.749997532348633C8.749998324584961,15.164197532348632,8.414208324584962,15.499997532348633,7.999998324584961,15.499997532348633C7.585788324584961,15.499997532348633,7.249998324584961,15.164197532348632,7.249998324584961,14.749997532348633L7.249998324584961,12.999997532348633L5.103550324584961,12.999997532348633C4.880829824584961,12.999997532348633,4.769279924584961,12.730697532348632,4.926770224584961,12.573197532348633L7.8232183245849605,9.676739732348633C7.920848324584961,9.579110132348633,8.07913832458496,9.579110132348633,8.17676832458496,9.676739732348633L11.073198324584961,12.573197532348633C11.23069832458496,12.730697532348632,11.11919832458496,12.999997532348633,10.896398324584961,12.999997532348633L8.749998324584961,12.999997532348633L8.749998324584961,14.749997532348633Z"
            fill="currentColor"
          />
          <path
            d="M2.25,8.5C2.6642099999999997,8.5,3,8.16421,3,7.75C3,7.33579,2.6642099999999997,7,2.25,7L1.75,7C1.33579,7,1,7.33579,1,7.75C1,8.16421,1.33579,8.5,1.75,8.5L2.25,8.5Z"
            fill="currentColor"
          />
          <path
            d="M6,7.75C6,8.16421,5.66421,8.5,5.25,8.5L4.75,8.5C4.33579,8.5,4,8.16421,4,7.75C4,7.33579,4.33579,7,4.75,7L5.25,7C5.66421,7,6,7.33579,6,7.75Z"
            fill="currentColor"
          />
          <path
            d="M8.25,8.5C8.66421,8.5,9,8.16421,9,7.75C9,7.33579,8.66421,7,8.25,7L7.75,7C7.33579,7,7,7.33579,7,7.75C7,8.16421,7.33579,8.5,7.75,8.5L8.25,8.5Z"
            fill="currentColor"
          />
          <path
            d="M12,7.75C12,8.16421,11.6642,8.5,11.25,8.5L10.75,8.5C10.3358,8.5,10,8.16421,10,7.75C10,7.33579,10.3358,7,10.75,7L11.25,7C11.6642,7,12,7.33579,12,7.75Z"
            fill="currentColor"
          />
          <path
            d="M14.25,8.5C14.6642,8.5,15,8.16421,15,7.75C15,7.33579,14.6642,7,14.25,7L13.75,7C13.3358,7,13,7.33579,13,7.75C13,8.16421,13.3358,8.5,13.75,8.5L14.25,8.5Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
