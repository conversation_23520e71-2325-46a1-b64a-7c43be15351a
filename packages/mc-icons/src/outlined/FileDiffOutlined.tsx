import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function FileDiffOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-file-diff-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_04092">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_04092)">
          <path
            d="M2.75,1.5C2.61193,1.5,2.5,1.61193,2.5,1.75L2.5,14.25C2.5,14.3881,2.61193,14.5,2.75,14.5L13.25,14.5C13.3881,14.5,13.5,14.3881,13.5,14.25L13.5,4.66421C13.5,4.59791,13.4737,4.53432,13.4268,4.48744L10.5126,1.57322C10.4657,1.52634,10.4021,1.5,10.3358,1.5L2.75,1.5ZM1,1.75C1,0.783502,1.7835,0,2.75,0L10.3358,0C10.7999,0,11.245,0.184374,11.5732,0.512563L14.4874,3.42678C14.8156,3.75497,15,4.20008,15,4.66421L15,14.25C15,15.2165,14.2165,16,13.25,16L2.75,16C1.7835,16,1,15.2165,1,14.25L1,1.75ZM8,3.25C8.41421,3.25,8.75,3.58579,8.75,4L8.75,5.5L10.25,5.5C10.6642,5.5,11,5.83579,11,6.25C11,6.66421,10.6642,7,10.25,7L8.75,7L8.75,8.5C8.75,8.91421,8.41421,9.25,8,9.25C7.58579,9.25,7.25,8.91421,7.25,8.5L7.25,7L5.75,7C5.33579,7,5,6.66421,5,6.25C5,5.83579,5.33579,5.5,5.75,5.5L7.25,5.5L7.25,4C7.25,3.58579,7.58579,3.25,8,3.25ZM5,11.25C5,10.8358,5.33579,10.5,5.75,10.5L10.25,10.5C10.6642,10.5,11,10.8358,11,11.25C11,11.6642,10.6642,12,10.25,12L5.75,12C5.33579,12,5,11.6642,5,11.25Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
