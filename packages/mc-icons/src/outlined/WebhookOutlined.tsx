import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function WebhookOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-webhook-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04772">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04772)">
          <path
            d="M5.500028168106079,4.25C5.500028168106079,3.00736,6.507388168106079,2,7.750028168106079,2C8.992668168106078,2,9.999998168106078,3.00736,9.999998168106078,4.25C9.999998168106078,4.66421,10.335798168106079,5,10.749998168106078,5C11.16419816810608,5,11.49999816810608,4.66421,11.49999816810608,4.25C11.49999816810608,2.1789300000000003,9.82108816810608,0.5,7.750028168106079,0.5C5.678958168106079,0.5,4.000028168106079,2.1789300000000003,4.000028168106079,4.25C4.000028168106079,5.41315,4.529738168106079,6.45181,5.359208168106079,7.13903L3.088259968106079,11.3971C2.893340168106079,11.7625,3.0315999681060792,12.2168,3.397080168106079,12.4118C3.762570168106079,12.6067,4.216868168106079,12.4684,4.411788168106079,12.1029L6.999748168106079,7.25052C7.1945481681060794,6.88526,7.056588168106079,6.43124,6.6914981681060794,6.23612C5.980848168106079,5.85632,5.500028168106079,5.10873,5.500028168106079,4.25Z"
            fill="currentColor"
          />
          <path
            d="M7.364120499176026,3.606870260757446C7.719299499176025,3.393760260757446,8.179996499176026,3.508929970757446,8.393106499176024,3.8641202607574465L11.002296499176026,8.212776260757447C11.393096499176025,8.074936260757447,11.813196499176026,7.999986260757447,12.249996499176024,7.999986260757447C14.321096499176026,7.999986260757447,15.999996499176026,9.678916260757447,15.999996499176026,11.749996260757447C15.999996499176026,13.821076260757446,14.321096499176026,15.499976260757446,12.249996499176024,15.499976260757446C11.568196499176025,15.499976260757446,10.926796499176024,15.317376260757447,10.374396499176026,14.997776260757446C10.015896499176026,14.790376260757446,9.893376499176025,14.331576260757446,10.100796499176026,13.973076260757447C10.308196499176026,13.614576260757445,10.766996499176026,13.491996260757446,11.125496499176025,13.699376260757447C11.455696499176025,13.890376260757446,11.838896499176025,13.999976260757446,12.249996499176024,13.999976260757446C13.492596499176026,13.999976260757446,14.499996499176024,12.992596260757447,14.499996499176024,11.749996260757447C14.499996499176024,10.507396260757446,13.492596499176026,9.499986260757446,12.249996499176024,9.499986260757446C11.825396499176026,9.499986260757446,11.430296499176025,9.616906260757446,11.092796499176025,9.819876260757447C10.922196499176025,9.922436260757447,10.717796499176025,9.952996260757446,10.524696499176025,9.904816260757446C10.331596499176026,9.856636260757448,10.165596499176026,9.733666260757445,10.063196499176026,9.562996260757446L7.106870499176026,4.635856260757446C6.893760499176025,4.280680260757446,7.008930209176025,3.8199802607574465,7.364120499176026,3.606870260757446Z"
            fill="currentColor"
          />
          <path
            d="M2.8992,8.775790460632324C3.10661,9.134340460632323,2.98409,9.593137460632324,2.62554,9.800537460632324C1.95106,10.190697460632324,1.5,10.918097460632325,1.5,11.749997460632324C1.5,12.992597460632325,2.50736,13.999997460632324,3.75,13.999997460632324C4.99264,13.999997460632324,6,12.992597460632325,6,11.749997460632324C6,11.335797460632325,6.33579,10.999997460632324,6.75,10.999997460632324L12.25,10.999997460632324C12.6642,10.999997460632324,13,11.335797460632325,13,11.749997460632324C13,12.164197460632325,12.6642,12.499997460632324,12.25,12.499997460632324L7.42499,12.499997460632324C7.07753,14.211697460632324,5.56422,15.499997460632324,3.75,15.499997460632324C1.67893,15.499997460632324,0,13.821097460632323,0,11.749997460632324C0,10.360997460632325,0.755535,9.149399460632324,1.87446,8.502140460632324C2.233,8.294730460632325,2.6918,8.417249660632324,2.8992,8.775790460632324Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
