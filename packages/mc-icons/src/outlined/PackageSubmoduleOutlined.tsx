import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function PackageSubmoduleOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-package-submodule-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4156_36928">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4156_36928)">
          <path
            d="M5.000007152557373,2.75L2.750007152557373,2.75L2.750007152557373,5C2.750007152557373,5.41421,2.414217152557373,5.75,2.000007152557373,5.75C1.585793152557373,5.75,1.250007152557373,5.41421,1.250007152557373,5L1.250007152557373,2.25Q1.250007152557373,1.8357860000000001,1.5429001525573731,1.542893Q1.8357941525573729,1.25,2.250007152557373,1.25L5.000007152557373,1.25C5.414217152557373,1.25,5.750007152557373,1.585786,5.750007152557373,2C5.750007152557373,2.4142099999999997,5.414217152557373,2.75,5.000007152557373,2.75ZM13.750007152557373,1.25L11.000007152557373,1.25C10.585797152557372,1.25,10.250007152557373,1.585786,10.250007152557373,2C10.250007152557373,2.4142099999999997,10.585797152557372,2.75,11.000007152557373,2.75L13.250007152557373,2.75L13.250007152557373,5C13.250007152557373,5.41421,13.585807152557374,5.75,14.000007152557373,5.75C14.414207152557372,5.75,14.750007152557373,5.41421,14.750007152557373,5L14.750007152557373,2.25Q14.750007152557373,1.8357869999999998,14.457107152557374,1.542893Q14.164207152557372,1.25,13.750007152557373,1.25ZM7.173527152557373,3.86295L4.530937152557373,5.38854Q4.2843671525573725,5.53089,4.142107152557373,5.7775Q3.999827152557373,6.02411,4.000007152557373,6.30865L4.000007152557373,9.69989Q3.999827152557373,9.9846,4.142077152557373,10.23114Q4.284347152557373,10.47784,4.530977152557373,10.62019L7.469047152557373,12.3158Q7.715427152557373,12.4581,8.000007152557373,12.4581Q8.284587152557373,12.4581,8.530897152557372,12.3159L11.469007152557372,10.62018Q11.715507152557374,10.47793,11.857707152557373,10.23157Q12.000007152557373,9.9852,12.000007152557373,9.70059L12.000007152557373,6.30832Q12.000207152557373,6.02355,11.857907152557374,5.77695Q11.715607152557373,5.5303,11.469007152557372,5.38798L8.530937152557373,3.69231Q8.284527152557374,3.55005,8.000007152557373,3.55005Q7.715487152557373,3.55005,7.469077152557373,3.69231L7.173527152557373,3.86295ZM5.985797152557373,6.07724L8.000547152557374,7.24056L10.014747152557373,6.07774L7.999997152557373,4.91442L5.985797152557373,6.07724ZM7.338607152557373,10.71214L7.338607152557373,8.38702L5.324397152557373,7.22377L5.324397152557373,9.54932L7.338607152557373,10.71214ZM8.662477152557372,10.7116L8.662477152557372,8.387039999999999L10.675607152557372,7.22477L10.675607152557372,9.54932L8.662477152557372,10.7116ZM2.000007152557373,10.25C1.585793152557373,10.25,1.250007152557373,10.58579,1.250007152557373,11L1.250007152557373,13.75Q1.250007152557373,14.1642,1.5429001525573731,14.4571Q1.8357941525573729,14.75,2.250007152557373,14.75L5.000007152557373,14.75C5.414217152557373,14.75,5.750007152557373,14.4142,5.750007152557373,14C5.750007152557373,13.5858,5.414217152557373,13.25,5.000007152557373,13.25L2.750007152557373,13.25L2.750007152557373,11C2.750007152557373,10.58579,2.414217152557373,10.25,2.000007152557373,10.25ZM14.000007152557373,10.25C13.585807152557374,10.25,13.250007152557373,10.58579,13.250007152557373,11L13.250007152557373,13.25L11.000007152557373,13.25C10.585797152557372,13.25,10.250007152557373,13.5858,10.250007152557373,14C10.250007152557373,14.4142,10.585797152557372,14.75,11.000007152557373,14.75L13.750007152557373,14.75Q14.164207152557372,14.75,14.457107152557374,14.4571Q14.750007152557373,14.1642,14.750007152557373,13.75L14.750007152557373,11C14.750007152557373,10.58579,14.414207152557372,10.25,14.000007152557373,10.25Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
