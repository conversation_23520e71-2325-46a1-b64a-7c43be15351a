import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function ArrowLeftOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-arrow-left-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_05204">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_05204)">
          <path
            d="M7.780332384185791,12.530302384185791C7.487442384185791,12.823202384185791,7.012562384185791,12.823202384185791,6.719672384185791,12.530302384185791L2.469670384185791,8.28033238418579C2.176779984185791,7.987442384185791,2.176779984185791,7.512562384185791,2.469670384185791,7.219672384185791L6.719672384185791,2.969670384185791C7.012562384185791,2.676779984185791,7.487442384185791,2.676779984185791,7.780332384185791,2.969670384185791C8.07322238418579,3.262560384185791,8.07322238418579,3.737440384185791,7.780332384185791,4.030332384185791L4.810662384185791,7.000002384185791L12.250002384185791,7.000002384185791C12.66420238418579,7.000002384185791,13.000002384185791,7.335792384185791,13.000002384185791,7.750002384185791C13.000002384185791,8.164212384185792,12.66420238418579,8.500002384185791,12.250002384185791,8.500002384185791L4.810662384185791,8.500002384185791L7.780332384185791,11.46970238418579C8.07322238418579,11.762602384185792,8.07322238418579,12.23740238418579,7.780332384185791,12.530302384185791Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
