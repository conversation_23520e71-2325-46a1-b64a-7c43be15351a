import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function PackageBoxOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-package-box-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4600_79603">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4600_79603)">
          <path
            d="M14.807300033378601,5.151610033378601L12.649100033378602,1.451178033378601Q12.438900033378602,1.090000033378601,12.019700033378601,1.090000033378601L3.982220033378601,1.090000033378601Q3.563890033378601,1.090000033378601,3.352930033378601,1.451446033378601L1.1952550333786012,5.150960033378601Q1.090000033378601,5.323090033378601,1.090000033378601,5.527510033378601L1.090000033378601,8.257970033378601L1.090000033378601,9.428010033378602L1.090000033378601,13.162300033378601C1.090000033378601,14.128800033378601,1.873502033378601,14.912300033378601,2.840000033378601,14.912300033378601L13.162300033378601,14.912300033378601C14.128800033378601,14.912300033378601,14.912300033378601,14.128800033378601,14.912300033378601,13.162300033378601L14.912300033378601,4.428620033378602L14.912300033378601,3.257720033378601L14.912300033378601,5.527510033378601Q14.912300033378601,5.323820033378601,14.807300033378601,5.151610033378601ZM5.886600033378601,4.7992400333786005L6.449870033378601,2.5461600333786008L4.400320033378601,2.5461600333786008L3.086250033378601,4.7992400333786005L5.886600033378601,4.7992400333786005ZM7.388120033378601,4.799280033378601L7.951400033378601,2.5461600333786008L8.050540033378601,2.5461600333786008L8.613810033378602,4.7995900333786015L7.388120033378601,4.799280033378601ZM11.601300033378601,2.5461600333786008L12.916000033378602,4.7992400333786005L10.115340033378601,4.7992400333786005L9.552070033378602,2.5461600333786008L11.601300033378601,2.5461600333786008ZM5.727030033378601,6.256160033378601L5.727030033378601,8.0009700333786Q5.727030033378601,8.302630033378602,5.940330033378601,8.515940033378602Q6.153640033378601,8.729250033378602,6.4553000333786015,8.729240033378602L9.5466500333786,8.729240033378602Q9.848470033378602,8.729250033378602,10.061690033378602,8.516020033378602Q10.274920033378601,8.302790033378601,10.274920033378601,8.0009700333786L10.274920033378601,6.256160033378601L13.4554000333786,6.256160033378601L13.4554000333786,13.2572000333786Q13.455000033378601,13.339800033378602,13.396300033378601,13.397900033378601Q13.337600033378601,13.455900033378601,13.254400033378602,13.4554000333786L2.747570033378601,13.4554000333786Q2.6648800333786014,13.4554000333786,2.605410033378601,13.396400033378601Q2.546540033378601,13.3380000333786,2.546540033378601,13.2578000333786L2.546540033378601,6.256160033378601L5.727030033378601,6.256160033378601ZM8.818410033378601,7.272700033378601L7.183570033378601,7.272700033378601L7.183570033378601,6.256160033378601L8.8187200333786,6.256160033378601L8.818410033378601,7.272700033378601Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
