import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function DependabotOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-dependabot-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_04139">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_04139)">
          <path
            d="M5.75,7.5C6.16421,7.5,6.5,7.83579,6.5,8.25L6.5,9.75C6.5,10.164200000000001,6.16421,10.5,5.75,10.5C5.33579,10.5,5,10.164200000000001,5,9.75L5,8.25C5,7.83579,5.33579,7.5,5.75,7.5Z"
            fill="currentColor"
          />
          <path
            d="M11,8.25C11,7.83579,10.6642,7.5,10.25,7.5C9.83579,7.5,9.5,7.83579,9.5,8.25L9.5,9.75C9.5,10.164200000000001,9.83579,10.5,10.25,10.5C10.6642,10.5,11,10.164200000000001,11,9.75L11,8.25Z"
            fill="currentColor"
          />
          <path
            d="M6.25,0C5.83579,0,5.5,0.335786,5.5,0.75C5.5,1.16421,5.83579,1.5,6.25,1.5L7.5,1.5L7.5,3.5L3.75,3.5C2.50736,3.5,1.5,4.50736,1.5,5.75L1.5,8L0.75,8C0.335786,8,0,8.33579,0,8.75C0,9.16421,0.335786,9.5,0.75,9.5L1.5,9.5L1.5,12.25C1.5,13.4926,2.50736,14.5,3.75,14.5L12.25,14.5C13.4926,14.5,14.5,13.4926,14.5,12.25L14.5,9.5L15.25,9.5C15.6642,9.5,16,9.16421,16,8.75C16,8.33579,15.6642,8,15.25,8L14.5,8L14.5,5.75C14.5,4.50736,13.4926,3.5,12.25,3.5L9,3.5L9,0.75C9,0.335786,8.66421,0,8.25,0L6.25,0ZM3,5.75C3,5.33579,3.33579,5,3.75,5L12.25,5C12.6642,5,13,5.33579,13,5.75L13,12.25C13,12.6642,12.6642,13,12.25,13L3.75,13C3.33579,13,3,12.6642,3,12.25L3,5.75Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
