import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function PaintbrushOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-paintbrush-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1860_03873">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1860_03873)">
          <path
            d="M14.692882519364357,4.871265124425888C13.666482519364356,6.283375124425888,12.170882519364357,7.844865124425888,10.915782519364358,9.116165124425889C9.840762519364358,10.205195124425888,8.827332519364356,10.964705124425889,7.933942519364357,11.430805124425888C8.110082519364358,12.571805124425888,7.758672519364357,13.778605124425889,6.879702519364357,14.657605124425888C6.448662519364357,15.088605124425888,5.814572519364357,15.348405124425888,5.223272519364357,15.518605124425887C4.608952519364357,15.695405124425887,3.928582519364357,15.805805124425888,3.308912519364357,15.876105124425887C2.685662519364357,15.946805124425888,2.103572519364357,15.979005124425887,1.678562519364357,15.993705124425889C1.465492519364357,16.001105124425887,1.290522519364357,16.004105124425887,1.1679505193643571,16.005305124425888C1.106629519364357,16.005905124425887,1.058330519364357,16.00600512442589,1.0248295193643568,16.00600512442589L0.9858765193643569,16.005905124425887L0.975121519364357,16.00580512442589L0.971997519364357,16.00580512442589L0.970654519364357,16.005705124425887C0.562443519364357,16.001605124425886,0.232292999364357,15.671405124425888,0.228119000364357,15.263205124425888L0.228106007364357,15.261805124425887L0.228079006364357,15.258705124425887L0.228007003364357,15.248005124425887L0.22788700461435699,15.209005124425888C0.227855995264357,15.175505124425888,0.227975994364357,15.127205124425888,0.228560000364357,15.065905124425887C0.229725999364357,14.943305124425889,0.232746009364357,14.768405124425888,0.240131019364357,14.555305124425889C0.254862019364357,14.130305124425888,0.287144019364357,13.548205124425888,0.357843519364357,12.925005124425889C0.428138519364357,12.305305124425889,0.5385775193643569,11.625005124425888,0.715375519364357,11.010705124425888C0.885552519364357,10.419405124425888,1.1453595193643569,9.785295124425888,1.576402519364357,9.354265124425888C2.452852519364357,8.477805124425888,3.655192519364357,8.125895124425888,4.793362519364357,8.298515124425888C5.253972519364357,7.3903151244258884,6.008652519364357,6.356505124425888,7.092892519364357,5.258225124425888C8.355352519364358,3.9794251244258882,9.918642519364356,2.494025124425888,11.342182519364357,1.483345124425888C12.047982519364357,0.9822461244258881,12.770582519364357,0.5603911244258881,13.438182519364357,0.35844312442588805C14.097282519364358,0.15906602442588808,14.907182519364357,0.12289212442588807,15.509082519364357,0.7263961244258881C16.115682519364356,1.332905124425888,16.05778251936436,2.146445124425888,15.848182519364357,2.795035124425888C15.634982519364357,3.455205124425888,15.201982519364357,4.170955124425888,14.692882519364357,4.871265124425888ZM6.212072519364357,8.822765124425889C6.449262519364357,8.970895124425889,6.673502519364357,9.148055124425888,6.879702519364357,9.354265124425888C7.082952519364357,9.557515124425889,7.258002519364357,9.778305124425888,7.404842519364357,10.011795124425888C7.876742519364357,9.747335124425888,8.417932519364356,9.367915124425888,9.012032519364357,8.850495124425889L7.353782519364357,7.192245124425888C6.844522519364357,7.795265124425888,6.471382519364357,8.344345124425889,6.212072519364357,8.822765124425889ZM13.479582519364357,3.989305124425888C12.589682519364358,5.213525124425888,11.290182519364357,6.594205124425888,10.094802519364357,7.811995124425888L8.376732519364356,6.093875124425888C9.581872519364357,4.884475124425888,10.974582519364358,3.583925124425888,12.210582519364356,2.706435124425888C12.871882519364357,2.236935124425888,13.437382519364357,1.925795124425888,13.872482519364358,1.794195124425888C14.316082519364357,1.660015124425888,14.423482519364358,1.762055124425888,14.447082519364358,1.7856751244258882L14.448482519364356,1.7870651244258882C14.462482519364357,1.800765124425888,14.561682519364357,1.8980651244258882,14.420882519364357,2.3338851244258882C14.281182519364357,2.766385124425888,13.959482519364357,3.329095124425888,13.479582519364357,3.989305124425888ZM3.139862519364357,14.385605124425888C2.620962519364357,14.444505124425888,2.1283525193643573,14.474805124425888,1.743612519364357,14.490305124425888C1.759092519364357,14.105505124425887,1.789422519364357,13.613005124425888,1.848292519364357,13.094105124425887C1.913712519364357,12.517405124425888,2.0122125193643567,11.928205124425888,2.156862519364357,11.425505124425888C2.308142519364357,10.899905124425889,2.482312519364357,10.569705124425887,2.637062519364357,10.414905124425887C3.5157425193643568,9.536235124425888,4.940362519364357,9.536235124425888,5.819042519364357,10.414905124425887C6.697722519364357,11.293605124425888,6.697722519364357,12.718205124425888,5.819042519364357,13.596905124425888C5.664292519364357,13.751605124425888,5.334072519364357,13.925805124425889,4.808432519364357,14.077105124425888C4.305792519364357,14.221705124425888,3.716572519364357,14.320205124425888,3.139862519364357,14.385605124425888Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
