import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function RollbackOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-rollback-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_2183_04527">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_2183_04527)">
          <path
            d="M6.28883,8.59785Q5.91468,8.5,5.5,8.5Q4.25736,8.5,3.37868,9.37868Q2.5,10.25736,2.5,11.5Q2.5,12.74264,3.37868,13.62132Q4.25736,14.5,5.5,14.5L5.5,16C3.01472,16,1,13.98528,1,11.5C1,9.01472,3.01472,7,5.5,7C5.72576,7,5.94763,7.0166244,6.16447,7.0487158L6.28883,8.59785Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M10.75,16L13.25,16C14.2165,16,15,15.2165,15,14.25L15,1.75C15,0.783502,14.2165,0,13.25,0L2.75,0C1.783502,0,1,0.783502,1,1.75L1,5.25C1,5.66421,1.335786,6,1.75,6C2.1642099999999997,6,2.5,5.66421,2.5,5.25L2.5,1.75Q2.5,1.64645,2.57322,1.57322Q2.6464499999999997,1.5,2.75,1.5L13.25,1.5Q13.5,1.5,13.5,1.75L13.5,14.25Q13.5,14.5,13.25,14.5L10.75,14.5C10.33579,14.5,10,14.8358,10,15.25C10,15.6642,10.33579,16,10.75,16"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M5,5.5L5,10.5C5,10.70601,5.235191,10.82361,5.4,10.7L8.73333,8.2C8.86667,8.1,8.86667,7.9,8.73333,7.8L5.4,5.3C5.235191,5.176393,5,5.293989,5,5.5Z"
            fill="currentColor"
          />
          <path
            d="M10.75,5Q10.75,4.9261315,10.764410999999999,4.853682Q10.778822,4.781233,10.80709,4.712987Q10.835359,4.644742,10.876398,4.583322Q10.917437,4.521903,10.96967,4.46967Q11.021903,4.417437,11.083322,4.376398Q11.144742,4.335359,11.212987,4.30709Q11.281233,4.278822,11.353682,4.264411Q11.4261315,4.25,11.5,4.25Q11.5738685,4.25,11.646318,4.264411Q11.718767,4.278822,11.787013,4.30709Q11.855258,4.335359,11.916678,4.376398Q11.978097,4.417437,12.03033,4.46967Q12.082563,4.521903,12.123602,4.583322Q12.164641,4.644742,12.19291,4.712987Q12.221178,4.781233,12.235589000000001,4.853682Q12.25,4.9261315,12.25,5L12.25,11Q12.25,11.07387,12.235589000000001,11.14632Q12.221178,11.21877,12.19291,11.28701Q12.164641,11.355260000000001,12.123602,11.41668Q12.082563,11.478100000000001,12.03033,11.53033Q11.978097,11.58256,11.916678,11.6236Q11.855258,11.66464,11.787013,11.692910000000001Q11.718767,11.72118,11.646318,11.73559Q11.5738685,11.75,11.5,11.75Q11.4261315,11.75,11.353682,11.73559Q11.281233,11.72118,11.212987,11.692910000000001Q11.144742,11.66464,11.083322,11.6236Q11.021903,11.58256,10.96967,11.53033Q10.917437,11.478100000000001,10.876398,11.41668Q10.835359,11.355260000000001,10.80709,11.28701Q10.778822,11.21877,10.764410999999999,11.14632Q10.75,11.07387,10.75,11L10.75,5Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M5.5,16.00001L6.2500029999999995,16L7.75,16C8.16421,16,8.5,15.66421,8.5,15.250001C8.5,14.835787,8.16421,14.500000507034,7.75,14.50000113249L6.2500029999999995,14.50000339746L5.5,14.5L5.5,16.00001Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
