import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function GitPullRequestClosedOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-git-pull-request-closed-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_04026">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_04026)">
          <path
            d="M10.719699795532227,1.2274797847442627C11.012599795532227,0.9345889847442627,11.487399795532227,0.9345889847442627,11.780304795532226,1.2274797847442627L12.750004795532227,2.1971517847442628L13.719704795532227,1.2274797847442627C14.012604795532226,0.9345889847442627,14.487404795532226,0.9345889847442627,14.780304795532228,1.2274797847442627C15.073204795532227,1.5203797847442626,15.073204795532227,1.9952497847442627,14.780304795532228,2.288141784744263L13.810704795532226,3.2578117847442627L14.780304795532228,4.2274817847442625C15.073204795532227,4.520371784744263,15.073204795532227,4.995251784744262,14.780304795532228,5.288141784744263C14.487404795532226,5.581041784744262,14.012604795532226,5.581041784744262,13.719704795532227,5.288141784744263L12.750004795532227,4.318471784744263L11.780304795532226,5.288141784744263C11.487399795532227,5.581041784744262,11.012599795532227,5.581041784744262,10.719699795532227,5.288141784744263C10.426799795532226,4.995251784744262,10.426799795532226,4.520371784744263,10.719699795532227,4.2274817847442625L11.689294795532227,3.2578117847442627L10.719699795532227,2.288141784744263C10.426799795532226,1.9952497847442627,10.426799795532226,1.5203797847442626,10.719699795532227,1.2274797847442627Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M12.75,6.5C12.3358,6.5,12,6.83579,12,7.25L12,10.628C11.1261,10.9369,10.5,11.770299999999999,10.5,12.75C10.5,13.9926,11.5074,15,12.75,15C13.9926,15,15,13.9926,15,12.75C15,11.770299999999999,14.373899999999999,10.9369,13.5,10.628L13.5,7.25C13.5,6.83579,13.164200000000001,6.5,12.75,6.5ZM12.75,12C12.3358,12,12,12.335799999999999,12,12.75C12,13.164200000000001,12.3358,13.5,12.75,13.5C13.164200000000001,13.5,13.5,13.164200000000001,13.5,12.75C13.5,12.335799999999999,13.164200000000001,12,12.75,12Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M2.5,3.25C2.5,2.8357900000000003,2.8357900000000003,2.5,3.25,2.5C3.66421,2.5,4,2.8357900000000003,4,3.25C4,3.66421,3.66421,4,3.25,4C2.8357900000000003,4,2.5,3.66421,2.5,3.25ZM3.25,1C2.0073600000000003,1,1,2.0073600000000003,1,3.25C1,4.22966,1.6261100000000002,5.06309,2.5,5.37197L2.5,10.628C1.6261100000000002,10.9369,1,11.7703,1,12.75C1,13.9926,2.0073600000000003,15,3.25,15C4.49264,15,5.5,13.9926,5.5,12.75C5.5,11.7703,4.873889999999999,10.9369,4,10.628L4,5.37197C4.873889999999999,5.06309,5.5,4.22966,5.5,3.25C5.5,2.0073600000000003,4.49264,1,3.25,1ZM3.25,12C2.8357900000000003,12,2.5,12.3358,2.5,12.75C2.5,13.1642,2.8357900000000003,13.5,3.25,13.5C3.66421,13.5,4,13.1642,4,12.75C4,12.3358,3.66421,12,3.25,12Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
