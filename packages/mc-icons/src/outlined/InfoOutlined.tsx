import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function InfoOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-info-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04950">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04950)">
          <path
            d="M8,1.5C4.41015,1.5,1.5,4.41015,1.5,8C1.5,11.5899,4.41015,14.5,8,14.5C11.5899,14.5,14.5,11.5899,14.5,8C14.5,4.41015,11.5899,1.5,8,1.5ZM0,8C0,3.58172,3.58172,0,8,0C12.4183,0,16,3.58172,16,8C16,12.4183,12.4183,16,8,16C3.58172,16,0,12.4183,0,8ZM6.5,7.75C6.5,7.33579,6.83579,7,7.25,7L8.25,7C8.66421,7,9,7.33579,9,7.75L9,10.5L9.25,10.5C9.66421,10.5,10,10.8358,10,11.25C10,11.6642,9.66421,12,9.25,12L7.25,12C6.83579,12,6.5,11.6642,6.5,11.25C6.5,10.8358,6.83579,10.5,7.25,10.5L7.5,10.5L7.5,8.5L7.25,8.5C6.83579,8.5,6.5,8.16421,6.5,7.75ZM8,6C8.55229,6,9,5.55228,9,5C9,4.44772,8.55229,4,8,4C7.44772,4,7,4.44772,7,5C7,5.55228,7.44772,6,8,6Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
