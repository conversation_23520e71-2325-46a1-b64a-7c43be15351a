import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function UnlinkOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-unlink-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04622">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04622)">
          <path
            d="M12.914203668327332,5.914210715255737C13.69530366832733,5.1331607152557375,13.69530366832733,3.866840715255737,12.914203668327332,3.085790715255737C12.133203668327331,2.304740715255737,10.866803668327332,2.304740715255737,10.085803668327332,3.085790715255737L9.24906366832733,3.9225207152557373C8.956169668327332,4.215410715255738,8.481300668327332,4.215410715255738,8.188399668327332,3.9225107152557372C7.895510068327331,3.6296207152557374,7.895510068327331,3.1547507152557372,8.188409668327331,2.8618507152557373L9.025143668327331,2.025130715255737C10.392003668327332,0.6582907152557373,12.608003668327331,0.6582907152557373,13.974903668327332,2.025130715255737C15.34170366832733,3.3919607152557374,15.34170366832733,5.608040715255737,13.974903668327332,6.974870715255737L13.780303668327331,7.169420715255737C13.487403668327332,7.462310715255738,13.012603668327332,7.462310715255738,12.71970366832733,7.169420715255737C12.42680366832733,6.8765207152557375,12.42680366832733,6.401650715255737,12.71970366832733,6.1087607152557375L12.914203668327332,5.914210715255737Z"
            fill="currentColor"
          />
          <path
            d="M11.04377079372406,9.395705269355773C11.02297079372406,9.423275269355774,10.99997079372406,9.449745269355773,10.97490079372406,9.474865269355774C10.34460079372406,10.105095269355774,9.50620079372406,10.583005269355773,8.63117079372406,10.738405269355773C7.74038079372406,10.896695269355774,6.7709807937240605,10.720705269355774,6.02514079372406,9.974865269355774C5.73224079372406,9.681975269355775,5.73224079372406,9.207095269355774,6.02514079372406,8.914215269355774C6.31803079372406,8.621315269355774,6.79290079372406,8.621315269355774,7.08580079372406,8.914215269355774C7.41389079372406,9.242305269355775,7.85207079372406,9.353335269355775,8.36885079372406,9.261545269355775C8.862080793724061,9.173955269355773,9.38670079372406,8.902355269355773,9.81397079372406,8.510235269355775L8.56736079372406,7.612665269355774C8.563940793724061,7.610255269355774,8.56054079372406,7.607805269355774,8.557150793724059,7.605325269355774L8.26170079372406,7.392595269355774C7.32186079372406,6.795895269355774,6.27753079372406,6.893995269355774,5.58576079372406,7.585775269355774L3.08576079372406,10.085805269355774C2.30471079372406,10.866805269355774,2.30471079372406,12.133235269355774,3.08576079372406,12.914235269355775C3.8668007937240603,13.695335269355773,5.13313079372406,13.695335269355773,5.91418079372406,12.914235269355775L6.75092079372406,12.077535269355774C7.04381079372406,11.784635269355775,7.51868079372406,11.784635269355775,7.81158079372406,12.077535269355774C8.10447079372406,12.370435269355774,8.10447079372406,12.845235269355774,7.81157079372406,13.138135269355773L6.97484079372406,13.974935269355774C5.60801079372406,15.341735269355773,3.39193079372406,15.341735269355773,2.02510079372406,13.974935269355774C0.6582607937240601,12.608035269355774,0.6582607937240601,10.392005269355774,2.02510079372406,9.025115269355773L4.52510079372406,6.525115269355774C4.92968079372406,6.1205352693557735,5.39102079372406,5.840715269355774,5.87871079372406,5.676845269355773L2.3117607937240603,3.1086352693557737C1.9756097937240602,2.8666152693557736,1.89930979372406,2.3979102693557737,2.1413307937240598,2.0617602693557737C2.38336079372406,1.725620069355774,2.85206079372406,1.649310269355774,3.18821079372406,1.891340269355774L9.11848079372406,6.161135269355774C9.23253079372406,6.235425269355774,9.344120793724061,6.315685269355774,9.45278079372406,6.401835269355774L15.68817079372406,10.891305269355774C16.02437079372406,11.133395269355773,16.10067079372406,11.602105269355773,15.85857079372406,11.938235269355774C15.61657079372406,12.274435269355774,15.14787079372406,12.350735269355773,14.81177079372406,12.108635269355775L11.04377079372406,9.395705269355773Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
