import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function LogOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-log-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_03972">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_03972)">
          <path
            d="M5,8.25C5,7.83579,5.33579,7.5,5.75,7.5L9.75,7.5C10.164200000000001,7.5,10.5,7.83579,10.5,8.25C10.5,8.66421,10.164200000000001,9,9.75,9L5.75,9C5.33579,9,5,8.66421,5,8.25Z"
            fill="currentColor"
          />
          <path
            d="M4,10.5C3.58579,10.5,3.25,10.8358,3.25,11.25C3.25,11.6642,3.58579,12,4,12L8,12C8.41421,12,8.75,11.6642,8.75,11.25C8.75,10.8358,8.41421,10.5,8,10.5L4,10.5Z"
            fill="currentColor"
          />
          <path
            d="M13,-0.00488281L3,-0.00488281C1.34315,-0.00488281,0,1.33826,0,2.99512C0,3.67056,0.224382,4.2492,0.603487,4.71725C1.12925,5.36638,1.9341,5.5,2.5095,5.5L3.68699,5.5C3.3227,6.16233,2.87272,6.83851,2.40028,7.54844C2.19504,7.85685,1.98557,8.17163,1.77704,8.49441C0.891308,9.8654,0,11.4175,0,13C0,14.6569,1.34315,16,3,16L13,16C14.6569,16,16,14.6569,16,13C16,11.9591,15.4693,11.0425,14.6672,10.5056C14.323,10.2752,13.8572,10.3675,13.6267,10.7117C13.3963,11.0559,13.4886,11.5217,13.8328,11.7521C14.2367,12.0225,14.5,12.4806,14.5,13C14.5,13.8284,13.8284,14.5,13,14.5C12.1716,14.5,11.5,13.8284,11.5,13C11.5,12.3576,11.7247,11.6528,12.1226,10.8645C12.5204,10.0766,13.0559,9.27112,13.6242,8.41791L13.6352,8.4014C14.1888,7.57021,14.7735,6.6924,15.2166,5.81304C15.6625,4.92823,16,3.97657,16,2.99512C16,1.32336,14.6542,-0.00488281,13,-0.00488281ZM3,1.49512C2.17157,1.49512,1.5,2.16669,1.5,2.99512C1.5,3.31643,1.59969,3.56398,1.76911,3.77315C1.86665,3.89358,2.09458,4,2.5095,4L10.1839,4C10.0663,3.69926,10,3.36466,10,2.99512C10,2.44869,10.1461,1.93638,10.4013,1.49512L3,1.49512ZM13,1.49512C13.8311,1.49512,14.5,2.15711,14.5,2.99512C14.5,3.64111,14.275,4.34845,13.8771,5.13803C13.4794,5.92725,12.9439,6.73342,12.3758,7.58638L12.3589,7.61171C11.8072,8.44003,11.2251,9.31395,10.7836,10.1885C10.3378,11.0715,10,12.0211,10,13C10,13.5464,10.1461,14.0587,10.4013,14.5L3,14.5C2.17157,14.5,1.5,13.8284,1.5,13C1.5,11.9157,2.13058,10.7114,3.03697,9.30839C3.21435,9.03383,3.40257,8.75152,3.59463,8.46343C4.22715,7.5147,4.90143,6.50334,5.36784,5.5L11.75,5.5C12.0802,5.5,12.3716,5.28399,12.4676,4.96801C12.5636,4.65203,12.4416,4.31044,12.1672,4.12674C11.7225,3.82906,11.5,3.47054,11.5,2.99512C11.5,2.16669,12.1716,1.49512,13,1.49512Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
