import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function PeopleOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-people-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_03770">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_03770)">
          <path
            d="M5.49999478515625,3.4999988199996945C4.96956478515625,3.4999988199996945,4.46084478515625,3.710708819999695,4.08578478515625,4.085778819999694C3.71071478515625,4.460858819999695,3.49999478515625,4.969558819999695,3.49999478515625,5.4999988199996945C3.49999478515625,6.0304288199996945,3.71071478515625,6.539138819999695,4.08578478515625,6.914208819999695C4.46084478515625,7.289278819999695,4.96956478515625,7.4999988199996945,5.49999478515625,7.4999988199996945C6.03042478515625,7.4999988199996945,6.53913478515625,7.289278819999695,6.91421478515625,6.914208819999695C7.28928478515625,6.539138819999695,7.49999478515625,6.0304288199996945,7.49999478515625,5.4999988199996945C7.49999478515625,4.969558819999695,7.28928478515625,4.460858819999695,6.91421478515625,4.085778819999694C6.53913478515625,3.710708819999695,6.03042478515625,3.4999988199996945,5.49999478515625,3.4999988199996945ZM1.99999478515625,5.4999988199996945C1.99992478515625,4.919868819999695,2.14405478515625,4.348818819999694,2.41942478515625,3.8382088199996947C2.69479478515625,3.327608819999695,3.09276478515625,2.893459819999695,3.57754478515625,2.574809819999695C4.06232478515625,2.256159819999695,4.61871478515625,2.063010019999695,5.19665478515625,2.012739919999695C5.77460478515625,1.9624600199996949,6.35598478515625,2.0566299199996947,6.88850478515625,2.286769819999695C7.42102478515625,2.516919819999695,7.88799478515625,2.8758198199996947,8.24739478515625,3.331208819999695C8.60680478515625,3.786588819999695,8.84737478515625,4.324168819999695,8.94748478515625,4.895588819999695C9.04760478515625,5.467018819999694,9.00410478515625,6.054368819999695,8.82090478515625,6.604808819999695C8.63770478515625,7.155248819999695,8.32056478515625,7.651528819999695,7.89799478515625,8.048998819999696C8.69485478515625,8.435578819999694,9.38671478515625,9.008668819999695,9.91488478515625,9.719628819999695C10.44305478515625,10.430598819999695,10.79195478515625,11.258498819999694,10.93195478515625,12.132968819999695C10.96315478515625,12.329468819999695,10.91495478515625,12.530368819999694,10.79795478515625,12.691368819999695C10.68105478515625,12.852368819999695,10.50495478515625,12.960268819999694,10.30845478515625,12.991468819999694C10.11195478515625,13.022668819999694,9.91111478515625,12.974468819999695,9.75010478515625,12.857468819999696C9.58911478515625,12.740568819999694,9.48114478515625,12.564468819999695,9.44999478515625,12.367968819999694C9.29867478515625,11.429398819999696,8.81804478515625,10.575298819999695,8.09424478515625,9.958818819999696C7.37043478515625,9.342348819999696,6.45074478515625,9.003788819999695,5.49999478515625,9.003788819999695C4.54924478515625,9.003788819999695,3.62956478515625,9.342348819999696,2.90575478515625,9.958818819999696C2.18194478515625,10.575298819999695,1.70131478515625,11.429398819999696,1.54999478515625,12.367968819999694C1.53450478515625,12.465268819999695,1.49998478515625,12.558568819999694,1.44843478515625,12.642568819999696C1.39687478515625,12.726568819999695,1.32928478515625,12.799568819999696,1.24952478515625,12.857368819999694C1.16975478515625,12.915268819999694,1.07937478515625,12.956768819999695,0.98354178515625,12.979668819999695C0.88770978515625,13.002668819999695,0.78830078515625,13.006468819999695,0.69099078515625,12.990968819999695C0.59368178515625,12.975468819999694,0.50037678515625,12.940968819999695,0.41640478515625,12.889368819999694C0.33243278515625,12.837868819999695,0.25943778515625,12.770268819999695,0.20158678515625,12.690468819999694C0.14373598515625,12.610768819999695,0.10216198515625,12.520368819999694,0.07923998515625,12.424468819999694C0.05631769515625,12.328668819999695,0.05249540515625,12.229268819999694,0.06799139515625,12.131968819999695C0.20832578515625,11.257798819999694,0.55736578515625,10.430398819999695,1.08552478515625,9.719758819999694C1.61368478515625,9.009168819999694,2.30538478515625,8.436398819999695,3.10199478515625,8.049998819999695C2.75352478515625,7.723038819999695,2.47590478515625,7.327998819999695,2.28635478515625,6.889368819999695C2.09679478515625,6.450728819999695,1.99932478515625,5.977838819999695,1.99999478515625,5.4999988199996945ZM10.99995478515625,3.999998819999695C10.80105478515625,3.999998819999695,10.61025478515625,4.079008819999695,10.46965478515625,4.219668819999695C10.32895478515625,4.360318819999694,10.24995478515625,4.551078819999695,10.24995478515625,4.7499988199996945C10.24995478515625,4.948908819999694,10.32895478515625,5.139668819999695,10.46965478515625,5.280328819999695C10.61025478515625,5.420978819999695,10.80105478515625,5.4999988199996945,10.99995478515625,5.4999988199996945C11.33825478515625,5.500018819999695,11.66665478515625,5.614428819999695,11.93185478515625,5.824648819999695C12.19695478515625,6.034868819999695,12.38325478515625,6.328538819999695,12.46035478515625,6.657978819999695C12.53745478515625,6.987418819999695,12.50095478515625,7.333268819999695,12.35675478515625,7.639338819999695C12.21255478515625,7.945418819999695,11.96915478515625,8.193748819999694,11.66595478515625,8.343998819999694C11.54095478515625,8.406178819999695,11.43575478515625,8.502028819999694,11.36225478515625,8.620748819999694C11.28875478515625,8.739478819999695,11.24985478515625,8.876358819999695,11.24995478515625,9.015998819999695L11.24995478515625,9.367998819999695C11.24975478515625,9.536458819999694,11.30625478515625,9.700088819999696,11.41035478515625,9.832518819999695C11.51445478515625,9.964948819999695,11.66015478515625,10.058498819999695,11.82395478515625,10.097998819999695C13.02395478515625,10.386998819999695,13.98595478515625,11.297998819999695,14.34595478515625,12.469968819999695C14.37445478515625,12.564468819999695,14.42145478515625,12.652468819999696,14.48405478515625,12.728868819999695C14.54675478515625,12.805168819999695,14.62385478515625,12.868468819999695,14.71105478515625,12.914968819999695C14.79815478515625,12.961368819999695,14.89365478515625,12.990168819999695,14.99195478515625,12.999668819999695C15.09025478515625,13.009168819999696,15.18945478515625,12.999068819999694,15.28395478515625,12.970168819999694C15.37835478515625,12.941168819999694,15.46615478515625,12.893868819999694,15.54215478515625,12.830868819999695C15.61825478515625,12.767868819999695,15.68115478515625,12.690468819999694,15.72725478515625,12.603068819999695C15.77335478515625,12.515768819999694,15.80175478515625,12.420168819999695,15.81075478515625,12.321768819999695C15.81985478515625,12.223468819999695,15.80935478515625,12.124268819999696,15.77995478515625,12.029968819999695C15.57975478515625,11.380598819999696,15.24955478515625,10.778698819999695,14.80945478515625,10.260998819999696C14.36935478515625,9.743168819999696,13.82855478515625,9.320278819999695,13.21995478515625,9.017998819999695C13.61085478515625,8.587938819999696,13.86855478515625,8.053568819999695,13.96145478515625,7.479848819999694C14.05445478515625,6.906128819999695,13.97875478515625,6.317758819999695,13.74365478515625,5.786248819999695C13.50845478515625,5.2547388199996945,13.12395478515625,4.802978819999694,12.63695478515625,4.485868819999695C12.14985478515625,4.168758819999695,11.58115478515625,3.9999588199996947,10.99995478515625,3.999998819999695Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
