import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function UniappOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-uniapp-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_3465_06366">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_3465_06366)">
          <path
            strokeOpacity={1}
            stroke="currentColor"
            fill="none"
            strokeWidth={1.5}
            d="M1.75 1.75H14.25V14.25H1.75z"
          />
          <path
            d="M4.25,5Q4.25,4.9261315,4.264411,4.853682Q4.278822,4.781233,4.30709,4.712987Q4.335359,4.644742,4.376398,4.583322Q4.417437,4.521903,4.46967,4.46967Q4.521903,4.417437,4.583322,4.376398Q4.644742,4.335359,4.712987,4.30709Q4.781233,4.278822,4.853682,4.264411Q4.9261315,4.25,5,4.25Q5.0738685,4.25,5.146318,4.264411Q5.218767,4.278822,5.287013,4.30709Q5.355258,4.335359,5.416678,4.376398Q5.478097,4.417437,5.53033,4.46967Q5.582563,4.521903,5.623602,4.583322Q5.664641,4.644742,5.69291,4.712987Q5.721178,4.781233,5.735589,4.853682Q5.75,4.9261315,5.75,5L5.75,9.25Q5.75,9.66421,6.04289,9.95711Q6.33579,10.25,6.75,10.25L9.25,10.25Q9.66421,10.25,9.95711,9.95711Q10.25,9.66421,10.25,9.25L10.25,5Q10.25,4.9261315,10.26441,4.853682Q10.27882,4.781233,10.307089999999999,4.712987Q10.33536,4.644742,10.3764,4.583322Q10.41744,4.521903,10.46967,4.46967Q10.521899999999999,4.417437,10.58332,4.376398Q10.644739999999999,4.335359,10.71299,4.30709Q10.78123,4.278822,10.85368,4.264411Q10.92613,4.25,11,4.25Q11.07387,4.25,11.14632,4.264411Q11.21877,4.278822,11.28701,4.30709Q11.355260000000001,4.335359,11.41668,4.376398Q11.478100000000001,4.417437,11.53033,4.46967Q11.58256,4.521903,11.6236,4.583322Q11.66464,4.644742,11.692910000000001,4.712987Q11.72118,4.781233,11.73559,4.853682Q11.75,4.9261315,11.75,5L11.75,9.25Q11.75,10.28553,11.017769999999999,11.017769999999999Q10.28553,11.75,9.25,11.75L6.75,11.75Q5.714466,11.75,4.9822332,11.017769999999999Q4.25,10.28553,4.25,9.25L4.25,5Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
