import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function GitBranchOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-git-branch-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_04055">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_04055)">
          <path
            d="M11.75,2.5C11.3358,2.5,11,2.8357900000000003,11,3.25C11,3.66421,11.3358,4,11.75,4C12.1642,4,12.5,3.66421,12.5,3.25C12.5,2.8357900000000003,12.1642,2.5,11.75,2.5ZM9.5,3.25C9.5,2.0073600000000003,10.5074,1,11.75,1C12.9926,1,14,2.0073600000000003,14,3.25C14,4.22966,13.3739,5.06309,12.5,5.37197L12.5,6C12.5,7.38071,11.3807,8.5,10,8.5L6,8.5C5.44772,8.5,5,8.94772,5,9.5L5,10.628C5.873889999999999,10.9369,6.5,11.7703,6.5,12.75C6.5,13.9926,5.49264,15,4.25,15C3.0073600000000003,15,2,13.9926,2,12.75C2,11.7703,2.62611,10.9369,3.5,10.628L3.5,9.5L3.5,5.37197C2.62611,5.06309,2,4.22966,2,3.25C2,2.0073600000000003,3.0073600000000003,1,4.25,1C5.49264,1,6.5,2.0073600000000003,6.5,3.25C6.5,4.22966,5.873889999999999,5.06309,5,5.37197L5,7.20802C5.306229999999999,7.07422,5.64445,7,6,7L10,7C10.5523,7,11,6.55228,11,6L11,5.37197C10.1261,5.06309,9.5,4.22966,9.5,3.25ZM4.25,12C3.8357900000000003,12,3.5,12.3358,3.5,12.75C3.5,13.1642,3.8357900000000003,13.5,4.25,13.5C4.664210000000001,13.5,5,13.1642,5,12.75C5,12.3358,4.664210000000001,12,4.25,12ZM3.5,3.25C3.5,2.8357900000000003,3.8357900000000003,2.5,4.25,2.5C4.664210000000001,2.5,5,2.8357900000000003,5,3.25C5,3.66421,4.664210000000001,4,4.25,4C3.8357900000000003,4,3.5,3.66421,3.5,3.25Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
