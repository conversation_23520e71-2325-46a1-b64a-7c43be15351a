import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function RepoDeleteOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-repo-delete-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_03942">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_03942)">
          <path
            d="M1,2.5C1,1.11929,2.11929,0,3.5,0L12.25,0C12.6642,0,13,0.335786,13,0.75L13,8.25C13,8.66421,12.6642,9,12.25,9C11.8358,9,11.5,8.66421,11.5,8.25L11.5,1.5L3.5,1.5C2.94772,1.5,2.5,1.94772,2.5,2.5L2.5,9.20802C2.8062300000000002,9.07422,3.14445,9,3.5,9L8.25,9C8.66421,9,9,9.33579,9,9.75C9,10.1642,8.66421,10.5,8.25,10.5L3.5,10.5C2.94772,10.5,2.5,10.9477,2.5,11.5C2.5,12.0523,2.94772,12.5,3.5,12.5L8.25,12.5C8.66421,12.5,9,12.8358,9,13.25C9,13.6642,8.66421,14,8.25,14L3.5,14C2.11929,14,1,12.8807,1,11.5L1,2.5Z"
            fill="currentColor"
          />
          <path
            d="M11.280299536743165,10.219699536743164C10.987400536743165,9.926779736743164,10.512599536743163,9.926779736743164,10.219699536743164,10.219699536743164C9.926779736743164,10.512599536743163,9.926779736743164,10.987400536743165,10.219699536743164,11.280299536743165L11.939299536743164,12.999999536743164L10.219699536743164,14.719699536743164C9.926779736743164,15.012599536743163,9.926779736743164,15.487399536743165,10.219699536743164,15.780299536743165C10.512599536743163,16.073199536743164,10.987400536743165,16.073199536743164,11.280299536743165,15.780299536743165L12.999999536743164,14.060699536743165L14.719699536743164,15.780299536743165C15.012599536743163,16.073199536743164,15.487399536743165,16.073199536743164,15.780299536743165,15.780299536743165C16.073199536743164,15.487399536743165,16.073199536743164,15.012599536743163,15.780299536743165,14.719699536743164L14.060699536743165,12.999999536743164L15.780299536743165,11.280299536743165C16.073199536743164,10.987400536743165,16.073199536743164,10.512599536743163,15.780299536743165,10.219699536743164C15.487399536743165,9.926779736743164,15.012599536743163,9.926779736743164,14.719699536743164,10.219699536743164L12.999999536743164,11.939299536743164L11.280299536743165,10.219699536743164Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
