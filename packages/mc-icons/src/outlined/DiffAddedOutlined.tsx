import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function DiffAddedOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-diff-added-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_04135">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_04135)">
          <path
            d="M13.25,2.5L2.75,2.5C2.61193,2.5,2.5,2.61193,2.5,2.75L2.5,13.25C2.5,13.3881,2.61193,13.5,2.75,13.5L13.25,13.5C13.3881,13.5,13.5,13.3881,13.5,13.25L13.5,2.75C13.5,2.61193,13.3881,2.5,13.25,2.5ZM2.75,1L13.25,1C14.2165,1,15,1.7835,15,2.75L15,13.25C15,14.2165,14.2165,15,13.25,15L2.75,15C1.7835,15,1,14.2165,1,13.25L1,2.75C1,1.7835,1.7835,1,2.75,1ZM8,4C8.41421,4,8.75,4.335789999999999,8.75,4.75L8.75,7.25L11.25,7.25C11.6642,7.25,12,7.58579,12,8C12,8.41421,11.6642,8.75,11.25,8.75L8.75,8.75L8.75,11.25C8.75,11.6642,8.41421,12,8,12C7.58579,12,7.25,11.6642,7.25,11.25L7.25,8.75L4.75,8.75C4.335789999999999,8.75,4,8.41421,4,8C4,7.58579,4.335789999999999,7.25,4.75,7.25L7.25,7.25L7.25,4.75C7.25,4.335789999999999,7.58579,4,8,4Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
