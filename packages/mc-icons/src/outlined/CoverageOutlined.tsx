import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function CoverageOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-coverage-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4077_34317">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4077_34317)">
          <path
            d="M0,8C0,12.3354,3.44867,15.8654,7.75237,15.9962C7.89038,16.0004,8,15.8881,8,15.75L8,8.17C8,8.07611,8.07611,8,8.17,8L15.75,8C15.8881,8,16.0004,7.89038,15.9962,7.75237C15.8654,3.44867,12.3354,0,8,0C3.58172,0,0,3.58172,0,8ZM1.5,8Q1.5,10.6381,3.33802,12.5295Q4.70072,13.9317,6.5,14.3363L6.5,8.17Q6.5,7.47826,6.98913,6.98913Q7.47826,6.5,8.17,6.5L14.3363,6.5Q13.9317,4.70072,12.5295,3.33802Q10.6381,1.5,8,1.5Q5.30761,1.5,3.40381,3.40381Q1.5,5.30761,1.5,8Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M15.60557,12.94721L10.223607,15.638200000000001C9.891156,15.80442,9.5,15.56267,9.5,15.19098L9.5,9.809017C9.5,9.437326,9.891156,9.195578,10.223607,9.361803L15.2355,11.867750000000001L15.60557,12.05279C15.9741,12.23705,15.9741,12.76295,15.60557,12.94721M10.91375,11.2875L13.33876,12.5L10.91375,13.7125L10.91375,11.2875Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
