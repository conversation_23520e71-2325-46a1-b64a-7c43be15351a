import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function GitCompareOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-git-compare-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_04047">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_04047)">
          <path
            d="M9.57322,0.67675294375L7.17677,3.07321734375C7.07914,3.17084734375,7.07914,3.32913734375,7.17677,3.42676734375L9.57322,5.82319734375C9.73071,5.98068734375,9.99999,5.86914734375,9.99999,5.64641734375L9.99999,3.99999734375L11,3.99999734375C11.5523,3.99999734375,12,4.44771734375,12,4.99999734375L12,10.62802734375C11.1261,10.93692734375,10.5,11.77032734375,10.5,12.75002734375C10.5,13.99262734375,11.5074,15.00002734375,12.75,15.00002734375C13.9926,15.00002734375,15,13.99262734375,15,12.75002734375C15,11.77032734375,14.3739,10.93692734375,13.5,10.62802734375L13.5,4.99999734375C13.5,3.61928734375,12.3807,2.49999734375,11,2.49999734375L9.99999,2.49999734375L9.99999,0.85352934375C9.99999,0.63080194375,9.73071,0.51925994375,9.57322,0.67675294375ZM6.00001,12.00002734375L6.00001,10.35349734375C6.00001,10.13079734375,6.26929,10.01929734375,6.42678,10.17679734375L8.823229999999999,12.57322734375C8.920860000000001,12.67082734375,8.920860000000001,12.82912734375,8.823229999999999,12.92672734375L6.42678,15.32322734375C6.26929,15.48072734375,6.00001,15.36922734375,6.00001,15.14642734375L6.00001,13.50002734375L5,13.50002734375C3.61929,13.50002734375,2.5,12.38072734375,2.5,11.00002734375L2.5,5.37193734375C1.6261100000000002,5.06305734375,1,4.22962734375,1,3.24996734375C1,2.00732734375,2.0073600000000003,0.99996934375,3.25,0.99996934375C4.49264,0.99996934375,5.5,2.00732734375,5.5,3.24996734375C5.5,4.22962734375,4.873889999999999,5.06305734375,4,5.37193734375L4,11.00002734375C4,11.55232734375,4.44772,12.00002734375,5,12.00002734375L6.00001,12.00002734375ZM12.75,12.00002734375C12.3358,12.00002734375,12,12.33582734375,12,12.75002734375C12,13.16422734375,12.3358,13.50002734375,12.75,13.50002734375C13.1642,13.50002734375,13.5,13.16422734375,13.5,12.75002734375C13.5,12.33582734375,13.1642,12.00002734375,12.75,12.00002734375ZM4,3.24996734375C4,3.66417734375,3.66421,3.99996734375,3.25,3.99996734375C2.8357900000000003,3.99996734375,2.5,3.66417734375,2.5,3.24996734375C2.5,2.83575734375,2.8357900000000003,2.49996734375,3.25,2.49996734375C3.66421,2.49996734375,4,2.83575734375,4,3.24996734375Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
