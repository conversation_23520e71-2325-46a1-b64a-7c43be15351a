import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function MirrorOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-mirror-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_05002">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_05002)">
          <path
            d="M8.75,1.75C8.75,1.33579,8.41421,1,8,1C7.58579,1,7.25,1.33579,7.25,1.75L7.25,2.25C7.25,2.6642099999999997,7.58579,3,8,3C8.41421,3,8.75,2.6642099999999997,8.75,2.25L8.75,1.75ZM8,4C8.41421,4,8.75,4.335789999999999,8.75,4.75L8.75,5.25C8.75,5.66421,8.41421,6,8,6C7.58579,6,7.25,5.66421,7.25,5.25L7.25,4.75C7.25,4.335789999999999,7.58579,4,8,4ZM8.75,7.75C8.75,7.33579,8.41421,7,8,7C7.58579,7,7.25,7.33579,7.25,7.75L7.25,8.25C7.25,8.66421,7.58579,9,8,9C8.41421,9,8.75,8.66421,8.75,8.25L8.75,7.75ZM8,10C8.41421,10,8.75,10.3358,8.75,10.75L8.75,11.25C8.75,11.6642,8.41421,12,8,12C7.58579,12,7.25,11.6642,7.25,11.25L7.25,10.75C7.25,10.3358,7.58579,10,8,10ZM8,13C8.41421,13,8.75,13.3358,8.75,13.75L8.75,14.25C8.75,14.6642,8.41421,15,8,15C7.58579,15,7.25,14.6642,7.25,14.25L7.25,13.75C7.25,13.3358,7.58579,13,8,13ZM15.5469,3.06128C15.8219,3.17981,16,3.45057,16,3.75002L16,12.25C16,12.5495,15.8219,12.8202,15.5469,12.9388C15.2719,13.0573,14.9527,13.0009,14.735,12.7953L10.235,8.54528C10.085,8.403590000000001,10,8.20636,10,8.00002C10,7.79367,10.085,7.59644,10.235,7.45476L14.735,3.20476C14.9527,2.99915,15.2719,2.9427399999999997,15.5469,3.06128ZM11.8423,8.00002L14.5,10.5101L14.5,5.48997L11.8423,8.00002ZM0,12.25C0,12.5494,0.178128,12.8202,0.453121,12.9387C0.728114,13.0573,1.04726,13.0009,1.26497,12.7952L5.76497,8.54524C5.91498,8.403559999999999,6,8.206330000000001,6,7.99998C6,7.79364,5.91498,7.5964,5.76497,7.45472L1.26497,3.20472C1.04726,2.99911,0.728114,2.94271,0.453121,3.06124C0.178128,3.17978,0,3.45053,0,3.74998L0,12.25ZM1.5,5.48993L4.1577,7.99998L1.5,10.51L1.5,5.48993Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
