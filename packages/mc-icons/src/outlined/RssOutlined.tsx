import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function RssOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-rss-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_03917">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_03917)">
          <path
            d="M2.0016200556344605,2.7251801088256835C2.0288200456344603,2.311860108825684,2.3859398456344603,1.9988600088256836,2.7992598456344604,2.0260601088256838C8.79028884563446,2.420390108825684,13.57943884563446,7.209543108825684,13.97383884563446,13.200613108825683C14.00103884563446,13.613913108825683,13.68803884563446,13.971013108825684,13.274738845634461,13.998213108825684C12.86133884563446,14.025413108825683,12.504238845634461,13.712413108825684,12.47703884563446,13.299113108825683C12.13213884563446,8.058873108825683,7.94096884563446,3.8677431088256835,2.7007398456344607,3.5228231088256834C2.2874198456344605,3.4956231088256837,1.9744100456344604,3.1385031088256836,2.0016200556344605,2.7251801088256835ZM2.0000000000344604,12.999813108825684C2.0000000000344604,12.447513108825683,2.4477198456344604,11.999803108825684,2.9999988456344604,11.999803108825684C3.5522788456344605,11.999803108825684,3.9999988456344604,12.447513108825683,3.9999988456344604,12.999813108825684C3.9999988456344604,13.552013108825683,3.5522788456344605,13.999813108825684,2.9999988456344604,13.999813108825684C2.4477198456344604,13.999813108825684,2.0000000000344604,13.552013108825683,2.0000000000344604,12.999813108825684ZM2.8390698456344605,7.049573108825683C2.4277898456344604,7.000383108825684,2.0545101456344605,7.293923108825684,2.0053200756344602,7.705203108825684C1.9561300456344604,8.116493108825683,2.2496698456344606,8.489773108825684,2.6609498456344607,8.538963108825683C5.16958884563446,8.838973108825684,7.16076884563446,10.830203108825684,7.460778845634461,13.338813108825683C7.509968845634461,13.750113108825683,7.88324884563446,14.043613108825683,8.29452884563446,13.994413108825684C8.705818845634461,13.945213108825683,8.999348845634461,13.571913108825683,8.950168845634462,13.160713108825684C8.567958845634461,9.964753108825683,6.034978845634461,7.431783108825684,2.8390698456344605,7.049573108825683Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
