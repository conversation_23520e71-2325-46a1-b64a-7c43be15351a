import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function DeployErrorFilled(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-deploy-error-filled': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 20 20"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <path
          d="M3.5999999046325684,3.5L3.5999999046325684,16.5C3.5999999046325684,17.328400000000002,4.271572904632569,18,5.099999904632568,18L10.403849904632569,18Q10.057429904632567,17.4,9.856879904632567,16.736800000000002Q9.656319904632568,16.0736,9.612189904632569,15.3822C9.570559904632567,15.4461,9.498489904632567,15.4883,9.416569904632567,15.4883C9.376919904632569,15.4883,9.337919904632567,15.4782,9.303249904632569,15.459L6.7399399046325685,14.0349C6.591789904632568,13.9526,6.499899904632569,13.7964,6.499899904632569,13.6269L6.499899904632569,10.65619C6.499899904632569,10.52732,6.604369904632568,10.42285,6.733239904632568,10.42285C6.768279904632569,10.42285,6.802879904632569,10.43075,6.834459904632569,10.44595L9.385679904632568,11.67432C9.547209904632568,11.75209,9.64989990463257,11.91551,9.64989990463257,12.0948L9.64989990463257,14.2278Q9.796939904632568,13.0949,10.349999904632568,12.0953L10.349999904632568,12.0948C10.349999904632568,11.9201,10.447509904632568,11.76046,10.60193990463257,11.68045Q11.079539904632568,10.96136,11.740489904632568,10.40607L10.197229904632568,11.12626C10.072139904632568,11.18463,9.927629904632568,11.18463,9.802539904632567,11.12626L6.952979904632569,9.79646C6.8361999046325685,9.74197,6.785709904632569,9.60312,6.840209904632568,9.48635C6.863389904632568,9.436679999999999,6.903309904632568,9.39676,6.952979904632569,9.37358L9.802539904632567,8.04378C9.927629904632568,7.98541,10.072139904632568,7.98541,10.197229904632568,8.04378L13.046789904632568,9.37358C13.108169904632568,9.40222,13.151229904632569,9.45415,13.17035990463257,9.51394Q13.744199904632568,9.25978,14.358299904632569,9.12989Q14.972399904632569,9,15.599999904632568,9Q16.00179990463257,9,16.39999990463257,9.05357L16.39999990463257,7.00132C16.39999990463257,6.6035,16.241999904632568,6.22196,15.960699904632568,5.940659999999999L12.459339904632568,2.43934C12.178039904632568,2.158035,11.796499904632569,2,11.39867990463257,2L5.099999904632568,2C4.271572904632569,2,3.5999999046325684,2.671573,3.5999999046325684,3.5ZM15.199799904632568,6.9202L11.919819904632568,6.9202C11.643679904632569,6.9202,11.419819904632568,6.69634,11.419819904632568,6.4202L11.419819904632568,3.2001999999999997L15.199799904632568,6.9202Z"
          fillRule="evenodd"
          fill="currentColor"
        />
        <path
          d="M15.5,19.5Q15.61047,19.5,15.7208,19.49458Q15.831140000000001,19.48916,15.94108,19.47833Q16.051009999999998,19.4675,16.16029,19.45129Q16.26956,19.43508,16.37791,19.41353Q16.48625,19.39198,16.59341,19.36514Q16.70057,19.3383,16.80628,19.30623Q16.91199,19.274160000000002,17.016,19.23695Q17.12001,19.199730000000002,17.222070000000002,19.15746Q17.32413,19.115180000000002,17.424,19.06795Q17.52386,19.02072,17.62128,18.96864Q17.71871,18.91657,17.81346,18.85978Q17.90821,18.80299,18.00006,18.74161Q18.091920000000002,18.680239999999998,18.18065,18.61443Q18.269379999999998,18.54863,18.354770000000002,18.47855Q18.44016,18.40847,18.52201,18.33428Q18.60387,18.260089999999998,18.68198,18.18198Q18.760089999999998,18.10387,18.83428,18.02201Q18.90847,17.94016,18.97855,17.854770000000002Q19.04863,17.769379999999998,19.11443,17.68065Q19.180239999999998,17.591920000000002,19.24161,17.50006Q19.30299,17.40821,19.35978,17.31346Q19.41657,17.21871,19.46865,17.12128Q19.52072,17.02386,19.56795,16.924Q19.615180000000002,16.82413,19.65746,16.722070000000002Q19.699730000000002,16.62001,19.73695,16.516Q19.774160000000002,16.41199,19.80623,16.30628Q19.8383,16.20057,19.86514,16.09341Q19.89198,15.98625,19.91353,15.87791Q19.93508,15.76956,19.95129,15.66029Q19.9675,15.55101,19.97833,15.44108Q19.98916,15.331140000000001,19.99458,15.2208Q20,15.11047,20,15Q20,14.88953,19.99458,14.7792Q19.98916,14.668859999999999,19.97833,14.55892Q19.9675,14.44899,19.95129,14.33971Q19.93508,14.23044,19.91353,14.12209Q19.89198,14.01375,19.86514,13.90659Q19.8383,13.799430000000001,19.80623,13.693719999999999Q19.774160000000002,13.58801,19.73695,13.484Q19.699730000000002,13.37998,19.65746,13.27792Q19.615180000000002,13.17586,19.56795,13.076Q19.52072,12.976140000000001,19.46864,12.87871Q19.41657,12.78129,19.35978,12.68654Q19.30299,12.59179,19.24161,12.499929999999999Q19.180239999999998,12.40808,19.11443,12.31935Q19.04863,12.23062,18.97855,12.14523Q18.90847,12.05984,18.83428,11.97798Q18.760089999999998,11.89613,18.68198,11.81802Q18.60387,11.73991,18.52201,11.66572Q18.44016,11.59153,18.354770000000002,11.52145Q18.269379999999998,11.451372,18.18065,11.385566Q18.091920000000002,11.31976,18.00006,11.258387Q17.90821,11.197013,17.81346,11.140221Q17.71871,11.083429,17.62128,11.031354Q17.52386,10.97928,17.424,10.932048Q17.32413,10.884817,17.222070000000002,10.842542Q17.12001,10.800267,17.016,10.763052Q16.91199,10.725836,16.80628,10.693768Q16.70057,10.661701,16.59341,10.634859Q16.48625,10.608018,16.37791,10.5864662Q16.26956,10.5649148,16.16029,10.5487057Q16.051009999999998,10.5324966,15.94108,10.5216687Q15.831140000000001,10.5108409,15.7208,10.50542045Q15.61047,10.5,15.5,10.5Q15.38953,10.5,15.2792,10.50542045Q15.168859999999999,10.5108409,15.05892,10.5216687Q14.94899,10.5324966,14.83971,10.5487057Q14.73044,10.5649148,14.62209,10.5864662Q14.51375,10.608018,14.40659,10.634859Q14.299430000000001,10.661701,14.193719999999999,10.693768Q14.08801,10.725836,13.984,10.763052Q13.87998,10.800267,13.77792,10.842542Q13.67586,10.884817,13.576,10.932048Q13.476140000000001,10.97928,13.37871,11.031354Q13.28129,11.083429,13.18654,11.140221Q13.09179,11.197013,12.999929999999999,11.258387Q12.90808,11.31976,12.81935,11.385566Q12.73062,11.451372,12.64523,11.52145Q12.55984,11.59153,12.47798,11.66572Q12.39613,11.73991,12.31802,11.81802Q12.23991,11.89613,12.16572,11.97798Q12.09153,12.05984,12.02145,12.14523Q11.951372,12.23062,11.885566,12.31935Q11.81976,12.40808,11.758387,12.499929999999999Q11.697013,12.59179,11.640221,12.68654Q11.583429,12.78129,11.531354,12.87871Q11.47928,12.976140000000001,11.432048,13.076Q11.384817,13.17586,11.342542,13.27792Q11.300267,13.37998,11.263052,13.484Q11.225836,13.58801,11.193768,13.693719999999999Q11.161701,13.799430000000001,11.134859,13.90659Q11.108018,14.01375,11.0864662,14.12209Q11.0649148,14.23044,11.0487057,14.33971Q11.0324966,14.44899,11.0216687,14.55892Q11.0108409,14.668859999999999,11.00542045,14.7792Q11,14.88953,11,15Q11,15.11047,11.00542045,15.2208Q11.0108409,15.331140000000001,11.0216687,15.44108Q11.0324966,15.55101,11.0487057,15.66029Q11.0649148,15.76956,11.0864662,15.87791Q11.108018,15.98625,11.134859,16.09341Q11.161701,16.20057,11.193768,16.30628Q11.225836,16.41199,11.263052,16.516Q11.300267,16.62001,11.342542,16.722070000000002Q11.384817,16.82413,11.432048,16.924Q11.47928,17.02386,11.531354,17.12128Q11.583429,17.21871,11.640221,17.31346Q11.697013,17.40821,11.758387,17.50006Q11.81976,17.591920000000002,11.885566,17.68065Q11.951372,17.769379999999998,12.02145,17.854770000000002Q12.09153,17.94016,12.16572,18.02201Q12.23991,18.10387,12.31802,18.18198Q12.39613,18.260089999999998,12.47798,18.33428Q12.55984,18.40847,12.64523,18.47855Q12.73062,18.54863,12.81935,18.61443Q12.90808,18.680239999999998,12.999929999999999,18.74161Q13.09179,18.80299,13.18654,18.85978Q13.28129,18.91657,13.37871,18.96865Q13.476140000000001,19.02072,13.576,19.06795Q13.67586,19.115180000000002,13.77792,19.15746Q13.87998,19.199730000000002,13.984,19.23695Q14.08801,19.274160000000002,14.193719999999999,19.30623Q14.299430000000001,19.3383,14.40659,19.36514Q14.51375,19.39198,14.62209,19.41353Q14.73044,19.43508,14.83971,19.45129Q14.94899,19.4675,15.05892,19.47833Q15.168859999999999,19.48916,15.2792,19.49458Q15.38953,19.5,15.5,19.5ZM13.21967,13.78033C12.92678,13.48743,12.92678,13.01257,13.21967,12.71968C13.51256,12.42677,13.98744,12.42677,14.28033,12.71968L15.5,13.93934L16.71967,12.71968C17.01256,12.42677,17.48744,12.42677,17.7803,12.71968C18.0732,13.01257,18.0732,13.48743,17.7803,13.78033L16.56066,15L17.7803,16.21968C18.0732,16.51256,18.0732,16.98744,17.7803,17.28033C17.48744,17.57323,17.01256,17.57323,16.71967,17.28033L15.5,16.060670000000002L14.28033,17.28033C13.98744,17.57323,13.51256,17.57323,13.21967,17.28033C12.92678,16.98744,12.92678,16.51256,13.21967,16.21968L14.43934,15L13.21967,13.78033Z"
          fillRule="evenodd"
          fill="currentColor"
        />
      </svg>
    </span>
  );
}
