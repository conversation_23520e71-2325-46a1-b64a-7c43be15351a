import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function CodeFilled(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-code-filled': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4676_81022">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4676_81022)">
          <path
            d="M2.5999876737594603,0.7998045682907104L13.39998767375946,0.7998045682907104C14.39408767375946,0.7998045682907104,15.19998767375946,1.6056915682907105,15.19998767375946,2.5998045682907103L15.19998767375946,13.39980456829071C15.19998767375946,14.39390456829071,14.39408767375946,15.19980456829071,13.39998767375946,15.19980456829071L2.5999876737594603,15.19980456829071C1.6058746737594605,15.19980456829071,0.7999876737594604,14.39390456829071,0.7999876737594604,13.39980456829071L0.7999876737594604,2.5998045682907103C0.7999876737594604,1.6056915682907105,1.6058746737594605,0.7998045682907104,2.5999876737594603,0.7998045682907104ZM9.75413767375946,5.622784568290711C9.91343767375946,5.132284568290711,9.64523767375946,4.604884568290711,9.15383767375946,4.445584568290711L9.15473767375946,4.445584568290711C9.038007673759461,4.407674568290711,8.914977673759461,4.39314456829071,8.79266767375946,4.40280456829071C8.670357673759462,4.41244456829071,8.55110767375946,4.44611456829071,8.441757673759462,4.50186456829071C8.33240767375946,4.55760456829071,8.235207673759461,4.634344568290711,8.15555767375946,4.72769456829071C8.07590767375946,4.8210345682907105,8.015427673759461,4.92915456829071,7.9775376737594605,5.0458845682907105L6.24593767375946,10.37658456829071C6.20480767375946,10.49403456829071,6.18758767375946,10.61859456829071,6.1952576737594605,10.74279456829071C6.20291767375946,10.86700456829071,6.235317673759461,10.988504568290711,6.290667673759461,11.10000456829071C6.34592767375946,11.21150456829071,6.42287767375946,11.310904568290711,6.517107673759461,11.39220456829071C6.61124767375946,11.47360456829071,6.720777673759461,11.53520456829071,6.83912767375946,11.57380456829071C6.957567673759461,11.61220456829071,7.08239767375946,11.62660456829071,7.206417673759461,11.61610456829071C7.3304376737594605,11.60550456829071,7.45112767375946,11.57040456829071,7.56137767375946,11.51270456829071C7.671627673759461,11.45490456829071,7.76918767375946,11.37560456829071,7.8483876737594604,11.27960456829071C7.927587673759461,11.183604568290711,7.98680767375946,11.07270456829071,8.02253767375946,10.953504568290711L9.75413767375946,5.622784568290711ZM10.05654767375946,6.7823245682907105L11.28688767375946,8.012624568290711L10.080847673759461,9.21862456829071C9.999487673759461,9.300344568290711,9.93504767375946,9.39736456829071,9.89130767375946,9.50401456829071C9.84747767375946,9.61066456829071,9.82524767375946,9.72496456829071,9.82569767375946,9.84025456829071C9.82623767375946,9.95563456829071,9.84945767375946,10.06966456829071,9.894187673759461,10.175954568290711C9.93891767375946,10.282334568290711,10.00416767375946,10.37872456829071,10.08624767375946,10.45972456829071C10.25067767375946,10.62370456829071,10.473067673759461,10.716224568290711,10.70526767375946,10.71721456829071C10.93738767375946,10.71820456829071,11.16068767375946,10.62766456829071,11.32648767375946,10.46512456829071L13.14448767375946,8.64712456829071C13.27118767375946,8.520404568290711,13.35578767375946,8.357774568290711,13.38678767375946,8.18128456829071C13.417887673759461,8.00488456829071,13.39388767375946,7.82308456829071,13.31818767375946,7.660724568290711C13.27498767375946,7.54615456829071,13.20778767375946,7.442114568290711,13.12108767375946,7.35562456829071L11.30308767375946,5.53582456829071C11.221287673759461,5.454264568290711,11.124387673759461,5.389654568290711,11.017687673759461,5.345714568290711C10.910887673759461,5.3017745682907105,10.79652767375946,5.27937456829071,10.68114767375946,5.27979456829071C10.56567767375946,5.280204568290711,10.45146767375946,5.30344456829071,10.34508767375946,5.348154568290711C10.23861767375946,5.392864568290711,10.14213767375946,5.45817456829071,10.061047673759461,5.5403245682907105C9.89661767375946,5.70491456829071,9.80391767375946,5.92778456829071,9.80310767375946,6.160394568290711C9.80229767375946,6.392984568290711,9.89337767375946,6.616544568290711,10.05654767375946,6.7823245682907105ZM2.8780976737594606,7.35475456829071C2.7919076737594604,7.4416945682907105,2.72531767375946,7.54600456829071,2.6827976737594605,7.66075456829071L2.6818976737594604,7.66075456829071C2.6063276737594605,7.823024568290711,2.5824676737594605,8.004554568290711,2.61352767375946,8.18086456829071C2.6445976737594608,8.35717456829071,2.7291076737594606,8.51962456829071,2.8555976737594606,8.646254568290711L4.673597673759461,10.46425456829071C4.84151767375946,10.61527456829071,5.060987673759461,10.69619456829071,5.2867776737594605,10.69024456829071C5.512557673759461,10.68430456829071,5.72745767375946,10.591964568290711,5.88717767375946,10.43221456829071C6.046887673759461,10.27256456829071,6.13923767375946,10.05764456829071,6.14519767375946,9.83182456829071C6.15115767375946,9.60602456829071,6.0702576737594605,9.386594568290711,5.91919767375946,9.218654568290711L4.71319767375946,8.01265456829071L5.9434976737594605,6.78235456829071C6.10679767375946,6.616664568290711,6.1980476737594605,6.393194568290711,6.19737767375946,6.160624568290711C6.196707673759461,5.9279945682907105,6.10416767375946,5.705064568290711,5.939897673759461,5.54035456829071C5.858747673759461,5.458274568290711,5.76217767375946,5.39302456829071,5.65573767375946,5.34839456829071C5.549297673759461,5.303764568290711,5.435067673759461,5.28061456829071,5.3196476737594605,5.280274568290711C5.20422767375946,5.27994456829071,5.08987767375946,5.30243456829071,4.983167673759461,5.3464445682907105C4.8764676737594606,5.39046456829071,4.779517673759461,5.455134568290711,4.69789767375946,5.536754568290711L2.8780976737594606,7.35475456829071Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
