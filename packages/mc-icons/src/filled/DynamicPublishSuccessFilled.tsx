import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function DynamicPublishSuccessFilled(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-dynamic-publish-success-filled': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 20 20"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4514_004594">
            <path d="M0 0H20V20H0z" />
          </clipPath>
          <clipPath id="master_svg1_4514_004598">
            <path d="M2 2H18V18H2z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4514_004594)">
          <path
            d="M16.06403515625,2C13.74343515625,2.0000977766,11.51786515625,2.922034,9.87698515625,4.563000000000001L9.41799515625,5.021C9.103985156250001,5.335,8.80198515625,5.662,8.513985156250001,6L5.30998515625,6C5.01116515625,6.00009,4.71735515625,6.07669,4.456525156250001,6.2225C4.19570515625,6.36832,3.97656515625,6.57849,3.81998515625,6.833L2.10999015625,9.607C2.04996375625,9.7045,2.01305735625,9.81445,2.00209982625,9.92842C1.99114226625,10.04239,2.00642441625,10.15736,2.04677485625,10.26451C2.08712545625,10.37166,2.15147315625,10.46815,2.23488615625,10.54658C2.31829815625,10.62502,2.41856115625,10.68331,2.52799015625,10.717L5.62998515625,11.671C5.66698515625,11.722,5.70898515625,11.771,5.75398515625,11.816L8.18298515625,14.244C8.228985156250001,14.29,8.27698515625,14.332,8.32798515625,14.369L9.28198515625,17.471C9.31567515625,17.580399999999997,9.37396515625,17.6807,9.45241515625,17.7641C9.53083515625,17.8475,9.62732515625,17.9119,9.73447515625,17.952199999999998C9.84162515625,17.9926,9.95660515625,18.0078,10.07056515625,17.9969C10.14059515625,17.990099999999998,10.20910515625,17.973599999999998,10.27413515625,17.9479Q9.89352515625,17.2732,9.696765156249999,16.523899999999998Q9.49999515625,15.7747,9.49999515625,15Q9.49999515625,14.8527,9.50722515625,14.7056Q9.514455156250001,14.5585,9.52889515625,14.4119Q9.543325156249999,14.2653,9.564945156250001,14.1196Q9.58655515625,13.9739,9.61528515625,13.8295Q9.644025156249999,13.685,9.679815156250001,13.5421Q9.71560515625,13.3992,9.758355156250001,13.2583Q9.801115156249999,13.1173,9.85073515625,12.9787Q9.90035515625,12.84,9.95672515625,12.7039Q10.01308515625,12.5678,10.07606515625,12.4347Q10.13903515625,12.3015,10.20847515625,12.1716Q10.27790515625,12.0417,10.35362515625,11.91538Q10.42935515625,11.78905,10.51118515625,11.66658Q10.59301515625,11.54411,10.68075515625,11.4258Q10.76849515625,11.3075,10.86193515625,11.19364Q10.95537515625,11.07978,11.05429515625,10.97065Q11.15320515625,10.86151,11.25735515625,10.75736Q11.36151515625,10.65321,11.47064515625,10.55429Q11.57978515625,10.45538,11.69363515625,10.36194Q11.80749515625,10.2685,11.92580515625,10.18075Q12.04413515625,10.09301,12.16653515625,10.01118Q12.28903515625,9.92935,12.41533515625,9.853629999999999Q12.54173515625,9.777899999999999,12.67163515625,9.70847Q12.80153515625,9.63904,12.93463515625,9.57606Q13.06783515625,9.51309,13.20393515625,9.45672Q13.33993515625,9.40036,13.47863515625,9.35074Q13.61733515625,9.301110000000001,13.75833515625,9.25836Q13.89923515625,9.2156,14.04213515625,9.17981Q14.18503515625,9.144020000000001,14.32943515625,9.11529Q14.47393515625,9.086549999999999,14.61963515625,9.06494Q14.76533515625,9.043330000000001,14.91193515625,9.02889Q15.05843515625,9.01445,15.20563515625,9.00723Q15.35273515625,9,15.50003515625,9Q15.92283515625,9,16.34153515625,9.05932C17.412335156250002,7.57787,17.99953515625,5.78725,18.00003515625,3.936L18.00003515625,3.75C18.00003515625,3.28587,17.81563515625,2.840752,17.48743515625,2.512563C17.15923515625,2.184374,16.714135156250002,2,16.25003515625,2L16.06403515625,2ZM12.27983515625,7.69381C12.10043515625,7.50764,12.00003515625,7.259,12.00003515625,7C12.00003515625,6.741,12.10043515625,6.49236,12.27983515625,6.30619Q12.28633515625,6.29948,12.29293515625,6.29289Q12.32793515625,6.25786,12.36623515625,6.22647Q12.41463515625,6.18683,12.46753515625,6.15352Q12.71163515625,6,13.00003515625,6Q13.25013515625,6,13.47093515625,6.11781Q13.55153515625,6.16084,13.62303515625,6.21777Q13.66713515625,6.25295,13.70713515625,6.29289Q13.84773515625,6.43354,13.92383515625,6.61731Q14.00003515625,6.80109,14.00003515625,7Q14.00003515625,7.09896,13.98063515625,7.196Q13.92123515625,7.49297,13.70713515625,7.70711Q13.67203515625,7.74214,13.63373515625,7.77353Q13.58013515625,7.81747,13.52093515625,7.85359Q13.28103515625,8,13.00003515625,8Q12.74983515625,8,12.52913515625,7.88219Q12.44973515625,7.83982,12.37913515625,7.78395Q12.33383515625,7.74803,12.29293515625,7.70711Q12.28633515625,7.70052,12.27983515625,7.69381ZM5.67798515625,10.116L7.19998515625,7.766C7.25798515625,7.676,7.31698515625,7.588,7.37598515625,7.5L5.3089851562499994,7.5C5.26627515625,7.49999,5.22426515625,7.51092,5.18698515625,7.53175C5.14969515625,7.55258,5.11836515625,7.58262,5.09598515625,7.619L3.89598515625,9.568999999999999L5.67798515625,10.116ZM5.55998515625,16.560000000000002C5.707355156249999,16.4227,5.825565156250001,16.2571,5.90754515625,16.0731C5.98953515625,15.8891,6.03361515625,15.6905,6.03716515625,15.489C6.04072515625,15.2876,6.00368515625,15.0876,5.92823515625,14.9008C5.85278515625,14.714,5.740495156250001,14.5444,5.59806515625,14.4019C5.45562515625,14.2595,5.28595515625,14.1472,5.09918515625,14.0718C4.91240515625,13.9963,4.7123451562500005,13.9593,4.51094515625,13.9628C4.30953515625,13.9664,4.11090515625,14.0105,3.92690515625,14.0924C3.74291515625,14.1744,3.57730515625,14.2926,3.4399851562499997,14.44C2.70599015625,15.17,2.39299015625,16.772,2.28999015625,17.442999999999998C2.28390015625,17.4792,2.28654715625,17.5163,2.29771015625,17.5512C2.30887315625,17.586100000000002,2.32822915625,17.6179,2.35416215625,17.6438C2.38009615625,17.669800000000002,2.41185515625,17.6891,2.44679015625,17.7003C2.48172615625,17.711399999999998,2.51882415625,17.714100000000002,2.55499015625,17.708C3.22598515625,17.605,4.82798515625,17.292,5.55998515625,16.560000000000002Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M15.5,19.5Q15.61047,19.5,15.7208,19.49458Q15.831140000000001,19.48916,15.94108,19.47833Q16.051009999999998,19.4675,16.16029,19.45129Q16.26956,19.43508,16.37791,19.41353Q16.48625,19.39198,16.59341,19.36514Q16.70057,19.3383,16.80628,19.30623Q16.91199,19.274160000000002,17.016,19.23695Q17.12001,19.199730000000002,17.222070000000002,19.15746Q17.32413,19.115180000000002,17.424,19.06795Q17.52386,19.02072,17.62128,18.96864Q17.71871,18.91657,17.81346,18.85978Q17.90821,18.80299,18.00006,18.74161Q18.091920000000002,18.680239999999998,18.18065,18.61443Q18.269379999999998,18.54863,18.354770000000002,18.47855Q18.44016,18.40847,18.52201,18.33428Q18.60387,18.260089999999998,18.68198,18.18198Q18.760089999999998,18.10387,18.83428,18.02201Q18.90847,17.94016,18.97855,17.854770000000002Q19.04863,17.769379999999998,19.11443,17.68065Q19.180239999999998,17.591920000000002,19.24161,17.50006Q19.30299,17.40821,19.35978,17.31346Q19.41657,17.21871,19.46865,17.12128Q19.52072,17.02386,19.56795,16.924Q19.615180000000002,16.82413,19.65746,16.722070000000002Q19.699730000000002,16.62001,19.73695,16.516Q19.774160000000002,16.41199,19.80623,16.30628Q19.8383,16.20057,19.86514,16.09341Q19.89198,15.98625,19.91353,15.87791Q19.93508,15.76956,19.95129,15.66029Q19.9675,15.55101,19.97833,15.44108Q19.98916,15.331140000000001,19.99458,15.2208Q20,15.11047,20,15Q20,14.88953,19.99458,14.7792Q19.98916,14.668859999999999,19.97833,14.55892Q19.9675,14.44899,19.95129,14.33971Q19.93508,14.23044,19.91353,14.12209Q19.89198,14.01375,19.86514,13.90659Q19.8383,13.799430000000001,19.80623,13.693719999999999Q19.774160000000002,13.58801,19.73695,13.484Q19.699730000000002,13.37998,19.65746,13.27792Q19.615180000000002,13.17586,19.56795,13.076Q19.52072,12.976140000000001,19.46864,12.87871Q19.41657,12.78129,19.35978,12.68654Q19.30299,12.59179,19.24161,12.499929999999999Q19.180239999999998,12.40808,19.11443,12.31935Q19.04863,12.23062,18.97855,12.14523Q18.90847,12.05984,18.83428,11.97798Q18.760089999999998,11.89613,18.68198,11.81802Q18.60387,11.73991,18.52201,11.66572Q18.44016,11.59153,18.354770000000002,11.52145Q18.269379999999998,11.451372,18.18065,11.385566Q18.091920000000002,11.31976,18.00006,11.258387Q17.90821,11.197013,17.81346,11.140221Q17.71871,11.083429,17.62128,11.031354Q17.52386,10.97928,17.424,10.932048Q17.32413,10.884817,17.222070000000002,10.842542Q17.12001,10.800267,17.016,10.763052Q16.91199,10.725836,16.80628,10.693768Q16.70057,10.661701,16.59341,10.634859Q16.48625,10.608018,16.37791,10.5864662Q16.26956,10.5649148,16.16029,10.5487057Q16.051009999999998,10.5324966,15.94108,10.5216687Q15.831140000000001,10.5108409,15.7208,10.50542045Q15.61047,10.5,15.5,10.5Q15.38953,10.5,15.2792,10.50542045Q15.168859999999999,10.5108409,15.05892,10.5216687Q14.94899,10.5324966,14.83971,10.5487057Q14.73044,10.5649148,14.62209,10.5864662Q14.51375,10.608018,14.40659,10.634859Q14.299430000000001,10.661701,14.193719999999999,10.693768Q14.08801,10.725836,13.984,10.763052Q13.87998,10.800267,13.77792,10.842542Q13.67586,10.884817,13.576,10.932048Q13.476140000000001,10.97928,13.37871,11.031354Q13.28129,11.083429,13.18654,11.140221Q13.09179,11.197013,12.999929999999999,11.258387Q12.90808,11.31976,12.81935,11.385566Q12.73062,11.451372,12.64523,11.52145Q12.55984,11.59153,12.47798,11.66572Q12.39613,11.73991,12.31802,11.81802Q12.23991,11.89613,12.16572,11.97798Q12.09153,12.05984,12.02145,12.14523Q11.951372,12.23062,11.885566,12.31935Q11.81976,12.40808,11.758387,12.499929999999999Q11.697013,12.59179,11.640221,12.68654Q11.583429,12.78129,11.531354,12.87871Q11.47928,12.976140000000001,11.432048,13.076Q11.384817,13.17586,11.342542,13.27792Q11.300267,13.37998,11.263052,13.484Q11.225836,13.58801,11.193768,13.693719999999999Q11.161701,13.799430000000001,11.134859,13.90659Q11.108018,14.01375,11.0864662,14.12209Q11.0649148,14.23044,11.0487057,14.33971Q11.0324966,14.44899,11.0216687,14.55892Q11.0108409,14.668859999999999,11.00542045,14.7792Q11,14.88953,11,15Q11,15.11047,11.00542045,15.2208Q11.0108409,15.331140000000001,11.0216687,15.44108Q11.0324966,15.55101,11.0487057,15.66029Q11.0649148,15.76956,11.0864662,15.87791Q11.108018,15.98625,11.134859,16.09341Q11.161701,16.20057,11.193768,16.30628Q11.225836,16.41199,11.263052,16.516Q11.300267,16.62001,11.342542,16.722070000000002Q11.384817,16.82413,11.432048,16.924Q11.47928,17.02386,11.531354,17.12128Q11.583429,17.21871,11.640221,17.31346Q11.697013,17.40821,11.758387,17.50006Q11.81976,17.591920000000002,11.885566,17.68065Q11.951372,17.769379999999998,12.02145,17.854770000000002Q12.09153,17.94016,12.16572,18.02201Q12.23991,18.10387,12.31802,18.18198Q12.39613,18.260089999999998,12.47798,18.33428Q12.55984,18.40847,12.64523,18.47855Q12.73062,18.54863,12.81935,18.61443Q12.90808,18.680239999999998,12.999929999999999,18.74161Q13.09179,18.80299,13.18654,18.85978Q13.28129,18.91657,13.37871,18.96865Q13.476140000000001,19.02072,13.576,19.06795Q13.67586,19.115180000000002,13.77792,19.15746Q13.87998,19.199730000000002,13.984,19.23695Q14.08801,19.274160000000002,14.193719999999999,19.30623Q14.299430000000001,19.3383,14.40659,19.36514Q14.51375,19.39198,14.62209,19.41353Q14.73044,19.43508,14.83971,19.45129Q14.94899,19.4675,15.05892,19.47833Q15.168859999999999,19.48916,15.2792,19.49458Q15.38953,19.5,15.5,19.5ZM12.84955,15.59445C12.55666,15.301549999999999,12.55666,14.82668,12.84955,14.53379C13.14245,14.24089,13.61732,14.24089,13.91021,14.53379L14.7941,15.417670000000001L17.092190000000002,13.11957C17.385089999999998,12.82668,17.85996,12.82668,18.15285,13.11957C18.44575,13.412469999999999,18.44575,13.88734,18.15285,14.18023L15.5012,16.831879999999998Q15.20831,17.12478,14.7941,17.12478Q14.37988,17.12478,14.08699,16.831879999999998L12.84955,15.59445Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
