/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { ChangeFreeResponse } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface CheckParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchCf
 * @name Check
 * @summary check
 * @request GET:/api/v1/patch/cf/release/check
 */
export async function check(query: CheckParams, options?: MethodOptions): Promise<ChangeFreeResponse> {
  return request(`${baseUrl}/api/v1/patch/cf/release/check`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface CreateParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchCf
 * @name Create
 * @summary create
 * @request GET:/api/v1/patch/cf/release/create
 */
export async function create(query: CreateParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cf/release/create`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
