/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  FenceBO,
  IntegrateRegressionItemVO,
  Regression,
  RegressionFileInfo,
  RegressionItemVO,
  RegressionVO,
  SoRegressionItemBO,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface Get32BitAlterRegressionsParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name Get32BitAlterRegressions
 * @summary 获取发布单32位回归信息，目前只有手淘使用
 * @request GET:/api/v1/regression/get32BitAlterRegressions
 */
export async function get32BitAlterRegressions(
  query: Get32BitAlterRegressionsParams,
  options?: MethodOptions,
): Promise<SoRegressionItemBO[]> {
  return request(`${baseUrl}/api/v1/regression/get32BitAlterRegressions`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetFenceResultParams {
  /**
   * regressionId
   * @format int64
   */
  regressionId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name GetFenceResult
 * @summary 获取回归的卡口结果
 * @request GET:/api/v1/regression/getFenceResult
 */
export async function getFenceResult(query: GetFenceResultParams, options?: MethodOptions): Promise<FenceBO[]> {
  return request(`${baseUrl}/api/v1/regression/getFenceResult`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetHistorySoRegressionsParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name GetHistorySoRegressions
 * @summary 获取历史版本的远端化模块回归信息
 * @request GET:/api/v1/regression/getHistorySoRegressions
 */
export async function getHistorySoRegressions(
  query: GetHistorySoRegressionsParams,
  options?: MethodOptions,
): Promise<SoRegressionItemBO[]> {
  return request(`${baseUrl}/api/v1/regression/getHistorySoRegressions`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetIntegrateRegressionItemsParams {
  /**
   * regressionId
   * @format int64
   */
  regressionId: number;
  /** search */
  search?: string;
}
/**
 * No description
 * @tags RegressionSheet
 * @name GetIntegrateRegressionItems
 * @summary 获取以变更单为单行的回归列表数据
 * @request GET:/api/v1/regression/getIntegrateRegressionItems
 */
export async function getIntegrateRegressionItems(
  query: GetIntegrateRegressionItemsParams,
  options?: MethodOptions,
): Promise<IntegrateRegressionItemVO[]> {
  return request(`${baseUrl}/api/v1/regression/getIntegrateRegressionItems`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetNextRegressionPipelineInstanceIdParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name GetNextRegressionPipelineInstanceId
 * @summary 根据发布单id查询下一轮可以用来回归的构建id
 * @request GET:/api/v1/regression/getNextRegressionPipelineInstanceId
 */
export async function getNextRegressionPipelineInstanceId(
  query: GetNextRegressionPipelineInstanceIdParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/regression/getNextRegressionPipelineInstanceId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRegressionDetailParams {
  /**
   * regressionId
   * @format int64
   */
  regressionId: number;
}
/**
 * No description
 * @tags RegressionSheet, RegressionSheet
 * @name GetRegressionDetail
 * @summary 查询回归单详情
 * @request GET:/api/v1/regression/getRegressionDetail
 */
export async function getRegressionDetail(
  query: GetRegressionDetailParams,
  options?: MethodOptions,
): Promise<RegressionVO> {
  return request(`${baseUrl}/api/v1/regression/getRegressionDetail`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRegressionFilesParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name GetRegressionFiles
 * @summary 获取发布单中的所有回归包
 * @request GET:/api/v1/regression/getRegressionFiles
 */
export async function getRegressionFiles(
  query: GetRegressionFilesParams,
  options?: MethodOptions,
): Promise<RegressionFileInfo[]> {
  return request(`${baseUrl}/api/v1/regression/getRegressionFiles`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRegressionItemsParams {
  /**
   * regressionId
   * @format int64
   */
  regressionId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name GetRegressionItems
 * @summary 获取回归列表----以模块为单行数据
 * @request GET:/api/v1/regression/getRegressionItems
 */
export async function getRegressionItems(
  query: GetRegressionItemsParams,
  options?: MethodOptions,
): Promise<RegressionItemVO[]> {
  return request(`${baseUrl}/api/v1/regression/getRegressionItems`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRegressionListByReleaseIdParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name GetRegressionListByReleaseId
 * @summary 根据发布单id查询回归单列表
 * @request GET:/api/v1/regression/getRegressionListByReleaseId
 */
export async function getRegressionListByReleaseId(
  query: GetRegressionListByReleaseIdParams,
  options?: MethodOptions,
): Promise<Regression[]> {
  return request(`${baseUrl}/api/v1/regression/getRegressionListByReleaseId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRemoteSoAlterRegressionsParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name GetRemoteSoAlterRegressions
 * @summary 获取远端化so信息，目前只有手淘使用
 * @request GET:/api/v1/regression/getRemoteSoAlterRegressions
 */
export async function getRemoteSoAlterRegressions(
  query: GetRemoteSoAlterRegressionsParams,
  options?: MethodOptions,
): Promise<SoRegressionItemBO[]> {
  return request(`${baseUrl}/api/v1/regression/getRemoteSoAlterRegressions`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface InitInsightGroupParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /** empId */
  empId: string;
}
/**
 * No description
 * @tags RegressionSheet
 * @name InitInsightGroup
 * @summary initInsightGroup
 * @request GET:/api/v1/regression/initInsightGroup
 */
export async function initInsightGroup(query: InitInsightGroupParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/regression/initInsightGroup`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface NotifyUserToRegressionParams {
  /**
   * regressionItemId
   * @format int64
   */
  regressionItemId?: number;
  /**
   * regressionId
   * @format int64
   */
  regressionId?: number;
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name NotifyUserToRegression
 * @summary 通知用户回归
 * @request PUT:/api/v1/regression/notifyUserToRegression
 */
export async function notifyUserToRegression(
  query: NotifyUserToRegressionParams,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/regression/notifyUserToRegression`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface NotifyUserToRegressionByAlterSheetIdParams {
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId?: number;
  /**
   * regressionItemId
   * @format int64
   */
  regressionItemId?: number;
  /**
   * regressionId
   * @format int64
   */
  regressionId: number;
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /** notifyType */
  notifyType: 'DINGDING_BOSS' | 'DINGDING_MSG' | 'DINGDING_MSG_BOSS' | 'DINGDING_PHONE';
}
/**
 * No description
 * @tags RegressionSheet
 * @name NotifyUserToRegressionByAlterSheetId
 * @summary 通知用户回归-根据变更单ID来通知
 * @request PUT:/api/v1/regression/notifyUserToRegressionByAlterSheetId
 */
export async function notifyUserToRegressionByAlterSheetId(
  query: NotifyUserToRegressionByAlterSheetIdParams,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/regression/notifyUserToRegressionByAlterSheetId`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface StartNewRegressionParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /**
   * stepId
   * @format int64
   */
  stepId?: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name StartNewRegression
 * @summary 发起新一轮回归
 * @request PUT:/api/v1/regression/startNewRegression
 */
export async function startNewRegression(query: StartNewRegressionParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/regression/startNewRegression`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface TransferRegressionUserParams {
  /**
   * regressionId
   * @format int64
   */
  regressionId: number;
  /**
   * regressionItemId
   * @format int64
   */
  regressionItemId?: number;
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId?: number;
  /** referrer */
  referrer: string;
}
/**
 * No description
 * @tags RegressionSheet
 * @name TransferRegressionUser
 * @summary 修改回归人
 * @request PUT:/api/v1/regression/transferRegressionUser
 */
export async function transferRegressionUser(
  query: TransferRegressionUserParams,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/regression/transferRegressionUser`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface UpdateRegressionItemStatusParams {
  /**
   * regressionItemId
   * @format int64
   */
  regressionItemId: number;
  /** regressionItemStatus */
  regressionItemStatus: 'FAIL' | 'INIT' | 'ONGOING' | 'SUCCESS';
  /**
   * integrateSheetId
   * @format int64
   */
  integrateSheetId: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name UpdateRegressionItemStatus
 * @summary 更新回归项状态
 * @request PUT:/api/v1/regression/updateRegressionItemStatus
 */
export async function updateRegressionItemStatus(
  query: UpdateRegressionItemStatusParams,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/regression/updateRegressionItemStatus`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface UpdateRegressionItemStatusByAlterSheetIdParams {
  /** regressionItemStatus */
  regressionItemStatus: 'FAIL' | 'INIT' | 'ONGOING' | 'SUCCESS';
  /**
   * regressionId
   * @format int64
   */
  regressionId: number;
  /**
   * regressionItemId
   * @format int64
   */
  regressionItemId?: number;
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId?: number;
}
/**
 * No description
 * @tags RegressionSheet
 * @name UpdateRegressionItemStatusByAlterSheetId
 * @summary 更新回归项状态-根据变更单ID更新所有的集成模块回归状态
 * @request PUT:/api/v1/regression/updateRegressionItemStatusByAlterSheetId
 */
export async function updateRegressionItemStatusByAlterSheetId(
  query: UpdateRegressionItemStatusByAlterSheetIdParams,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/regression/updateRegressionItemStatusByAlterSheetId`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface UpdateRegressionStatusParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /**
   * regressionId
   * @format int64
   */
  regressionId: number;
  /** regressionStatus */
  regressionStatus: 'REGRESSION_FAIL' | 'REGRESSION_ONGOING' | 'REGRESSION_PASS';
  /** force */
  force?: boolean;
}
/**
 * No description
 * @tags RegressionSheet
 * @name UpdateRegressionStatus
 * @summary 更新回归单状态
 * @request PUT:/api/v1/regression/updateRegressionStatus
 */
export async function updateRegressionStatus(
  query: UpdateRegressionStatusParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/regression/updateRegressionStatus`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}
