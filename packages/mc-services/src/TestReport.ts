/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { ReportEmail, TestReport, TestReportQuery } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface CreateTestReportParams {
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /**
   * entityId
   * @format int64
   */
  entityId: number;
}
/**
 * No description
 * @tags TestReport
 * @name CreateTestReport
 * @summary createTestReport
 * @request POST:/api/v1/report/test/createTestReport
 */
export async function createTestReport(
  query: CreateTestReportParams,
  data: TestReport,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/report/test/createTestReport`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface DeleteTestReportParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags TestReport
 * @name DeleteTestReport
 * @summary deleteTestReport
 * @request POST:/api/v1/report/test/deleteTestReport
 */
export async function deleteTestReport(query: DeleteTestReportParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/report/test/deleteTestReport`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface FindTestReportByEntityParams {
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /**
   * entityId
   * @format int64
   */
  entityId: number;
}
/**
 * No description
 * @tags TestReport
 * @name FindTestReportByEntity
 * @summary findTestReportByEntity
 * @request POST:/api/v1/report/test/findTestReportByEntity
 */
export async function findTestReportByEntity(
  query: FindTestReportByEntityParams,
  options?: MethodOptions,
): Promise<TestReport> {
  return request(`${baseUrl}/api/v1/report/test/findTestReportByEntity`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface GenTestReportEmailIframeParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags TestReport
 * @name GenTestReportEmailIframe
 * @summary genTestReportEmailIframe
 * @request GET:/api/v1/report/test/genTestReportEmailIframe
 */
export async function genTestReportEmailIframe(
  query: GenTestReportEmailIframeParams,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/report/test/genTestReportEmailIframe`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GenTestReportEmailVelocityParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags TestReport
 * @name GenTestReportEmailVelocity
 * @summary genTestReportEmailVelocity
 * @request POST:/api/v1/report/test/genTestReportEmailVelocity
 */
export async function genTestReportEmailVelocity(
  query: GenTestReportEmailVelocityParams,
  options?: MethodOptions,
): Promise<string> {
  return request(`${baseUrl}/api/v1/report/test/genTestReportEmailVelocity`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags TestReport
 * @name QueryTestReport
 * @summary queryTestReport
 * @request POST:/api/v1/report/test/queryTestReport
 */
export async function queryTestReport(data: TestReportQuery, options?: MethodOptions): Promise<TestReport[]> {
  return request(`${baseUrl}/api/v1/report/test/queryTestReport`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags TestReport
 * @name SendEmail
 * @summary sendEmail
 * @request POST:/api/v1/report/test/sendEmail
 */
export async function sendEmail(data: ReportEmail, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/report/test/sendEmail`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags TestReport
 * @name UpdateTestReport
 * @summary updateTestReport
 * @request POST:/api/v1/report/test/updateTestReport
 */
export async function updateTestReport(data: TestReport, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/report/test/updateTestReport`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
