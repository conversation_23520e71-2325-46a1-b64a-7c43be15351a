/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  ChangeDependencyReq,
  Dependency,
  PatchChangeRequest,
  PatchChangeRequestCreate,
  PatchChangeRequestUpdate,
  PatchRegressionInfo,
  Pipeline,
  PipelineExecuteParams,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags PatchChangeRequest
 * @name AddChangeDependency
 * @summary addChangeDependency
 * @request POST:/api/v1/patch/cr/addChangeDependency
 */
export async function addChangeDependency(data: ChangeDependencyReq, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cr/addChangeDependency`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface ArtifactDeployParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name ArtifactDeploy
 * @summary artifactDeploy
 * @request POST:/api/v1/patch/cr/artifactDeploy
 */
export async function artifactDeploy(
  query: ArtifactDeployParams,
  data: PipelineExecuteParams[],
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cr/artifactDeploy`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface CheckIntegrationDependenciesChangedParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name CheckIntegrationDependenciesChanged
 * @summary checkIntegrationDependenciesChanged
 * @request GET:/api/v1/patch/cr/checkIntegrationDependenciesChanged
 */
export async function checkIntegrationDependenciesChanged(
  query: CheckIntegrationDependenciesChangedParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cr/checkIntegrationDependenciesChanged`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ConfirmTestPassParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** isPass */
  isPass: boolean;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name ConfirmTestPass
 * @summary confirmTestPass
 * @request GET:/api/v1/patch/cr/confirmTest
 */
export async function confirmTestPass(query: ConfirmTestPassParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cr/confirmTest`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags PatchChangeRequest
 * @name Create
 * @summary create
 * @request POST:/api/v1/patch/cr/create
 */
export async function create(data: PatchChangeRequestCreate, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/patch/cr/create`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface BuildParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name Build
 * @summary build
 * @request POST:/api/v1/patch/cr/execute
 */
export async function build(
  query: BuildParams,
  data: PipelineExecuteParams[],
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/patch/cr/execute`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface FindByPublishAreaIdParams {
  /**
   * publishAreaId
   * @format int64
   */
  publishAreaId: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name FindByPublishAreaId
 * @summary findByPublishAreaId
 * @request GET:/api/v1/patch/cr/findByPublishAreaId
 */
export async function findByPublishAreaId(
  query: FindByPublishAreaIdParams,
  options?: MethodOptions,
): Promise<PatchChangeRequest[]> {
  return request(`${baseUrl}/api/v1/patch/cr/findByPublishAreaId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindPipelineBySceneTypeParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** scope */
  scope: string;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name FindPipelineBySceneType
 * @summary findPipelineBySceneType
 * @request GET:/api/v1/patch/cr/findPipelineBySceneType
 */
export async function findPipelineBySceneType(
  query: FindPipelineBySceneTypeParams,
  options?: MethodOptions,
): Promise<Pipeline[]> {
  return request(`${baseUrl}/api/v1/patch/cr/findPipelineBySceneType`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetByPassPatchInfoParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name GetByPassPatchInfo
 * @summary getByPassPatchInfo
 * @request GET:/api/v1/patch/cr/getByPassPatchInfo
 */
export async function getByPassPatchInfo(query: GetByPassPatchInfoParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/cr/getByPassPatchInfo`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetDependenciesParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name GetDependencies
 * @summary getDependencies
 * @request GET:/api/v1/patch/cr/getDependencies
 */
export async function getDependencies(query: GetDependenciesParams, options?: MethodOptions): Promise<Dependency[]> {
  return request(`${baseUrl}/api/v1/patch/cr/getDependencies`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPatchInfoParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name GetPatchInfo
 * @summary getPatchInfo
 * @request GET:/api/v1/patch/cr/getPatchInfo
 */
export async function getPatchInfo(query: GetPatchInfoParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/cr/getPatchInfo`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPatchInfoForMtl3Params {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name GetPatchInfoForMtl3
 * @summary getPatchInfoForMtl3
 * @request GET:/api/v1/patch/cr/getPatchInfoForMtl3
 */
export async function getPatchInfoForMtl3(query: GetPatchInfoForMtl3Params, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/cr/getPatchInfoForMtl3`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRegressionInfoParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name GetRegressionInfo
 * @summary getRegressionInfo
 * @request GET:/api/v1/patch/cr/getRegressionInfo
 */
export async function getRegressionInfo(
  query: GetRegressionInfoParams,
  options?: MethodOptions,
): Promise<PatchRegressionInfo> {
  return request(`${baseUrl}/api/v1/patch/cr/getRegressionInfo`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface RemoveChangeDependencyParams {
  /**
   * dependencyId
   * @format int64
   */
  dependencyId: number;
  /**
   * crId
   * @format int64
   */
  crId: number;
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name RemoveChangeDependency
 * @summary removeChangeDependency
 * @request GET:/api/v1/patch/cr/removeChangeDependency
 */
export async function removeChangeDependency(
  query: RemoveChangeDependencyParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cr/removeChangeDependency`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags PatchChangeRequest
 * @name Update
 * @summary update
 * @request POST:/api/v1/patch/cr/update
 */
export async function update(data: PatchChangeRequestUpdate, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cr/update`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags PatchChangeRequest
 * @name UpdateChangeDependency
 * @summary updateChangeDependency
 * @request POST:/api/v1/patch/cr/updateChangeDependency
 */
export async function updateChangeDependency(data: ChangeDependencyReq, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cr/updateChangeDependency`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface UpdateStatusParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** status */
  status: 'BUILD_ING' | 'FINISHED' | 'INIT' | 'PUBLISH_ING';
}
/**
 * No description
 * @tags PatchChangeRequest
 * @name UpdateStatus
 * @summary updateStatus
 * @request GET:/api/v1/patch/cr/updateStatus
 */
export async function updateStatus(query: UpdateStatusParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/cr/updateStatus`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
