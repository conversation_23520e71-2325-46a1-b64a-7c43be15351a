/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { ApplicationProfile } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags AppProfile
 * @name AddRelationProfile
 * @summary 客户端关联profile
 * @request PUT:/api/v1/app/{appId}/profile/{profileId}
 */
export async function addRelationProfile(
  appId: number,
  profileId: number,
  options?: MethodOptions,
): Promise<ApplicationProfile> {
  return request(`${baseUrl}/api/v1/app/${appId}/profile/${profileId}`, {
    method: 'PUT',
    ...options,
  });
}

/**
 * No description
 * @tags AppProfile
 * @name DeleteRelationProfile
 * @summary 客户端取消关联profile
 * @request DELETE:/api/v1/app/{appId}/profile/{profileId}
 */
export async function deleteRelationProfile(
  appId: number,
  profileId: number,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/app/${appId}/profile/${profileId}`, {
    method: 'DELETE',
    ...options,
  });
}
