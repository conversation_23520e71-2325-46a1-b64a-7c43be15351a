/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PipelineAutoReorderResult } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface AutoReorderPipelineStagesParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags PipelineReorder
 * @name AutoReorderPipelineStages
 * @summary 流水线阶段自动编排
 * @request POST:/api/v1/pipeline/reorder/autoReorderPipelineStages
 */
export async function autoReorderPipelineStages(
  query: AutoReorderPipelineStagesParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/pipeline/reorder/autoReorderPipelineStages`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface GetAutoReorderResultParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags PipelineReorder
 * @name GetAutoReorderResult
 * @summary 获取流水线阶段自动编排结果
 * @request POST:/api/v1/pipeline/reorder/getAutoReorderResult
 */
export async function getAutoReorderResult(
  query: GetAutoReorderResultParams,
  options?: MethodOptions,
): Promise<PipelineAutoReorderResult> {
  return request(`${baseUrl}/api/v1/pipeline/reorder/getAutoReorderResult`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface IsSupportParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags PipelineReorder
 * @name IsSupport
 * @summary 是否支持自动编排
 * @request POST:/api/v1/pipeline/reorder/isSupport
 */
export async function isSupport(query: IsSupportParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/pipeline/reorder/isSupport`, {
    method: 'POST',
    params: query,
    ...options,
  });
}
