/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { MtlLinkAoneSubWorkitemVO } from './DataContracts';
import { getBaseUrl, request } from './HttpClient';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags MtlLinkAoneSubWorkItem
 * @name Create
 * @summary create
 * @request POST:/api/v1/link/aone/sub/workitem/create
 */
export async function create(data: MtlLinkAoneSubWorkitemVO): Promise<boolean> {
  return request(
    `${baseUrl}/api/v1/link/aone/sub/workitem/create`,
    {
      method: 'POST',
      body: data as any,
    },
    true,
    false,
    true,
  );
}

export interface FindByIdParams {
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId: number;
  /** workitemId */
  workitemId: string;
  /** empId */
  empId: string;
}
/**
 * No description
 * @tags MtlLinkAoneSubWorkItem
 * @name FindById
 * @summary findById
 * @request GET:/api/v1/link/aone/sub/workitem/findById
 */
export async function findById(query: FindByIdParams): Promise<MtlLinkAoneSubWorkitemVO> {
  return request(
    `${baseUrl}/api/v1/link/aone/sub/workitem/findById`,
    {
      method: 'GET',
      params: query,
    },
    true,
    false,
    true,
  );
}

/**
 * No description
 * @tags MtlLinkAoneSubWorkItem
 * @name GetStatusList
 * @summary getStatusList
 * @request GET:/api/v1/link/aone/sub/workitem/getStatusList
 */
export async function getStatusList(): Promise<string[]> {
  return request(
    `${baseUrl}/api/v1/link/aone/sub/workitem/getStatusList`,
    {
      method: 'GET',
    },
    true,
    false,
    true,
  );
}

export interface UpdateStatusParams {
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId: number;
  /** workitemId */
  workitemId: string;
  /** status */
  status: 'CANCEL' | 'DOING' | 'DONE' | 'TODO';
  /** empId */
  empId: string;
}
/**
 * No description
 * @tags MtlLinkAoneSubWorkItem
 * @name UpdateStatus
 * @summary updateStatus
 * @request GET:/api/v1/link/aone/sub/workitem/updateStatus
 */
export async function updateStatus(query: UpdateStatusParams): Promise<boolean> {
  return request(
    `${baseUrl}/api/v1/link/aone/sub/workitem/updateStatus`,
    {
      method: 'GET',
      params: query,
    },
    true,
    false,
    true,
  );
}
