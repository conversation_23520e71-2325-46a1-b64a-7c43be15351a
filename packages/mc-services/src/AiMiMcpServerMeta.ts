/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { McpServerMetaRequest, McpServerMetaVO, PageMcpServerMetaVO } from './AiMiMcpDataContracts';
import { getBaseUrl, MethodOptions, request } from './HttpClient';

const baseUrl = getBaseUrl('https://aimi-mcp.alibaba-inc.com');

export interface GetMcpServerMetaParams {
  /** @format int64 */
  id: number;
}
/**
 * No description
 * @tags AiMiMcpServerMeta
 * @name GetMcpServerMeta
 * @request GET:/mcp/api/v1/server/meta
 */
export async function getMcpServerMeta(
  query: GetMcpServerMetaParams,
  options?: MethodOptions,
): Promise<McpServerMetaVO> {
  return request(`${baseUrl}/mcp/api/v1/server/meta`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiMcpServerMeta
 * @name UpdateMcpServerMeta
 * @request PUT:/mcp/api/v1/server/meta
 */
export async function updateMcpServerMeta(data: McpServerMetaRequest, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/mcp/api/v1/server/meta`, {
    method: 'PUT',
    body: data as any,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiMcpServerMeta
 * @name SaveMcpServerMeta
 * @request POST:/mcp/api/v1/server/meta
 */
export async function saveMcpServerMeta(data: McpServerMetaRequest, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/mcp/api/v1/server/meta`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

export interface DeleteMcpServerMetaParams {
  /** @format int64 */
  id: number;
}
/**
 * No description
 * @tags AiMiMcpServerMeta
 * @name DeleteMcpServerMeta
 * @request DELETE:/mcp/api/v1/server/meta
 */
export async function deleteMcpServerMeta(query: DeleteMcpServerMetaParams, options?: MethodOptions): Promise<Boolean> {
  return request(`${baseUrl}/mcp/api/v1/server/meta`, {
    method: 'DELETE',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface CallMcpToolParams {
  /** @format int64 */
  id: number;
  /** @format int64 */
  toolMetaId: number;
  inputData: string;
}
/**
 * No description
 * @tags AiMiMcpServerMeta
 * @name CallMcpTool
 * @request POST:/mcp/api/v1/server/meta/callTool
 */
export async function callMcpTool(query: CallMcpToolParams, options?: MethodOptions): Promise<String> {
  return request(`${baseUrl}/mcp/api/v1/server/meta/callTool`, {
    method: 'POST',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface GetMcpServerMetasParams {
  name?: string;
  creator?: string;
  /** @format int64 */
  bizGroupId?: number;
  /**
   * @format int32
   * @default 0
   */
  pageNo?: number;
  /**
   * @format int32
   * @default 10
   */
  pageSize?: number;
}
/**
 * No description
 * @tags AiMiMcpServerMeta
 * @name GetMcpServerMetas
 * @request GET:/mcp/api/v1/server/meta/list
 */
export async function getMcpServerMetas(
  query?: GetMcpServerMetasParams,
  options?: MethodOptions,
): Promise<PageMcpServerMetaVO> {
  return request(`${baseUrl}/mcp/api/v1/server/meta/list`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}
