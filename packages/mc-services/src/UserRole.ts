/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { User, UserRole } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface FindIntegrateSheetForPageParams {
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /**
   * entityId
   * @format int64
   */
  entityId: number;
}
/**
 * No description
 * @tags UserRole
 * @name FindIntegrateSheetForPage
 * @summary 查询用户角色列表
 * @request GET:/api/v1/userRole
 */
export async function findIntegrateSheetForPage(
  query: FindIntegrateSheetForPageParams,
  options?: MethodOptions,
): Promise<UserRole[]> {
  return request(`${baseUrl}/api/v1/userRole`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface CreateIntegrateSheetParams {
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /**
   * entityId
   * @format int64
   */
  entityId: number;
  /** role */
  role:
    | 'ADMIN'
    | 'ARCH_WATCHMAN'
    | 'CC'
    | 'DEVELOPER'
    | 'MODULE_RELATION_AUDITOR'
    | 'NOTIFIER'
    | 'OWNER'
    | 'PLATFORM_WATCHMAN'
    | 'PMO_WATCHMAN'
    | 'RELEASE_OWNER'
    | 'TESTER'
    | 'TEST_OWNER'
    | 'TEST_WATCHMAN';
  /** userList */
  userList: string;
}
/**
 * No description
 * @tags UserRole
 * @name CreateIntegrateSheet
 * @summary 创建用户角色
 * @request POST:/api/v1/userRole
 */
export async function createIntegrateSheet(
  query: CreateIntegrateSheetParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/userRole`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface DeleteUserRoleParams {
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /**
   * entityId
   * @format int64
   */
  entityId: number;
}
/**
 * @description 请谨慎使用
 * @tags UserRole
 * @name DeleteUserRole
 * @summary 删除用户角色
 * @request POST:/api/v1/userRole/deleteUserRole
 */
export async function deleteUserRole(query: DeleteUserRoleParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/userRole/deleteUserRole`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface DeleteUserRoleByIdParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * @description 请谨慎使用
 * @tags UserRole
 * @name DeleteUserRoleById
 * @summary 根据主键删除用户角色
 * @request POST:/api/v1/userRole/deleteUserRoleById
 */
export async function deleteUserRoleById(query: DeleteUserRoleByIdParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/userRole/deleteUserRoleById`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface FindUserParams {
  /** empId */
  empId: string;
}
/**
 * No description
 * @tags UserRole
 * @name FindUser
 * @summary 查询用户
 * @request GET:/api/v1/userRole/findUserInfo
 */
export async function findUser(query: FindUserParams, options?: MethodOptions): Promise<User[]> {
  return request(`${baseUrl}/api/v1/userRole/findUserInfo`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
