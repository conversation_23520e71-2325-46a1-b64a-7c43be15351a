/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PluginConfigVO } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface RenderPluginConfigParams {
  /**
   * pluginVersionId
   * @format int64
   */
  pluginVersionId: number;
}
/**
 * No description
 * @tags RenderPluginConfig
 * @name RenderPluginConfig
 * @summary render plugin config
 * @request GET:/api/v1/plugin/config
 */
export async function renderPluginConfig(
  query: RenderPluginConfigParams,
  options?: MethodOptions,
): Promise<PluginConfigVO> {
  return request(`${baseUrl}/api/v1/plugin/config`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
