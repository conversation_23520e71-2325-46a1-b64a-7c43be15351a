/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  DocumentChunkConfigUpdateRequest,
  DocumentDownloadTaskCreateRequest,
  DocumentPageRequest,
  DocumentReloadRequest,
  DocumentSuggestionVO,
  DocumentVO,
  PageDocumentVO,
  TableDocumentPreviewRequest,
  TableDocumentSheetVO,
  TableDocumentVO,
} from './AiMiDataContracts';
import { getBaseUrl, MethodOptions } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://aimi.alibaba-inc.com');

export interface ModifyDocumentStatusParams {
  /** @format int64 */
  documentId: number;
  /** 文档处理状态 */
  status: 'PROCESSING' | 'ACTIVE' | 'ERROR' | 'REMOVED' | 'DISABLED';
}
/**
 * No description
 * @tags AiMiDocument
 * @name ModifyDocumentStatus
 * @summary 修改文档状态
 * @request PUT:/aimi/api/v1/document/status
 */
export async function modifyDocumentStatus(
  query: ModifyDocumentStatusParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/document/status`, {
    method: 'PUT',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiDocument
 * @name UpdateDocumentChunkConfig
 * @summary 更新文档分段配置
 * @request PUT:/aimi/api/v1/document/chunkConfig
 */
export async function updateDocumentChunkConfig(
  data: DocumentChunkConfigUpdateRequest,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/document/chunkConfig`, {
    method: 'PUT',
    body: data as any,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiDocument
 * @name PreviewTableContent
 * @summary 预览表格文档内容
 * @request POST:/aimi/api/v1/document/table/preview
 */
export async function previewTableContent(
  data: TableDocumentPreviewRequest,
  options?: MethodOptions,
): Promise<TableDocumentSheetVO> {
  return request(`${baseUrl}/aimi/api/v1/document/table/preview`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiDocument
 * @name ReloadDocument
 * @summary 更新文档
 * @request POST:/aimi/api/v1/document/reload
 */
export async function reloadDocument(data: DocumentReloadRequest, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/document/reload`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiDocument
 * @name CreateDownloadTask
 * @summary 创建下载任务
 * @request POST:/aimi/api/v1/document/createDownloadTask
 */
export async function createDownloadTask(
  data: DocumentDownloadTaskCreateRequest,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/document/createDownloadTask`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

export interface GetDocumentParams {
  /** @format int64 */
  documentId: number;
}
/**
 * No description
 * @tags AiMiDocument
 * @name GetDocument
 * @summary 获取文档详情
 * @request GET:/aimi/api/v1/document
 */
export async function getDocument(query: GetDocumentParams, options?: MethodOptions): Promise<DocumentVO> {
  return request(`${baseUrl}/aimi/api/v1/document`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface FetchTableContentParams {
  /** @format int64 */
  taskId: number;
}
/**
 * No description
 * @tags AiMiDocument
 * @name FetchTableContent
 * @summary 获取表格文档内容
 * @request GET:/aimi/api/v1/document/table/content
 */
export async function fetchTableContent(
  query: FetchTableContentParams,
  options?: MethodOptions,
): Promise<TableDocumentVO> {
  return request(`${baseUrl}/aimi/api/v1/document/table/content`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface GetCommentAndSuggestionParams {
  /** @format int64 */
  documentId: number;
}
/**
 * No description
 * @tags AiMiDocument
 * @name GetCommentAndSuggestion
 * @summary 获取文档优化建议
 * @request GET:/aimi/api/v1/document/suggestion
 */
export async function getCommentAndSuggestion(
  query: GetCommentAndSuggestionParams,
  options?: MethodOptions,
): Promise<DocumentSuggestionVO> {
  return request(`${baseUrl}/aimi/api/v1/document/suggestion`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface PageDocumentParams {
  /** 文档列表请求 */
  request: DocumentPageRequest;
}
/**
 * No description
 * @tags AiMiDocument
 * @name PageDocument
 * @summary 分页获取知识库中的文档列表
 * @request GET:/aimi/api/v1/document/page
 */
export async function pageDocument(query: PageDocumentParams, options?: MethodOptions): Promise<PageDocumentVO> {
  return request(`${baseUrl}/aimi/api/v1/document/page`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}
