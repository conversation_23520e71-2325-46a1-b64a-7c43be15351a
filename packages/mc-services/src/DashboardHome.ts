/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  AoneRequest,
  BuildHistoryPageVO,
  CheckReqInAlterSheetIntegrateVO,
  DashboardHomeAnnouncement,
  DashboardHomeCommonUseApp,
  DashboardHomeHelp,
  DashboardHomeMyDev,
  DashboardHomeMyOperation,
  DashboardHomeMyOps,
  DashboardHomeMyTask,
  DashboardHomeScrollGraph,
  DashboardHomeTop,
  IntegrateHistoryPageVO,
  TodoItem,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags DashboardHome
 * @name Announcement
 * @summary 公告数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function announcement(options?: MethodOptions): Promise<DashboardHomeAnnouncement> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}

export interface GetBuildHistoryParams {
  /**
   * 客户端的id
   * @format int64
   */
  applicationId?: number;
  /** 构建状态 */
  executeStatus?: string;
  /** 做构建的人的工号 */
  executor?: string;
  /** 迭代名称的查询条件 */
  keyword?: string;
  /**
   * 上一次数据最后一条的时间
   * @format int64
   */
  lastTime?: number;
  /**
   * 一次返回多少个
   * @format int64
   */
  pageSize?: number;
  /**
   * 0表示我的构建，1表示团队空间的构建
   * @format int64
   */
  scope?: number;
  /**
   * 迭代的类型, 0表示开启的迭代，1表示已完成的迭代
   * @format int64
   */
  type?: number;
}
/**
 * No description
 * @tags DashboardHome
 * @name GetBuildHistory
 * @summary 构建记录
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function getBuildHistory(
  query?: GetBuildHistoryParams,
  options?: MethodOptions,
): Promise<BuildHistoryPageVO> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name CheckReqInAlterSheetIntegrate
 * @summary checkReqInAlterSheetIntegrate
 * @request POST:/api/v1/dashboard/home/<USER>
 */
export async function checkReqInAlterSheetIntegrate(
  data: CheckReqInAlterSheetIntegrateVO[],
  options?: MethodOptions,
): Promise<Record<string, boolean>> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name CommonUseApp
 * @summary 常用APP数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function commonUseApp(options?: MethodOptions): Promise<DashboardHomeCommonUseApp> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}

export interface GetAlterSheetIntegrateIdsParams {
  /** ids */
  ids?: string;
}
/**
 * No description
 * @tags DashboardHome
 * @name GetAlterSheetIntegrateIds
 * @summary getAlterSheetIntegrateIds
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function getAlterSheetIntegrateIds(
  query?: GetAlterSheetIntegrateIdsParams,
  options?: MethodOptions,
): Promise<Record<string, object>[]> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetReqTypeParams {
  /** ids */
  ids?: string;
}
/**
 * No description
 * @tags DashboardHome
 * @name GetReqType
 * @summary getReqType
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function getReqType(
  query?: GetReqTypeParams,
  options?: MethodOptions,
): Promise<Record<number, AoneRequest>> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name Help
 * @summary 帮助数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function help(options?: MethodOptions): Promise<DashboardHomeHelp> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}

export interface GetIntegrateHistoryParams {
  /** 查询条件 */
  keyword?: string;
  /**
   * 上一次数据最后一条的时间
   * @format int64
   */
  lastTime?: number;
  /**
   * 一次返回多少个
   * @format int64
   */
  pageSize?: number;
  /** 发布状态 */
  publishType?: any;
  /**
   * 0表示我的集成，1表示团队空间的集成
   * @format int64
   */
  scope?: number;
  /**
   * 版本计划
   * @format int64
   */
  versionId?: number;
}
/**
 * No description
 * @tags DashboardHome
 * @name GetIntegrateHistory
 * @summary 集成记录
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function getIntegrateHistory(
  query?: GetIntegrateHistoryParams,
  options?: MethodOptions,
): Promise<IntegrateHistoryPageVO> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface MyDevParams {
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
}
/**
 * No description
 * @tags DashboardHome
 * @name MyDev
 * @summary MyDev数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function myDev(query: MyDevParams, options?: MethodOptions): Promise<DashboardHomeMyDev> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name MyOperation
 * @summary MyOperation数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function myOperation(options?: MethodOptions): Promise<DashboardHomeMyOperation> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}

export interface MyOpsParams {
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
}
/**
 * No description
 * @tags DashboardHome
 * @name MyOps
 * @summary MyOps数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function myOps(query: MyOpsParams, options?: MethodOptions): Promise<DashboardHomeMyOps> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name MyTask
 * @summary MyTask数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function myTask(options?: MethodOptions): Promise<DashboardHomeMyTask> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name MyUniAppOperation
 * @summary MyOperation数据-uniapp
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function myUniAppOperation(options?: MethodOptions): Promise<DashboardHomeMyOperation> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name GetDashboardRequestResult
 * @summary getDashboardRequestResult
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function getDashboardRequestResult(options?: MethodOptions): Promise<Record<string, object>[]> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name ScrollGraph
 * @summary 常用APP数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function scrollGraph(options?: MethodOptions): Promise<DashboardHomeScrollGraph> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}

export interface TodosParams {
  /** status */
  status?: 'PROCESSED' | 'UNPROCESSED';
  /** handleByMe */
  handleByMe: boolean;
  /** createByMe */
  createByMe: boolean;
  /**
   * spaceId
   * @format int64
   */
  spaceId?: number;
  /** userId */
  userId?: string;
}
/**
 * No description
 * @tags DashboardHome
 * @name Todos
 * @summary 待办列表
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function todos(query: TodosParams, options?: MethodOptions): Promise<TodoItem[]> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags DashboardHome
 * @name Top
 * @summary 顶部统计数据
 * @request GET:/api/v1/dashboard/home/<USER>
 */
export async function top(options?: MethodOptions): Promise<DashboardHomeTop> {
  return request(`${baseUrl}/api/v1/dashboard/home/<USER>
    method: 'GET',
    ...options,
  });
}
