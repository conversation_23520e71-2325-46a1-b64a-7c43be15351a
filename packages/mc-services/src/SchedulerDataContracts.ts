/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** AfterFinishJobFinishedRequest */
export interface AfterFinishJobFinishedRequest {
  /** @format int64 */
  finishTime?: number;
  jobResult?: JobResult;
  jobScheduleId?: string;
}

/** AfterFinishJobFinishedResult */
export type AfterFinishJobFinishedResult = object;

/** AfterFinishJobStartedRequest */
export interface AfterFinishJobStartedRequest {
  jobScheduleId?: string;
  /** @format int64 */
  startTime?: number;
}

/** AfterFinishJobStartedResult */
export type AfterFinishJobStartedResult = object;

/** AgentAuthKey */
export interface AgentAuthKey {
  authKey?: string;
  publicKey?: string;
}

/** AppendUploadCompleteRequest */
export interface AppendUploadCompleteRequest {
  fileMetadata?: Record<string, string>;
  storageObjectMetadata?: Record<string, string>;
  tokenId?: string;
  uploadId?: string;
}

/** AppendUploadCompleteResult */
export interface AppendUploadCompleteResult {
  uploadId?: string;
}

/** AppendUploadRequest */
export interface AppendUploadRequest {
  fileInfo?: FileInfo;
  storageObjectInfo?: StorageObjectInfo;
  tokenId?: string;
  uploadId?: string;
}

/** AppendUploadResult */
export interface AppendUploadResult {
  uploadId?: string;
}

/** ApplyArtifactUploadRequest */
export interface ApplyArtifactUploadRequest {
  artifactInfos?: ArtifactInfo[];
  jobScheduleId?: string;
  storageObjectKeys?: string[];
  workspacePath?: string;
}

/** ApplyArtifactUploadResult */
export interface ApplyArtifactUploadResult {
  uploadTokens?: Record<string, UploadToken>;
}

/** ApplyLogUploadRequest */
export interface ApplyLogUploadRequest {
  jobScheduleId?: string;
  logInfos?: LogInfo[];
  workspacePath?: string;
}

/** ApplyLogUploadResult */
export interface ApplyLogUploadResult {
  uploadTokens?: Record<string, UploadToken>;
}

/** ArtifactInfo */
export interface ArtifactInfo {
  artifactId?: string;
  artifactMetadata?: Record<string, string>;
  artifactName?: string;
  artifactType?: string;
  fileInfo?: FileInfo;
}

/** ArtifactMetadata */
export interface ArtifactMetadata {
  bundleIdentifier?: string;
  bundleVersion?: string;
  mainArtifact?: boolean;
  title?: string;
}

/** ArtifactUploadedRequest */
export interface ArtifactUploadedRequest {
  artifactInfo?: ArtifactInfo;
  jobScheduleId?: string;
  uploadId?: string;
}

/** ArtifactUploadedResult */
export interface ArtifactUploadedResult {
  artifactId?: string;
  downloadUri?: string;
}

/** BuildErrorRootCauseInfo */
export interface BuildErrorRootCauseInfo {
  coordinators?: string;
  rootCause?: string;
}

/** Environment */
export interface Environment {
  dockerImage?: string;
  envType?: 'CLOUD' | 'HYBRID' | 'NATIVE';
  envYml?: string;
  os?: string;
  osVersion?: string;
  /** @uniqueItems true */
  tags?: string[];
  xcodeVersion?: string;
}

/** FileInfo */
export interface FileInfo {
  fileMetadata?: Record<string, string>;
  filename?: string;
}

/** FileMetadata */
export interface FileMetadata {
  fileMD5?: string;
  fileName?: string;
  /** @format int32 */
  fileSize?: number;
  fileType?: string;
}

/** InputStream */
export type InputStream = object;

/** Job */
export interface Job {
  definitionId?: string;
  jobCondition?: JobCondition;
  jobDTO?: JobDTO;
  jobPolicy?: JobPolicy;
  /** @uniqueItems true */
  jobRefQueueIds?: JobRefQueueId[];
  /** @format int64 */
  jobScheduleId?: number;
}

/** JobArtifact */
export interface JobArtifact {
  artifactId?: string;
  artifactMetadata?: Record<string, string>;
  artifactMetadataObj?: ArtifactMetadata;
  artifactName?: string;
  artifactType?: string;
  creator?: string;
  extension?: string;
  fileMetadata?: Record<string, string>;
  fileMetadataObj?: FileMetadata;
  filename?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  /** @format int64 */
  jobScheduleId?: number;
  modifier?: string;
  uploadId?: string;
}

/** JobCacheSummary */
export interface JobCacheSummary {
  /** @format int64 */
  jobCacheSize?: number;
  jobCacheTop100?: Job[];
}

/** JobCondition */
export interface JobCondition {
  targetCondition?: TargetCondition;
}

/** JobDTO */
export interface JobDTO {
  isStopJob?: boolean;
  pipelineJobInstance?: PipelineJobInstance;
}

/** JobExt */
export interface JobExt {
  jobDTO?: JobDTO;
  jobScheduleId?: string;
}

/** JobFinishedRequest */
export interface JobFinishedRequest {
  /** @format int64 */
  finishTime?: number;
  jobResult?: JobResult;
  jobScheduleId?: string;
}

/** JobFinishedResult */
export type JobFinishedResult = object;

/** JobPolicy */
export interface JobPolicy {
  /** @format int64 */
  priorityLevel?: number;
  /** @format int64 */
  timeoutSec?: number;
}

/** JobPreparedRequest */
export interface JobPreparedRequest {
  jobResult?: JobResult;
  jobScheduleId?: string;
  /** @format int64 */
  preparedTime?: number;
}

/** JobPreparedResult */
export type JobPreparedResult = object;

/** JobRef */
export interface JobRef {
  jobCondition?: JobCondition;
  /** @format int64 */
  jobScheduleId?: number;
}

/** JobRefQueueId */
export interface JobRefQueueId {
  queueName?: string;
  queueType?: 'AGENT_JOB_REF_QUEUE' | 'SERVERLESS_JOB_REF_QUEUE' | 'STOP_JOB_REF_QUEUE' | 'WORKER_JOB_REF_QUEUE';
}

/** JobRefSummary */
export interface JobRefSummary {
  jobRefName?: string;
  jobRefSize?: Record<string, number>;
  jobRefTop100?: Record<string, JobRef[]>;
}

/** JobResult */
export interface JobResult {
  envVariables?: Record<string, string>;
  status?:
    | 'CANCELLED'
    | 'FAILED'
    | 'RUNNING'
    | 'SKIPPED'
    | 'SKIP_SUCCEEDED'
    | 'SUCCEEDED'
    | 'TIMEOUT'
    | 'WAITING'
    | 'WAITING_TO_SKIP';
}

/** JobRunnerFinishedRequest */
export interface JobRunnerFinishedRequest {
  jobResult?: JobResult;
  jobScheduleId?: string;
  /** @format int64 */
  runnerFinishTime?: number;
}

/** JobRunnerFinishedResult */
export type JobRunnerFinishedResult = object;

/** JobRunnerStartedRequest */
export interface JobRunnerStartedRequest {
  jobResult?: JobResult;
  jobScheduleId?: string;
  /** @format int64 */
  runnerStartTime?: number;
  runnerType?: string;
}

/** JobRunnerStartedResult */
export type JobRunnerStartedResult = object;

/** JobSchedule */
export interface JobSchedule {
  creator?: string;
  definitionId?: string;
  /** @format int64 */
  fetchTime?: number;
  /** @format int64 */
  finishTime?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  jobCondition?: JobCondition;
  jobDTO?: JobDTO;
  jobPolicy?: JobPolicy;
  jobResult?: JobResult;
  /** @format int64 */
  machineId?: number;
  modifier?: string;
  /** @format int64 */
  prepareTime?: number;
  /** @format int64 */
  runnerFinishTime?: number;
  /** @format int64 */
  runnerStartTime?: number;
  runnerType?: string;
  /** @format int64 */
  startTime?: number;
  status?: 'FETCHED' | 'FINISHED' | 'STARTED' | 'SUBMITED' | 'TIMEOUTED';
  /** @format int64 */
  submitTime?: number;
  /** @format int64 */
  timeoutTime?: number;
  /** @format int64 */
  workerId?: number;
}

/** JobStartedRequest */
export interface JobStartedRequest {
  jobScheduleId?: string;
  /** @format int64 */
  startTime?: number;
}

/** JobStartedResult */
export type JobStartedResult = object;

/** JobSummary */
export interface JobSummary {
  agentJobRefSummary?: JobRefSummary;
  jobCacheSummary?: JobCacheSummary;
  serverlessJobRefSummary?: JobRefSummary;
  workerJobRefSummary?: JobRefSummary;
  workerJobs?: Record<string, string>;
}

/** JobTask */
export interface JobTask {
  creator?: string;
  /** @format int64 */
  downloadFinishTime?: number;
  /** @format int64 */
  downloadStartTime?: number;
  /** @format int64 */
  finishTime?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  /** @format int64 */
  installFinishTime?: number;
  /** @format int64 */
  installStartTime?: number;
  /** @format int64 */
  jobScheduleId?: number;
  /** @format int64 */
  loadFinishTime?: number;
  /** @format int64 */
  loadStartTime?: number;
  modifier?: string;
  /** @format int64 */
  startTime?: number;
  taskDisplayName?: string;
  taskId?: string;
  taskLog?: JobTaskLog;
  taskName?: string;
  taskResult?: TaskResult;
  taskStatus?: 'FINISHED' | 'PREPARING' | 'STARTED' | 'WAITING';
}

/** JobTaskLog */
export interface JobTaskLog {
  jobScheduleId?: string;
  logContent?: string;
  logFileMetadata?: Record<string, string>;
  logFilename?: string;
  logMetadata?: Record<string, string>;
  logName?: string;
  logUploadId?: string;
}

/** LLMChatInfoDTO */
export interface LLMChatInfoDTO {
  answer?: string;
  question?: string;
}

/** LogAnalyzeConclusion */
export interface LogAnalyzeConclusion {
  canShow?: boolean;
  errorSolutionChatInfo?: LLMChatInfoDTO;
  matchKnowledgeList?: LogKnowledgeMatchItem[];
}

/** LogAnalyzeResultDTO */
export interface LogAnalyzeResultDTO {
  /** @format date-time */
  analyzeEndTime?: string;
  analyzeMachineIp?: string;
  analyzeParams?: Record<string, string>;
  analyzeResultConclusion?: LogAnalyzeConclusion;
  /** @format date-time */
  analyzeStartTime?: string;
  analyzeType?: 'ERROR_BUILD_ANALYZE' | 'LOG_GROUP_AGENTS';
  errorDistributeFlag?: 'CORRECT' | 'ERROR' | 'WAIT_CONFIRM';
  errorFixDetail?: string;
  /** @format int64 */
  errorTaskInstanceId?: number;
  errorTaskUuid?: string;
  extra?: object;
  fixStatus?: 'AUTO_FIX_FAIL' | 'AUTO_FIX_SUCCESS' | 'FIXING' | 'UNFIXED';
  invokeUser?: string;
  /** @format int64 */
  jobScheduleId?: number;
  keyErrorLogFileFullPath?: string;
  keyLogExtraInfo?: Record<string, string>;
  logFeatureFileFullPath?: string;
  logStructuredFileFullPath?: string;
  logTemplatesFileFullPath?: string;
  /** @format int64 */
  matchKnowledgeItemId?: number;
  originLogUploadId?: string;
  rootCause?: 'ARCHITECTURAL_ISSUE' | 'BIZ_ISSUE' | 'DEPENDENCY_ISSUE' | 'EXTERNAL_ISSUE' | 'PLATFORM_ISSUE';
  rootCauseDetail?: BuildErrorRootCauseInfo;
  sceneInfo?: LogAnalyzeSceneInfo;
  /** @format int64 */
  trainModelId?: number;
}

/** LogAnalyzeResultQuery */
export interface LogAnalyzeResultQuery {
  analyzeMachineIp?: string;
  analyzeType?: string;
  /** @format int64 */
  applicationId?: number;
  errorDistributeFlagList?: ('CORRECT' | 'ERROR' | 'WAIT_CONFIRM')[];
  errorFixDetail?: string;
  fixStatus?: 'AUTO_FIX_FAIL' | 'AUTO_FIX_SUCCESS' | 'FIXING' | 'UNFIXED';
  /** @format date-time */
  gmtCreateEnd?: string;
  /** @format date-time */
  gmtCreateStart?: string;
  invokeUser?: string;
  /** @format int64 */
  matchKnowledgeItemId?: number;
  /** @format int64 */
  relatedApplicationId?: number;
  rootCauseList?: ('ARCHITECTURAL_ISSUE' | 'BIZ_ISSUE' | 'DEPENDENCY_ISSUE' | 'EXTERNAL_ISSUE' | 'PLATFORM_ISSUE')[];
  sceneInfo?: LogAnalyzeSceneInfo;
  selectNoErrorDistributeFlag?: boolean;
  selectNoRootCause?: boolean;
  selectNotNullRootCause?: boolean;
}

/** LogAnalyzeResultVO */
export interface LogAnalyzeResultVO {
  analyzeContext?: object;
  /** @format date-time */
  analyzeEndTime?: string;
  analyzeMachineIp?: string;
  analyzeParams?: Record<string, string>;
  analyzeResultConclusion?: LogAnalyzeConclusion;
  /** @format date-time */
  analyzeStartTime?: string;
  analyzeType?: 'ERROR_BUILD_ANALYZE' | 'LOG_GROUP_AGENTS';
  bestMatchKnowledgeItem?: LogKnowledgeMatchItem;
  creator?: string;
  errorDistributeFlag?: 'CORRECT' | 'ERROR' | 'WAIT_CONFIRM';
  errorFixDetail?: string;
  extra?: object;
  fixStatus?: 'AUTO_FIX_FAIL' | 'AUTO_FIX_SUCCESS' | 'FIXING' | 'UNFIXED';
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  invokeUser?: string;
  keyErrorLogDownloadUrl?: string;
  keyLogExtraInfo?: Record<string, string>;
  keyLogMetadata?: Record<string, string>;
  keyLogUploadId?: string;
  /** @format int64 */
  logAnalyzeResultId?: number;
  logContent?: LogContent0;
  logFeatureUploadId?: string;
  logStructuredUploadId?: string;
  logTemplatesUploadId?: string;
  /** @format int64 */
  matchKnowledgeItemId?: number;
  modifier?: string;
  originLogMetadata?: Record<string, string>;
  originLogUploadId?: string;
  /** @format int64 */
  relatedEntityId?: number;
  relatedEntityType?: 'PIPELINE_TASK_INSTANCE';
  rootCause?: 'ARCHITECTURAL_ISSUE' | 'BIZ_ISSUE' | 'DEPENDENCY_ISSUE' | 'EXTERNAL_ISSUE' | 'PLATFORM_ISSUE';
  rootCauseDetail?: BuildErrorRootCauseInfo;
  sceneInfo?: LogAnalyzeSceneInfo;
  /** @format int64 */
  trainModelId?: number;
}

/** LogAnalyzeSceneInfo */
export interface LogAnalyzeSceneInfo {
  buildUser?: string;
  /** @format int64 */
  buildWorkerId?: number;
  /** @format int64 */
  pipelineId?: number;
  /** @format int64 */
  pipelineInstanceId?: number;
  /** @format int64 */
  pipelineJobInstanceId?: number;
  /** @format int64 */
  pipelineRelationId?: number;
  pipelineSceneType?: string;
  /** @format int64 */
  platformNodeEntityId?: number;
  platformNodeEntityType?: string;
}

/** LogContent */
export interface LogContent {
  content?: string;
  /** @format int64 */
  contentCurrentMaxLength?: number;
  /** @format int64 */
  contentLength?: number;
  /** @format int64 */
  position?: number;
}

/** LogContent0 */
export interface LogContent0 {
  content?: string;
  /** @format int64 */
  contentCurrentMaxLength?: number;
  /** @format int64 */
  contentLength?: number;
  /** @format int64 */
  position?: number;
}

/** LogErrorItemSearchDTO */
export interface LogErrorItemSearchDTO {
  bizIdentifier?: string;
  content?: string;
  /** @format int64 */
  embeddingModelId?: number;
  embeddingModelIdentifier?: string;
  extra?: object;
}

/** LogInfo */
export interface LogInfo {
  fileInfo?: FileInfo;
  logId?: string;
  logMetadata?: Record<string, string>;
  logName?: string;
}

/** LogKnowledgeMatchItem */
export interface LogKnowledgeMatchItem {
  errorSolutionChatInfo?: LLMChatInfoDTO;
  matchKnowledgeContent?: string;
  /** @format int64 */
  matchKnowledgeItemId?: number;
  /** @format double */
  matchScore?: number;
  /** @format int64 */
  matchSolutionId?: number;
  searchSource?: LogErrorItemSearchDTO;
  solutionContent?: string;
}

/** LogPageContent */
export interface LogPageContent {
  content?: string;
  /** @format int64 */
  contentCurrentMaxLength?: number;
  /** @format int64 */
  contentLength?: number;
  /** @format int32 */
  page?: number;
  /** @format int64 */
  pageSize?: number;
  /** @format int32 */
  pageTotal?: number;
  /** @format int64 */
  position?: number;
}

/** Machine */
export interface Machine {
  androidVersion?: string;
  /** @format double */
  cpuFreqGHz?: number;
  creator?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format double */
  hardDiskGB?: number;
  hostAddress?: string;
  hostName?: string;
  /** @format int64 */
  id?: number;
  installedTools?: string[];
  isCpu64bit?: boolean;
  javaProperties?: Record<string, string>;
  javaVersion?: string;
  /** @format int32 */
  logicalCpuCount?: number;
  macAddress?: string;
  /** @format int64 */
  machineGroupId?: number;
  /** @format int64 */
  maxFileDescriptor?: number;
  /** @format double */
  maxMemoryMB?: number;
  modifier?: string;
  os?: string;
  osArch?: string;
  osVersion?: string;
  /** @format int32 */
  physicalCpuCount?: number;
  pythonVersion?: string;
  status?: 'ABNORMITY' | 'DISCARD' | 'NORMAL';
  xcodeVersion?: string;
}

/** MachineBO */
export interface MachineBO {
  androidVersion?: string;
  /** @format double */
  cpuFreqGHz?: number;
  creator?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format double */
  hardDiskGB?: number;
  hostAddress?: string;
  hostName?: string;
  /** @format int64 */
  id?: number;
  installedTools?: string[];
  isCpu64bit?: boolean;
  javaProperties?: Record<string, string>;
  javaVersion?: string;
  /** @format int32 */
  logicalCpuCount?: number;
  macAddress?: string;
  /** @format int64 */
  machineGroupId?: number;
  /** @format int64 */
  maxFileDescriptor?: number;
  /** @format double */
  maxMemoryMB?: number;
  modifier?: string;
  os?: string;
  osArch?: string;
  osVersion?: string;
  /** @format int32 */
  physicalCpuCount?: number;
  pythonVersion?: string;
  status?: 'ABNORMITY' | 'DISCARD' | 'NORMAL';
  xcodeVersion?: string;
}

/** MachineGroup */
export interface MachineGroup {
  creator?: string;
  globalName?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  modifier?: string;
  tags?: string[];
  tenant?: string;
}

/** MachineInfo */
export interface MachineInfo {
  androidVersion?: string;
  /** @format double */
  cpuFreqGHz?: number;
  /** @format double */
  hardDiskGB?: number;
  hostAddress?: string;
  hostName?: string;
  installedTools?: string[];
  isCpu64bit?: boolean;
  javaProperties?: Record<string, string>;
  javaVersion?: string;
  /** @format int32 */
  logicalCpuCount?: number;
  macAddress?: string;
  /** @format int64 */
  maxFileDescriptor?: number;
  /** @format double */
  maxMemoryMB?: number;
  os?: string;
  osArch?: string;
  osVersion?: string;
  /** @format int32 */
  physicalCpuCount?: number;
  pythonVersion?: string;
  xcodeVersion?: string;
}

/** ModelEvaluationDataBO */
export interface ModelEvaluationDataBO {
  allErrorLog?: string;
  buildTaskName?: string;
  errorRootCause?: string;
  extra?: string;
  /** @format int64 */
  jobInstanceId?: number;
  jobInstanceUrl?: string;
  jobLogDownloadUrl?: string;
  keyErrorLog?: string;
  modelIdentifier?: string;
  modelName?: string;
  status?: 'INVALID' | 'VALID';
}

/** ModelTrainTaskRecordDTO */
export interface ModelTrainTaskRecordDTO {
  extra?: object;
  featureDataInfo?: Record<string, string>;
  featureDataUploadId?: string;
  identifier?: string;
  modelEvaluationResult?: string;
  modelFileUploadId?: string;
  modelInfo?: object;
  modelStatus?: 'INVALID' | 'VALID' | 'WAIT_CONFIRM';
  modelType?: 'BUILD_TASK_LOG_EXTRACT_MODEL' | 'TEXT_EMBEDDING_MODEL';
  name?: string;
  originLogDataInfo?: Record<string, string>;
  originLogDataUploadId?: string;
  parserObjectUploadId?: string;
  /** @format int64 */
  relatedEntityId?: number;
  relatedEntityType?: 'APPLICATION' | 'PIPELINE_TASK';
  status?: 'RUNNING' | 'SUCCEEDED';
  structuredLogDataInfo?: Record<string, string>;
  structuredLogDataUploadId?: string;
  templatesLogDataInfo?: Record<string, string>;
  templatesLogDataUploadId?: string;
  /** @format date-time */
  trainEndTime?: string;
  trainEnvironment?: Record<string, string>;
  trainMachineIp?: string;
  trainParams?: object;
  /** @format date-time */
  trainStartTime?: string;
  trainType?: string;
}

/** PipelineJob */
export interface PipelineJob {
  /** @format int64 */
  createTime?: number;
  creator?: string;
  envVariables?: Record<string, string>;
  extended?: Record<string, string>;
  /** @uniqueItems true */
  formerJobs?: string[];
  formerTaskMap?: Record<string, string[]>;
  /** @format int64 */
  id?: number;
  jobEnvType?: 'AGENT' | 'SERVERLESS';
  jobEnvironment?: Environment;
  jobOutputName?: string;
  jobRunType?: 'RUN_WHATEVER' | 'RUN_WHEN_FORMER_FAILED' | 'RUN_WHEN_FORMER_SUCCESS';
  /** @format int64 */
  jobTimeoutMinutes?: number;
  jobType?: 'GATE' | 'NORMAL';
  /** @format int64 */
  modifiedTime?: number;
  modifier?: string;
  name?: string;
  /** @format int64 */
  sourceId?: number;
  sourceUuid?: string;
  /** @format int64 */
  stageId?: number;
  stageUuid?: string;
  tasks?: PipelineTask[];
  uuid?: string;
}

/** PipelineJobInstance */
export interface PipelineJobInstance {
  afterFinishJobExecuteStatus?:
    | 'CANCELLED'
    | 'FAILED'
    | 'RUNNING'
    | 'SKIPPED'
    | 'SKIP_SUCCEEDED'
    | 'SUCCEEDED'
    | 'TIMEOUT'
    | 'WAITING'
    | 'WAITING_TO_SKIP';
  /** @format date-time */
  afterFinishJobFinishTime?: string;
  /** @format date-time */
  afterFinishJobStartTime?: string;
  definitionId?: string;
  /** @format date-time */
  endTime?: string;
  /** @format date-time */
  endTimeServer?: string;
  envVariables?: Record<string, string>;
  executor?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  jobBizStatus?:
    | 'CANCELLED'
    | 'FAILED'
    | 'RUNNING'
    | 'SKIPPED'
    | 'SKIP_SUCCEEDED'
    | 'SUCCEEDED'
    | 'TIMEOUT'
    | 'WAITING'
    | 'WAITING_TO_SKIP';
  jobExecuteStatus?:
    | 'CANCELLED'
    | 'FAILED'
    | 'RUNNING'
    | 'SKIPPED'
    | 'SKIP_SUCCEEDED'
    | 'SUCCEEDED'
    | 'TIMEOUT'
    | 'WAITING'
    | 'WAITING_TO_SKIP';
  /** @format int64 */
  jobMaxRunningMinutes?: number;
  /** @format int64 */
  jobScheduleId?: number;
  machineIp?: string;
  /** @format int64 */
  pipelineInstanceId?: number;
  pipelineJob?: PipelineJob;
  /** @format int64 */
  pipelineJobId?: number;
  /** @format int64 */
  pipelineSnapshotId?: number;
  /** @format int64 */
  pipelineStageInstanceId?: number;
  pipelineTaskInstanceList?: PipelineTaskInstance[];
  /** @format date-time */
  startTime?: string;
  /** @format date-time */
  startTimeServer?: string;
  taskConfigs?: Record<string, object>;
}

/** PipelineTask */
export interface PipelineTask {
  /** @format int64 */
  createTime?: number;
  creator?: string;
  extended?: Record<string, string>;
  /** @uniqueItems true */
  formerTasks?: string[];
  /** @format int64 */
  id?: number;
  /** @format int64 */
  jobId?: number;
  jobUuid?: string;
  /** @format int64 */
  modifiedTime?: number;
  modifier?: string;
  name?: string;
  pluginConfigs?: Record<string, object>;
  /** @format int64 */
  pluginId?: number;
  /** @format int64 */
  sourceId?: number;
  sourceUuid?: string;
  taskPhase?: 'AFTER_FINISH' | 'BEFORE_START' | 'NORMAL';
  taskRunType?: 'RUN_WHATEVER' | 'RUN_WHEN_FORMER_FAILED' | 'RUN_WHEN_FORMER_SUCCESS';
  taskType?: 'GATE' | 'PLUGIN_TASK';
  uuid?: string;
}

/** PipelineTaskInstance */
export interface PipelineTaskInstance {
  /** @format date-time */
  endTime?: string;
  /** @format date-time */
  endTimeServer?: string;
  executor?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  /** @format int64 */
  jobInstanceId?: number;
  /** @format int64 */
  pipelineInstanceId?: number;
  /** @format int64 */
  pipelineSnapshotId?: number;
  /** @format int64 */
  pipelineStageInstanceId?: number;
  pipelineTask?: PipelineTask;
  /** @format int64 */
  pipelineTaskId?: number;
  /** @format date-time */
  pluginDownloadFinishTime?: string;
  /** @format date-time */
  pluginDownloadStartTime?: string;
  /** @format date-time */
  pluginInstallFinishTime?: string;
  /** @format date-time */
  pluginInstallStartTime?: string;
  /** @format date-time */
  pluginLoadFinishTime?: string;
  /** @format date-time */
  pluginLoadStartTime?: string;
  pluginTask?: PluginTask;
  /** @format date-time */
  startTime?: string;
  /** @format date-time */
  startTimeServer?: string;
  taskBizStatus?:
    | 'CANCELLED'
    | 'FAILED'
    | 'RUNNING'
    | 'SKIPPED'
    | 'SKIP_SUCCEEDED'
    | 'SUCCEEDED'
    | 'TIMEOUT'
    | 'WAITING'
    | 'WAITING_TO_SKIP';
  taskExecuteStatus?:
    | 'CANCELLED'
    | 'FAILED'
    | 'RUNNING'
    | 'SKIPPED'
    | 'SKIP_SUCCEEDED'
    | 'SUCCEEDED'
    | 'TIMEOUT'
    | 'WAITING'
    | 'WAITING_TO_SKIP';
  taskPhase?: 'AFTER_FINISH' | 'BEFORE_START' | 'NORMAL';
  taskResult?: TaskResult;
}

/** PluginTask */
export interface PluginTask {
  artifactId?: string;
  /** @format date-time */
  createTime?: string;
  creator?: string;
  definitionId?: string;
  dependents?: string[];
  groupId?: string;
  /** @format int64 */
  id?: number;
  /** @format int64 */
  jobId?: number;
  mavenVersion?: string;
  modifier?: string;
  /** @format date-time */
  modifyTime?: string;
  pluginConfigs?: Record<string, object>;
  pluginDescription?: string;
  pluginId?: string;
  pluginModifier?: string;
  pluginName?: string;
  /** @uniqueItems true */
  pluginOutputs?: string[];
  /** @format int64 */
  pluginPlatformId?: number;
  pluginProvider?: string;
  pluginVersion?: string;
  taskRunType?: 'RUN_WHATEVER' | 'RUN_WHEN_FORMER_FAILED' | 'RUN_WHEN_FORMER_SUCCESS';
  taskType?: 'GATE' | 'PLUGIN_TASK';
}

/** PullJobRequest */
export interface PullJobRequest {
  runningJobList?: string[];
  workerId?: string;
}

/** PullJobResult */
export interface PullJobResult {
  jobs?: JobExt[];
}

/** PushEventsRequest */
export interface PushEventsRequest {
  workerId?: string;
}

/** PushEventsResult */
export type PushEventsResult = object;

/** RegisterRequest */
export interface RegisterRequest {
  authInfo?: string;
  authKey?: string;
  authRandom?: string;
  machineInfo?: MachineInfo;
  workerInfo?: WorkerInfo;
}

/** RegisterResult */
export interface RegisterResult {
  signToken?: string;
  workerId?: string;
}

/** ReportMetricsRequest */
export interface ReportMetricsRequest {
  workerId?: string;
}

/** ReportMetricsResult */
export type ReportMetricsResult = object;

/** RequestForwardRequest */
export interface RequestForwardRequest {
  jobScheduleId?: string;
  requestMessage?: RequestMessage;
}

/** RequestForwardResult */
export interface RequestForwardResult {
  responseMessage?: ResponseMessage;
}

/** RequestMessage */
export interface RequestMessage {
  content?: InputStream;
  /** @format int64 */
  contentLength?: number;
  endpoint?: string;
  formParameters?: Record<string, string>;
  headers?: Record<string, string>;
  json?: string;
  method?: 'DELETE' | 'GET' | 'HEAD' | 'OPTIONS' | 'POST' | 'PUT';
  originalRequest?: object;
  parameters?: Record<string, string>;
  path?: string;
  requireSignIn?: boolean;
  url?: string;
}

/** ResponseEntity */
export interface ResponseEntity {
  body?: object;
  statusCode?:
    | 'ACCEPTED'
    | 'ALREADY_REPORTED'
    | 'BAD_GATEWAY'
    | 'BAD_REQUEST'
    | 'BANDWIDTH_LIMIT_EXCEEDED'
    | 'CHECKPOINT'
    | 'CONFLICT'
    | 'CONTINUE'
    | 'CREATED'
    | 'DESTINATION_LOCKED'
    | 'EXPECTATION_FAILED'
    | 'FAILED_DEPENDENCY'
    | 'FORBIDDEN'
    | 'FOUND'
    | 'GATEWAY_TIMEOUT'
    | 'GONE'
    | 'HTTP_VERSION_NOT_SUPPORTED'
    | 'IM_USED'
    | 'INSUFFICIENT_SPACE_ON_RESOURCE'
    | 'INSUFFICIENT_STORAGE'
    | 'INTERNAL_SERVER_ERROR'
    | 'I_AM_A_TEAPOT'
    | 'LENGTH_REQUIRED'
    | 'LOCKED'
    | 'LOOP_DETECTED'
    | 'METHOD_FAILURE'
    | 'METHOD_NOT_ALLOWED'
    | 'MOVED_PERMANENTLY'
    | 'MOVED_TEMPORARILY'
    | 'MULTIPLE_CHOICES'
    | 'MULTI_STATUS'
    | 'NETWORK_AUTHENTICATION_REQUIRED'
    | 'NON_AUTHORITATIVE_INFORMATION'
    | 'NOT_ACCEPTABLE'
    | 'NOT_EXTENDED'
    | 'NOT_FOUND'
    | 'NOT_IMPLEMENTED'
    | 'NOT_MODIFIED'
    | 'NO_CONTENT'
    | 'OK'
    | 'PARTIAL_CONTENT'
    | 'PAYLOAD_TOO_LARGE'
    | 'PAYMENT_REQUIRED'
    | 'PERMANENT_REDIRECT'
    | 'PRECONDITION_FAILED'
    | 'PRECONDITION_REQUIRED'
    | 'PROCESSING'
    | 'PROXY_AUTHENTICATION_REQUIRED'
    | 'REQUESTED_RANGE_NOT_SATISFIABLE'
    | 'REQUEST_ENTITY_TOO_LARGE'
    | 'REQUEST_HEADER_FIELDS_TOO_LARGE'
    | 'REQUEST_TIMEOUT'
    | 'REQUEST_URI_TOO_LONG'
    | 'RESET_CONTENT'
    | 'SEE_OTHER'
    | 'SERVICE_UNAVAILABLE'
    | 'SWITCHING_PROTOCOLS'
    | 'TEMPORARY_REDIRECT'
    | 'TOO_MANY_REQUESTS'
    | 'UNAUTHORIZED'
    | 'UNAVAILABLE_FOR_LEGAL_REASONS'
    | 'UNPROCESSABLE_ENTITY'
    | 'UNSUPPORTED_MEDIA_TYPE'
    | 'UPGRADE_REQUIRED'
    | 'URI_TOO_LONG'
    | 'USE_PROXY'
    | 'VARIANT_ALSO_NEGOTIATES';
  /** @format int32 */
  statusCodeValue?: number;
}

/** ResponseMessage */
export interface ResponseMessage {
  body?: string;
  content?: InputStream;
  /** @format int64 */
  contentLength?: number;
  contentType?: string;
  headers?: Record<string, string>;
  httpResponse?: object;
  json?: string;
  originalRequest?: object;
  reasonPhrase?: string;
  /** @format int32 */
  statusCode?: number;
  url?: string;
}

/** StorageObjectInfo */
export interface StorageObjectInfo {
  storageObjectKey?: StorageObjectKey;
  storageObjectMetadata?: Record<string, string>;
  storageType?: 'CEPH' | 'DISK' | 'FTP' | 'OSS';
}

/** StorageObjectKey */
export type StorageObjectKey = object;

/** StorageToken */
export interface StorageToken {
  tokenId?: string;
}

/** TargetCondition */
export type TargetCondition = object;

/** TaskFinishedRequest */
export interface TaskFinishedRequest {
  /** @format int64 */
  finishTime?: number;
  jobScheduleId?: string;
  taskId?: string;
  taskResult?: TaskResult;
}

/** TaskFinishedResult */
export type TaskFinishedResult = object;

/** TaskPluginDownloadFinishRequest */
export interface TaskPluginDownloadFinishRequest {
  jobScheduleId?: string;
  /** @format int64 */
  pluginDownloadFinishTime?: number;
  taskId?: string;
}

/** TaskPluginDownloadFinishResult */
export type TaskPluginDownloadFinishResult = object;

/** TaskPluginDownloadStartRequest */
export interface TaskPluginDownloadStartRequest {
  jobScheduleId?: string;
  /** @format int64 */
  pluginDownloadStartTime?: number;
  taskDisplayName?: string;
  taskId?: string;
  taskName?: string;
}

/** TaskPluginDownloadStartResult */
export type TaskPluginDownloadStartResult = object;

/** TaskPluginInstallFinishRequest */
export interface TaskPluginInstallFinishRequest {
  jobScheduleId?: string;
  /** @format int64 */
  pluginInstallFinishTime?: number;
  taskId?: string;
}

/** TaskPluginInstallFinishResult */
export type TaskPluginInstallFinishResult = object;

/** TaskPluginInstallStartRequest */
export interface TaskPluginInstallStartRequest {
  jobScheduleId?: string;
  /** @format int64 */
  pluginInstallStartTime?: number;
  taskId?: string;
}

/** TaskPluginInstallStartResult */
export type TaskPluginInstallStartResult = object;

/** TaskPluginLoadFinishRequest */
export interface TaskPluginLoadFinishRequest {
  jobScheduleId?: string;
  /** @format int64 */
  pluginLoadFinishTime?: number;
  taskId?: string;
}

/** TaskPluginLoadFinishResult */
export type TaskPluginLoadFinishResult = object;

/** TaskPluginLoadStartRequest */
export interface TaskPluginLoadStartRequest {
  jobScheduleId?: string;
  /** @format int64 */
  pluginLoadStartTime?: number;
  taskId?: string;
}

/** TaskPluginLoadStartResult */
export type TaskPluginLoadStartResult = object;

/** TaskResult */
export interface TaskResult {
  status?:
    | 'CANCELLED'
    | 'FAILED'
    | 'RUNNING'
    | 'SKIPPED'
    | 'SKIP_SUCCEEDED'
    | 'SUCCEEDED'
    | 'TIMEOUT'
    | 'WAITING'
    | 'WAITING_TO_SKIP';
}

/** TaskStartedRequest */
export interface TaskStartedRequest {
  jobScheduleId?: string;
  logContent?: string;
  logFileMetadata?: Record<string, string>;
  logFilename?: string;
  logMetadata?: Record<string, string>;
  logName?: string;
  logUploadId?: string;
  /** @format int64 */
  startTime?: number;
  taskDisplayName?: string;
  taskId?: string;
  taskName?: string;
}

/** TaskStartedResult */
export type TaskStartedResult = object;

/** TrainModelDTO */
export interface TrainModelDTO {
  extra?: object;
  identifier?: string;
  modelFileUploadId?: string;
  modelInfo?: object;
  modelRunArgs?: string;
  /** @format int64 */
  modelTrainRecordId?: number;
  modelType?: 'BUILD_TASK_LOG_EXTRACT_MODEL' | 'TEXT_EMBEDDING_MODEL';
  name?: string;
  parserObjectUploadId?: string;
  status?: 'INVALID' | 'VALID' | 'WAIT_CONFIRM';
  /** @format date-time */
  validEndTime?: string;
  /** @format date-time */
  validStartTime?: string;
}

/** TrainModelResultVO */
export interface TrainModelResultVO {
  creator?: string;
  /** @format int64 */
  errorJobTaskId?: number;
  errorJobTaskLogUploadId?: string;
  /** @format int64 */
  errorTaskInstanceId?: number;
  errorTaskUuid?: string;
  extra?: object;
  featureDataDownloadUrl?: string;
  featureDataInfo?: Record<string, string>;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  identifier?: string;
  logTemplateDataInfo?: Record<string, string>;
  logTemplateDownloadUrl?: string;
  message?: string;
  modelFileDownloadUrl?: string;
  modelFileUploadId?: string;
  modelInfo?: object;
  modelRunArgs?: string;
  /** @format int64 */
  modelTrainRecordId?: number;
  modelType?: 'BUILD_TASK_LOG_EXTRACT_MODEL' | 'TEXT_EMBEDDING_MODEL';
  modifier?: string;
  name?: string;
  originLogDataInfo?: Record<string, string>;
  originLogDownloadUrl?: string;
  parserObjectDownloadUrl?: string;
  parserObjectUploadId?: string;
  status?: 'INVALID' | 'VALID' | 'WAIT_CONFIRM';
  structuredLogDataInfo?: Record<string, string>;
  structuredLogDownloadUrl?: string;
  /** @format date-time */
  validEndTime?: string;
  /** @format date-time */
  validStartTime?: string;
}

/** TrainModelVO */
export interface TrainModelVO {
  creator?: string;
  extra?: object;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  identifier?: string;
  modelFileDownloadUrl?: string;
  modelFileUploadId?: string;
  modelInfo?: object;
  modelRunArgs?: string;
  /** @format int64 */
  modelTrainRecordId?: number;
  modelType?: 'BUILD_TASK_LOG_EXTRACT_MODEL' | 'TEXT_EMBEDDING_MODEL';
  modifier?: string;
  name?: string;
  parserObjectDownloadUrl?: string;
  parserObjectUploadId?: string;
  status?: 'INVALID' | 'VALID' | 'WAIT_CONFIRM';
  /** @format date-time */
  validEndTime?: string;
  /** @format date-time */
  validStartTime?: string;
}

/** UpdateMachineInfoRequest */
export interface UpdateMachineInfoRequest {
  machineInfo?: MachineInfo;
  workerId?: string;
}

/** UpdateMachineInfoResult */
export type UpdateMachineInfoResult = object;

/** UploadToken */
export interface UploadToken {
  storageObjectKey?: StorageObjectKey;
  storageToken?: StorageToken;
  storageType?: 'CEPH' | 'DISK' | 'FTP' | 'OSS';
  tokenId?: string;
}

/** UploadedRequest */
export interface UploadedRequest {
  fileInfo?: FileInfo;
  storageObjectInfo?: StorageObjectInfo;
  tokenId?: string;
}

/** UploadedResult */
export interface UploadedResult {
  uploadId?: string;
}

/** Worker */
export interface Worker {
  creator?: string;
  definitionId?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  isDockerSupported?: boolean;
  /** @format int32 */
  jobLimit?: number;
  /** @format int64 */
  machineId?: number;
  modifier?: string;
  signToken?: string;
  status?: 'ABNORMITY' | 'DISCARD' | 'NORMAL';
  /** @uniqueItems true */
  supportJobTypes?: string[];
  /** @uniqueItems true */
  tags?: string[];
  type?: 'AGENT' | 'SERVERLESS';
  version?: string;
  /** @format int64 */
  workgroupId?: number;
}

/** WorkerBO */
export interface WorkerBO {
  creator?: string;
  definitionId?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  /** @format int64 */
  id?: number;
  isDockerSupported?: boolean;
  /** @format int64 */
  machineId?: number;
  modifier?: string;
  signToken?: string;
  status?: 'ABNORMITY' | 'DISCARD' | 'NORMAL';
  /** @uniqueItems true */
  supportJobTypes?: string[];
  /** @uniqueItems true */
  tags?: string[];
  type?: 'AGENT' | 'SERVERLESS';
  version?: string;
  workerOnlineStatus?: 'WORKER_OFFLINE' | 'WORKER_ONLINE';
  /** @format int64 */
  workgroupId?: number;
}

/** WorkerInfo */
export interface WorkerInfo {
  isDockerSupported?: boolean;
  /** @uniqueItems true */
  tags?: string[];
  version?: string;
  workgroupName?: string;
}

/** WorkerMachineInfo */
export interface WorkerMachineInfo {
  machine?: MachineBO;
  worker?: WorkerBO;
}

/** WorkgroupBO */
export interface WorkgroupBO {
  admin?: string;
  creator?: string;
  description?: string;
  globalName?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  groupName?: string;
  /** @format int64 */
  id?: number;
  isUsing?: boolean;
  modifier?: string;
  /** @format int64 */
  securityKeyId?: number;
  source?: 'MTL';
  /** @uniqueItems true */
  tags?: string[];
  tenant?: string;
  workerType?: 'AGENT' | 'SERVERLESS';
  workgroupType?: 'PRIVATE' | 'PUBLIC';
}
