/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { File, WhiteOssFileInfo } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface UploadParams {
  /** type */
  type: 'USERID' | 'UTDID';
}
/**
 * No description
 * @tags PublishWhiteCrowd
 * @name Upload
 * @summary 保存白名单人群
 * @request POST:/api/v1/publish/action/white/crowd/upload
 */
export async function upload(query: UploadParams, data: File, options?: MethodOptions): Promise<WhiteOssFileInfo> {
  return request(`${baseUrl}/api/v1/publish/action/white/crowd/upload`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}
