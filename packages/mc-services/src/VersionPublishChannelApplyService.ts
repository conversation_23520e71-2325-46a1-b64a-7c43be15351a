/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { VersionPublishChannelApplyInfo } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface CreateParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /** infoText */
  infoText: string;
}
/**
 * No description
 * @tags VersionPublishChannelApplyService
 * @name Create
 * @summary 创建bpms审批单
 * @request GET:/api/v1/publish/action/versionPublishChannelApply/create
 */
export async function create(query: CreateParams, options?: MethodOptions): Promise<VersionPublishChannelApplyInfo> {
  return request(`${baseUrl}/api/v1/publish/action/versionPublishChannelApply/create`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface SearchParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags VersionPublishChannelApplyService
 * @name Search
 * @summary 查询bpms审批单
 * @request GET:/api/v1/publish/action/versionPublishChannelApply/search
 */
export async function search(query: SearchParams, options?: MethodOptions): Promise<VersionPublishChannelApplyInfo> {
  return request(`${baseUrl}/api/v1/publish/action/versionPublishChannelApply/search`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
