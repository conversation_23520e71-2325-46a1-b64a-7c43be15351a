/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import {
  CiBranchValidatorResult,
  CommitDiff,
  CreateProjectRequest,
  GitBranchInfo,
  GitCommit,
  GitGroup,
  GitProject,
  GitTag,
  MergeRequestDetailDTO,
  MergeRequestResult,
  MergeResultDTO,
  ModuleGitActionDetail,
  ModuleGitOperationQuery,
  RepositoryFileRequest,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface AutoMergeParams {
  /** scmAddress */
  scmAddress: string;
  /** from */
  from: string;
  /** to */
  to: string;
}
/**
 * No description
 * @tags Gitlab
 * @name AutoMerge
 * @summary 一键合并
 * @request PUT:/api/v1/gitlab/autoMerge
 */
export async function autoMerge(query: AutoMergeParams, options?: MethodOptions): Promise<MergeResultDTO> {
  return request(`${baseUrl}/api/v1/gitlab/autoMerge`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface CheckCiBranchParams {
  /** scmAddress */
  scmAddress: string;
}
/**
 * No description
 * @tags Gitlab
 * @name CheckCiBranch
 * @summary 检查CI分支名
 * @request PUT:/api/v1/gitlab/checkCiBranch
 */
export async function checkCiBranch(
  query: CheckCiBranchParams,
  options?: MethodOptions,
): Promise<CiBranchValidatorResult> {
  return request(`${baseUrl}/api/v1/gitlab/checkCiBranch`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface CheckPermissionParams {
  /** scmAddress */
  scmAddress: string;
  /** empId */
  empId: string;
  /** accessLevel */
  accessLevel?: 'Developer' | 'Guest' | 'Master' | 'Owner' | 'Reporter';
}
/**
 * No description
 * @tags Gitlab
 * @name CheckPermission
 * @summary 检查用户权限
 * @request PUT:/api/v1/gitlab/checkPermission
 */
export async function checkPermission(query: CheckPermissionParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/checkPermission`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface GetCommitDiffParams {
  /** scmAddress */
  scmAddress: string;
  /** from */
  from: string;
  /** to */
  to: string;
  /** empId */
  empId: string;
}
/**
 * No description
 * @tags Gitlab
 * @name GetCommitDiff
 * @summary 对比两个commit/分支/tag
 * @request GET:/api/v1/gitlab/compareCommits
 */
export async function getCommitDiff(query: GetCommitDiffParams, options?: MethodOptions): Promise<CommitDiff> {
  return request(`${baseUrl}/api/v1/gitlab/compareCommits`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface CreateBranchParams {
  /** scmAddress */
  scmAddress: string;
  /** sourceBranch */
  sourceBranch: string;
  /** targetBranch */
  targetBranch: string;
}
/**
 * @description 谨慎赋予权限
 * @tags Gitlab
 * @name CreateBranch
 * @summary 基于已有的分支/commit拉取新的分支（谨慎赋权）
 * @request POST:/api/v1/gitlab/createBranch
 */
export async function createBranch(query: CreateBranchParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/createBranch`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface CreateGitTagParams {
  /** scmAddress */
  scmAddress: string;
  /** scmCommit */
  scmCommit: string;
  /** tagName */
  tagName: string;
  /** message */
  message?: string;
  /** releaseDesc */
  releaseDesc?: string;
}
/**
 * No description
 * @tags Gitlab
 * @name CreateGitTag
 * @summary create git tag
 * @request GET:/api/v1/gitlab/createGitTag
 */
export async function createGitTag(query: CreateGitTagParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/createGitTag`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface CreateMergeRequestAndAcceptParams {
  /** scmAddress */
  scmAddress: string;
  /** sourceBranch */
  sourceBranch: string;
  /** targetBranch */
  targetBranch: string;
  /** assigneeid */
  assigneeid: string;
  /** title */
  title: string;
}
/**
 * No description
 * @tags Gitlab
 * @name CreateMergeRequestAndAccept
 * @summary merge and accept automatically
 * @request POST:/api/v1/gitlab/createMergeRequestAndAccept
 */
export async function createMergeRequestAndAccept(
  query: CreateMergeRequestAndAcceptParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/gitlab/createMergeRequestAndAccept`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags Gitlab
 * @name CreateProjectForUser
 * @summary create project for user
 * @request POST:/api/v1/gitlab/createProjectForUser
 */
export async function createProjectForUser(data: CreateProjectRequest, options?: MethodOptions): Promise<GitProject> {
  return request(`${baseUrl}/api/v1/gitlab/createProjectForUser`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface CreateTagParams {
  /** scmAddress */
  scmAddress: string;
  /** scmCommit */
  scmCommit: string;
  /** tag */
  tag: string;
}
/**
 * No description
 * @tags Gitlab
 * @name CreateTag
 * @summary create git tag
 * @request POST:/api/v1/gitlab/createTag
 */
export async function createTag(query: CreateTagParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/createTag`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface DeleteBranchParams {
  /** scmAddress */
  scmAddress: string;
  /** targetBranch */
  targetBranch: string;
}
/**
 * No description
 * @tags Gitlab
 * @name DeleteBranch
 * @summary delete branch
 * @request POST:/api/v1/gitlab/deleteBranch
 */
export async function deleteBranch(query: DeleteBranchParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/deleteBranch`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface FindCommitListParams {
  /** gitAddress */
  gitAddress: string;
  /** refName */
  refName: string;
  /**
   * pageNum
   * @format int32
   */
  pageNum: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize: number;
}
/**
 * No description
 * @tags Gitlab
 * @name FindCommitList
 * @summary find all commit list
 * @request GET:/api/v1/gitlab/findCommitList
 */
export async function findCommitList(query: FindCommitListParams, options?: MethodOptions): Promise<GitCommit[]> {
  return request(`${baseUrl}/api/v1/gitlab/findCommitList`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags Gitlab
 * @name FindModuleGitActionDetail
 * @summary 查询分支活动详情
 * @request POST:/api/v1/gitlab/findModuleGitActionDetail
 */
export async function findModuleGitActionDetail(
  data: ModuleGitOperationQuery,
  options?: MethodOptions,
): Promise<ModuleGitActionDetail[]> {
  return request(`${baseUrl}/api/v1/gitlab/findModuleGitActionDetail`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface GetAuthorizedProjectsParams {
  /** archived */
  archived?: boolean;
  /** orderBy */
  orderBy?: string;
  /**
   * pageNo
   * @format int32
   */
  pageNo?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
  /** search */
  search?: string;
  /** sort */
  sort?: string;
}
/**
 * No description
 * @tags Gitlab
 * @name GetAuthorizedProjects
 * @summary 获取用户有权限的projects
 * @request GET:/api/v1/gitlab/getAuthorizedProjects
 */
export async function getAuthorizedProjects(
  query?: GetAuthorizedProjectsParams,
  options?: MethodOptions,
): Promise<GitProject[]> {
  return request(`${baseUrl}/api/v1/gitlab/getAuthorizedProjects`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetBranchPageParams {
  /** scmAddress */
  scmAddress: string;
  /** search */
  search?: string;
  /** sort */
  sort?: string;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
}
/**
 * No description
 * @tags Gitlab
 * @name GetBranchPage
 * @summary 分页获取Git仓库地址的分支
 * @request GET:/api/v1/gitlab/getBranchByPage
 */
export async function getBranchPage(
  query: GetBranchPageParams,
  options?: MethodOptions,
): Promise<PaginationResult<GitBranchInfo>> {
  return request(`${baseUrl}/api/v1/gitlab/getBranchByPage`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetBranchListParams {
  /** scnAddress */
  scnAddress: string;
}
/**
 * No description
 * @tags Gitlab
 * @name GetBranchList
 * @summary 获取Git仓库地址的所有分支
 * @request GET:/api/v1/gitlab/getBranches
 */
export async function getBranchList(query: GetBranchListParams, options?: MethodOptions): Promise<GitBranchInfo[]> {
  return request(`${baseUrl}/api/v1/gitlab/getBranches`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetGitGroupsParams {
  /** accessLevels */
  accessLevels?: string;
  /** includePersonal */
  includePersonal?: boolean;
  /**
   * pageNo
   * @format int32
   */
  pageNo?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
  /** search */
  search?: string;
  /** gitUrlPrefix */
  gitUrlPrefix?: string;
}
/**
 * No description
 * @tags Gitlab
 * @name GetGitGroups
 * @summary 获取用户的git groups
 * @request GET:/api/v1/gitlab/getGitGroups
 */
export async function getGitGroups(query?: GetGitGroupsParams, options?: MethodOptions): Promise<GitGroup[]> {
  return request(`${baseUrl}/api/v1/gitlab/getGitGroups`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetLatestCommitParams {
  /** scmAddress */
  scmAddress: string;
  /** scmBranch */
  scmBranch: string;
}
/**
 * No description
 * @tags Gitlab
 * @name GetLatestCommit
 * @summary get latest commit by scmAddress and scmBranch
 * @request GET:/api/v1/gitlab/getLatestCommit
 */
export async function getLatestCommit(query: GetLatestCommitParams, options?: MethodOptions): Promise<string> {
  return request(`${baseUrl}/api/v1/gitlab/getLatestCommit`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetMergeRequestsParams {
  /** scmAddress */
  scmAddress: string;
  /** sourceBranch */
  sourceBranch: string;
  /** targetBranch */
  targetBranch: string;
  /**
   * page
   * @format int32
   */
  page: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize: number;
}
/**
 * No description
 * @tags Gitlab
 * @name GetMergeRequests
 * @summary 获取代码评审记录
 * @request GET:/api/v1/gitlab/getMergeRequests
 */
export async function getMergeRequests(
  query: GetMergeRequestsParams,
  options?: MethodOptions,
): Promise<MergeRequestDetailDTO[]> {
  return request(`${baseUrl}/api/v1/gitlab/getMergeRequests`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface QueryMrRequestParams {
  /** scmAddress */
  scmAddress: string;
  /**
   * mergeRequestId
   * @format int32
   */
  mergeRequestId: number;
}
/**
 * No description
 * @tags Gitlab
 * @name QueryMrRequest
 * @summary get Mr request
 * @request GET:/api/v1/gitlab/getMrRequest
 */
export async function queryMrRequest(
  query: QueryMrRequestParams,
  options?: MethodOptions,
): Promise<MergeRequestResult> {
  return request(`${baseUrl}/api/v1/gitlab/getMrRequest`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetProjectIdParams {
  /** scmAddress */
  scmAddress: string;
}
/**
 * No description
 * @tags Gitlab
 * @name GetProjectId
 * @summary get project id
 * @request GET:/api/v1/gitlab/getProjectId
 */
export async function getProjectId(query: GetProjectIdParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/gitlab/getProjectId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetSingleCommitParams {
  /** scmAddress */
  scmAddress: string;
  /** scmCommit */
  scmCommit: string;
}
/**
 * No description
 * @tags Gitlab
 * @name GetSingleCommit
 * @summary get commit by scmAddress and commit/tag
 * @request GET:/api/v1/gitlab/getSingleCommit
 */
export async function getSingleCommit(query: GetSingleCommitParams, options?: MethodOptions): Promise<GitCommit> {
  return request(`${baseUrl}/api/v1/gitlab/getSingleCommit`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetTagPageParams {
  /** scmAddress */
  scmAddress: string;
  /** search */
  search?: string;
  /** sort */
  sort?: string;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
}
/**
 * No description
 * @tags Gitlab
 * @name GetTagPage
 * @summary 分页获取Git仓库地址的tag
 * @request GET:/api/v1/gitlab/getTagByPage
 */
export async function getTagPage(query: GetTagPageParams, options?: MethodOptions): Promise<PaginationResult<GitTag>> {
  return request(`${baseUrl}/api/v1/gitlab/getTagByPage`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface IsCommitInBranchParams {
  /** scmAddress */
  scmAddress: string;
  /** scmBranch */
  scmBranch: string;
  /** scmCommit */
  scmCommit: string;
}
/**
 * No description
 * @tags Gitlab
 * @name IsCommitInBranch
 * @summary judge commit is in branch
 * @request GET:/api/v1/gitlab/isCommitInBranch
 */
export async function isCommitInBranch(query: IsCommitInBranchParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/isCommitInBranch`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface IsDiffParams {
  /** scmAddress */
  scmAddress: string;
  /** from */
  from: string;
  /** to */
  to: string;
  /** useCode */
  useCode: boolean;
}
/**
 * No description
 * @tags Gitlab
 * @name IsDiff
 * @summary 判断分支或Commit是否有差别
 * @request GET:/api/v1/gitlab/isDiff
 */
export async function isDiff(query: IsDiffParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/isDiff`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface IsMergeCommitParams {
  /** scmAddress */
  scmAddress: string;
  /** scmCommit */
  scmCommit: string;
}
/**
 * No description
 * @tags Gitlab
 * @name IsMergeCommit
 * @summary judge commit of branch is merge commit
 * @request GET:/api/v1/gitlab/isMergeCommit
 */
export async function isMergeCommit(query: IsMergeCommitParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/isMergeCommit`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface MergeTagCommitToBranchParams {
  /** scmAddress */
  scmAddress: string;
  /** tagName */
  tagName: string;
  /** tagCommit */
  tagCommit: string;
  /** scmBranch */
  scmBranch: string;
}
/**
 * No description
 * @tags Gitlab
 * @name MergeTagCommitToBranch
 * @summary merge tag commit to branch
 * @request POST:/api/v1/gitlab/mergeTagCommit
 */
export async function mergeTagCommitToBranch(
  query: MergeTagCommitToBranchParams,
  options?: MethodOptions,
): Promise<MergeRequestDetailDTO> {
  return request(`${baseUrl}/api/v1/gitlab/mergeTagCommit`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface GetGitTagsParams {
  /** scmAddress */
  scmAddress: string;
}
/**
 * No description
 * @tags Gitlab
 * @name GetGitTags
 * @summary get git tags
 * @request GET:/api/v1/gitlab/tags
 */
export async function getGitTags(query: GetGitTagsParams, options?: MethodOptions): Promise<GitTag[]> {
  return request(`${baseUrl}/api/v1/gitlab/tags`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface UnprotectBranchParams {
  /** scmAddress */
  scmAddress: string;
  /** scmBranch */
  scmBranch: string;
}
/**
 * No description
 * @tags Gitlab
 * @name UnprotectBranch
 * @summary unprotect branch
 * @request GET:/api/v1/gitlab/unprotectBranch
 */
export async function unprotectBranch(query: UnprotectBranchParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/unprotectBranch`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface UpdateMrRequestParams {
  /** scmAddress */
  scmAddress: string;
  /**
   * mergeRequestId
   * @format int32
   */
  mergeRequestId: number;
  /** state */
  state: string;
}
/**
 * No description
 * @tags Gitlab
 * @name UpdateMrRequest
 * @summary update Mr request
 * @request POST:/api/v1/gitlab/updateMrRequest
 */
export async function updateMrRequest(
  query: UpdateMrRequestParams,
  options?: MethodOptions,
): Promise<MergeRequestResult> {
  return request(`${baseUrl}/api/v1/gitlab/updateMrRequest`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags Gitlab
 * @name UpdateRepositoryFile
 * @summary 更新代码库文件
 * @request POST:/api/v1/gitlab/updateRepositoryFile
 */
export async function updateRepositoryFile(data: RepositoryFileRequest, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/gitlab/updateRepositoryFile`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
