/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags Nacos
 * @name Deregister
 * @summary deregister
 * @request GET:/api/v1/nacos/deregister
 */
export async function deregister(options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/nacos/deregister`, {
    method: 'GET',
    ...options,
  });
}
