/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { InsightResultSnapshotDTO } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface ListParams {
  /** @format int64 */
  appId?: number;
  /** @format int64 */
  entityId?: number;
  entityType?: string;
  operator?: string;
}
/**
 * No description
 * @tags InsightResultSnapshot
 * @name List
 * @summary list
 * @request GET:/api/v2/insight/result/snapshots/current
 */
export async function list(query?: ListParams, options?: MethodOptions): Promise<InsightResultSnapshotDTO> {
  return request(`${baseUrl}/api/v2/insight/result/snapshots/current`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
