/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import {
  AlterSheetBO,
  PipelineInstanceModuleVO,
  SubmitTestBO,
  SubmitTestFormDTO,
  SubmitTestModuleBORes,
  SubmitTestVO,
  TestReport,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface CheckCommitTestResultParams {
  /**
   * moduleId
   * @format int64
   */
  moduleId: number;
  /** commitId */
  commitId: string;
}
/**
 * No description
 * @tags TestSheet
 * @name CheckCommitTestResult
 * @summary 查询某个提测单的模块是否测试通过
 * @request GET:/api/v1/submitTest/checkCommitIdIsTestPass
 */
export async function checkCommitTestResult(
  query: CheckCommitTestResultParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/submitTest/checkCommitIdIsTestPass`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface CheckExistIterationInSpaceParams {
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId: number;
}
/**
 * No description
 * @tags TestSheet
 * @name CheckExistIterationInSpace
 * @summary 检查当前研发空间是否已存在集成迭代
 * @request GET:/api/v1/submitTest/checkExistIterationInSpace
 */
export async function checkExistIterationInSpace(
  query: CheckExistIterationInSpaceParams,
  options?: MethodOptions,
): Promise<AlterSheetBO> {
  return request(`${baseUrl}/api/v1/submitTest/checkExistIterationInSpace`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindSubmitTestParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags TestSheet
 * @name FindSubmitTest
 * @summary 查询某个提测单
 * @request GET:/api/v1/submitTest/detail
 */
export async function findSubmitTest(query: FindSubmitTestParams, options?: MethodOptions): Promise<SubmitTestVO> {
  return request(`${baseUrl}/api/v1/submitTest/detail`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FeedbackTestResultParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** status */
  status: 'CANCEL' | 'DRAFT' | 'TESTING' | 'TEST_FAIL' | 'TEST_PASS';
  /** feedback */
  feedback?: string;
}
/**
 * No description
 * @tags TestSheet
 * @name FeedbackTestResult
 * @summary 反馈提测单测试结论
 * @request POST:/api/v1/submitTest/feedbackTestResult
 */
export async function feedbackTestResult(
  query: FeedbackTestResultParams,
  data: TestReport,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/submitTest/feedbackTestResult`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface FindModuleCommitMapOnBranchParams {
  /**
   * moduleId
   * @format int64
   */
  moduleId: number;
  /** branch */
  branch: string;
}
/**
 * No description
 * @tags TestSheet
 * @name FindModuleCommitMapOnBranch
 * @summary 查询某模块在某分支的commitId列表及对应测试状态
 * @request GET:/api/v1/submitTest/findModuleCommitStatusMap
 */
export async function findModuleCommitMapOnBranch(
  query: FindModuleCommitMapOnBranchParams,
  options?: MethodOptions,
): Promise<Record<string, string>> {
  return request(`${baseUrl}/api/v1/submitTest/findModuleCommitStatusMap`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindModuleSubmitTestListOnBranchParams {
  /**
   * moduleId
   * @format int64
   */
  moduleId: number;
  /** branch */
  branch: string;
}
/**
 * No description
 * @tags TestSheet
 * @name FindModuleSubmitTestListOnBranch
 * @summary 查询某模块在某分支的提测列表
 * @request GET:/api/v1/submitTest/findModuleSubmitTestList
 */
export async function findModuleSubmitTestListOnBranch(
  query: FindModuleSubmitTestListOnBranchParams,
  options?: MethodOptions,
): Promise<SubmitTestModuleBORes[]> {
  return request(`${baseUrl}/api/v1/submitTest/findModuleSubmitTestList`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetLatestPassedSubmitTestParams {
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId: number;
}
/**
 * No description
 * @tags TestSheet
 * @name GetLatestPassedSubmitTest
 * @summary 获取最新提测通过的提测单
 * @request GET:/api/v1/submitTest/getLatestPassedSubmitTest
 */
export async function getLatestPassedSubmitTest(
  query: GetLatestPassedSubmitTestParams,
  options?: MethodOptions,
): Promise<SubmitTestBO> {
  return request(`${baseUrl}/api/v1/submitTest/getLatestPassedSubmitTest`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetModuleLatestTestPassCommitIdParams {
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId: number;
  /**
   * moduleId
   * @format int64
   */
  moduleId: number;
}
/**
 * No description
 * @tags TestSheet
 * @name GetModuleLatestTestPassCommitId
 * @summary 查询模块最新测试通过的commitId
 * @request GET:/api/v1/submitTest/getModuleLatestPassCommitId
 */
export async function getModuleLatestTestPassCommitId(
  query: GetModuleLatestTestPassCommitIdParams,
  options?: MethodOptions,
): Promise<string> {
  return request(`${baseUrl}/api/v1/submitTest/getModuleLatestPassCommitId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetModuleSubmitTestListParams {
  /** branch */
  branch: string;
  /**
   * moduleId
   * @format int64
   */
  moduleId: number;
}
/**
 * No description
 * @tags TestSheet
 * @name GetModuleSubmitTestList
 * @summary 查询模块的提测记录
 * @request GET:/api/v1/submitTest/getModuleSubmitTestList
 */
export async function getModuleSubmitTestList(
  query: GetModuleSubmitTestListParams,
  options?: MethodOptions,
): Promise<SubmitTestModuleBORes[]> {
  return request(`${baseUrl}/api/v1/submitTest/getModuleSubmitTestList`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPipelineInstanceAlterModuleListParams {
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId: number;
  /**
   * pipelineInstanceId
   * @format int64
   */
  pipelineInstanceId: number;
}
/**
 * No description
 * @tags TestSheet
 * @name GetPipelineInstanceAlterModuleList
 * @summary 根据构建实例查询变更模块信息
 * @request GET:/api/v1/submitTest/getPipelineInstanceAlterModuleList
 */
export async function getPipelineInstanceAlterModuleList(
  query: GetPipelineInstanceAlterModuleListParams,
  options?: MethodOptions,
): Promise<PipelineInstanceModuleVO[]> {
  return request(`${baseUrl}/api/v1/submitTest/getPipelineInstanceAlterModuleList`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRelatedSubmitTestParams {
  /**
   * submitTestId
   * @format int64
   */
  submitTestId: number;
}
/**
 * No description
 * @tags TestSheet
 * @name GetRelatedSubmitTest
 * @summary 获取相关的提测单（同一个版本计划，存在同模块且提测通过）
 * @request GET:/api/v1/submitTest/getRelatedSubmitTest
 */
export async function getRelatedSubmitTest(
  query: GetRelatedSubmitTestParams,
  options?: MethodOptions,
): Promise<SubmitTestBO[]> {
  return request(`${baseUrl}/api/v1/submitTest/getRelatedSubmitTest`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindSubmitTestListParams {
  /**
   * alterSheetId
   * @format int64
   */
  alterSheetId?: number;
  /**
   * collaborationSpaceId
   * @format int64
   */
  collaborationSpaceId?: number;
  /** showMyself */
  showMyself?: boolean;
  /** submitter */
  submitter?: string;
  /** verifier */
  verifier?: string;
  /** statusList */
  statusList?: 'CANCEL' | 'DRAFT' | 'TESTING' | 'TEST_FAIL' | 'TEST_PASS';
  /** search */
  search?: string;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
}
/**
 * No description
 * @tags TestSheet
 * @name FindSubmitTestList
 * @summary 查询提测单列表
 * @request GET:/api/v1/submitTest/list
 */
export async function findSubmitTestList(
  query?: FindSubmitTestListParams,
  options?: MethodOptions,
): Promise<PaginationResult<SubmitTestBO>> {
  return request(`${baseUrl}/api/v1/submitTest/list`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface NotifyModuleRiskParams {
  /**
   * submitTestId
   * @format int64
   */
  submitTestId: number;
}
/**
 * No description
 * @tags TestSheet
 * @name NotifyModuleRisk
 * @summary 通知提测模块风险
 * @request GET:/api/v1/submitTest/notifyModuleRisk
 */
export async function notifyModuleRisk(query: NotifyModuleRiskParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/submitTest/notifyModuleRisk`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface RemindReSubmitTestDueToNewCommitParams {
  /**
   * submitTestId
   * @format int64
   */
  submitTestId: number;
}
/**
 * No description
 * @tags TestSheet
 * @name RemindReSubmitTestDueToNewCommit
 * @summary 提测分支有新提交通知开发重新提测
 * @request POST:/api/v1/submitTest/remindReSubmitTest
 */
export async function remindReSubmitTestDueToNewCommit(
  query: RemindReSubmitTestDueToNewCommitParams,
  data: number[],
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/submitTest/remindReSubmitTest`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface SaveAndSubmitTestParams {
  /**
   * stepId
   * @format int64
   */
  stepId?: number;
}
/**
 * No description
 * @tags TestSheet
 * @name SaveAndSubmitTest
 * @summary 保存并提交提测单
 * @request POST:/api/v1/submitTest/saveAndSubmit
 */
export async function saveAndSubmitTest(
  data: SubmitTestFormDTO,
  query?: SaveAndSubmitTestParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/submitTest/saveAndSubmit`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface UpdateSubmitTestStatusParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** status */
  status: 'CANCEL' | 'DRAFT' | 'TESTING' | 'TEST_FAIL' | 'TEST_PASS';
}
/**
 * No description
 * @tags TestSheet
 * @name UpdateSubmitTestStatus
 * @summary 更新提测单状态
 * @request POST:/api/v1/submitTest/updateStatus
 */
export async function updateSubmitTestStatus(
  query: UpdateSubmitTestStatusParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/submitTest/updateStatus`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface UpdateSubmitTestVerifierParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** verifier */
  verifier: string;
}
/**
 * No description
 * @tags TestSheet
 * @name UpdateSubmitTestVerifier
 * @summary 更新验证者
 * @request POST:/api/v1/submitTest/updateVerifier
 */
export async function updateSubmitTestVerifier(
  query: UpdateSubmitTestVerifierParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/submitTest/updateVerifier`, {
    method: 'POST',
    params: query,
    ...options,
  });
}
