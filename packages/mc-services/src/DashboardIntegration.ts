/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { MetricComparativeData, MetricData } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface GetBuildStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId: number;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetBuildStatistics
 * @summary 构建数据统计
 * @request GET:/api/v1/dashboard/integration/build
 */
export async function getBuildStatistics(
  query: GetBuildStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/integration/build`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetBuildTrendStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
  /** identifiers */
  identifiers: string;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetBuildTrendStatistics
 * @summary 构建趋势数据统计
 * @request GET:/api/v1/dashboard/integration/buildTrend
 */
export async function getBuildTrendStatistics(
  query: GetBuildTrendStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/integration/buildTrend`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetComparativeStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId: number;
  /**
   * comparativeIntegrateAreaId
   * @format int64
   */
  comparativeIntegrateAreaId?: number;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetComparativeStatistics
 * @summary 对比集成区数据统计
 * @request GET:/api/v1/dashboard/integration/comparativeStatistics
 */
export async function getComparativeStatistics(
  query: GetComparativeStatisticsParams,
  options?: MethodOptions,
): Promise<MetricComparativeData[]> {
  return request(`${baseUrl}/api/v1/dashboard/integration/comparativeStatistics`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetIntegrationStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId: number;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetIntegrationStatistics
 * @summary 集成数据统计
 * @request GET:/api/v1/dashboard/integration/integration
 */
export async function getIntegrationStatistics(
  query: GetIntegrationStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/integration/integration`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetIntegrationTopRankStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId: number;
  /** integrateSheetType */
  integrateSheetType: string;
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetIntegrationTopRankStatistics
 * @summary 集成榜单数据统计
 * @request GET:/api/v1/dashboard/integration/integrationTopRank
 */
export async function getIntegrationTopRankStatistics(
  query: GetIntegrationTopRankStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData> {
  return request(`${baseUrl}/api/v1/dashboard/integration/integrationTopRank`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetIntegrationTendStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
  /** identifiers */
  identifiers: string;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetIntegrationTendStatistics
 * @summary 集成趋势数据统计
 * @request GET:/api/v1/dashboard/integration/integrationTrend
 */
export async function getIntegrationTendStatistics(
  query: GetIntegrationTendStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/integration/integrationTrend`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPublishStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId: number;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetPublishStatistics
 * @summary 发布数据统计
 * @request GET:/api/v1/dashboard/integration/publish
 */
export async function getPublishStatistics(
  query: GetPublishStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/integration/publish`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPublishTopRankStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId: number;
  /** publishType */
  publishType: string;
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetPublishTopRankStatistics
 * @summary 发布榜单数据统计
 * @request GET:/api/v1/dashboard/integration/publishTopRank
 */
export async function getPublishTopRankStatistics(
  query: GetPublishTopRankStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData> {
  return request(`${baseUrl}/api/v1/dashboard/integration/publishTopRank`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPublishTrendStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
  /** identifiers */
  identifiers: string;
}
/**
 * No description
 * @tags DashboardIntegration
 * @name GetPublishTrendStatistics
 * @summary 发布趋势数据统计
 * @request GET:/api/v1/dashboard/integration/publishTrend
 */
export async function getPublishTrendStatistics(
  query: GetPublishTrendStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/integration/publishTrend`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
