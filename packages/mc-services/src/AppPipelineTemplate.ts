/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  PipelineSceneVO,
  PipelineStageTemplate,
  PipelineStageUsageVO,
  PipelineTemplate,
  PipelineTemplateApproval,
  PipelineTemplateReq,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags AppPipelineTemplate
 * @name ListTemplates
 * @summary 获取某个应用的流水线模板
 * @request GET:/api/v1/app/{appId}/config/pipelineTemplates
 */
export async function listTemplates(appId: number, options?: MethodOptions): Promise<PipelineTemplate[]> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates`, {
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags AppPipelineTemplate
 * @name UpdateTemplate
 * @summary 更新流水线模板
 * @request PUT:/api/v1/app/{appId}/config/pipelineTemplates
 */
export async function updateTemplate(
  appId: string,
  data: PipelineTemplateReq,
  options?: MethodOptions,
): Promise<PipelineTemplateApproval> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates`, {
    method: 'PUT',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags AppPipelineTemplate
 * @name CreateTemplate
 * @summary 新建流水线模板
 * @request POST:/api/v1/app/{appId}/config/pipelineTemplates
 */
export async function createTemplate(
  appId: string,
  data: PipelineTemplateReq,
  options?: MethodOptions,
): Promise<PipelineTemplateApproval> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface GetDefaultPipelineTemplateParams {
  /** pipelineScene */
  pipelineScene:
    | 'AGILE_CI_ALTER_SHEET_BUILD'
    | 'AI_IOS_DEPENDENCY_ANALYSIS'
    | 'ALTER_SHEET_BUILD'
    | 'ALTER_SHEET_DEPLOY'
    | 'ALTER_SHEET_UNIT_TEST'
    | 'CONTINUOUS_INTEGRATE_AREA_BUILD'
    | 'DART_PATCH_CR_BUILD'
    | 'DART_PATCH_CR_DEPLOY'
    | 'DART_PATCH_RELEASE_BUILD'
    | 'HOT_PATCH_CR_BUILD'
    | 'HOT_PATCH_CR_DEPLOY'
    | 'HOT_PATCH_RELEASE_BUILD'
    | 'INLINE_PATCH_CR_BUILD'
    | 'INLINE_PATCH_CR_DEPLOY'
    | 'INLINE_PATCH_RELEASE_BUILD'
    | 'INSTANT_PATCH_CR_BUILD'
    | 'INSTANT_PATCH_CR_DEPLOY'
    | 'INSTANT_PATCH_RELEASE_BUILD'
    | 'INTEGRATE_AREA_BUILD'
    | 'INTEGRATE_SHEET_BUILD'
    | 'NATIVE_DYNAMIC_ALTER_SHEET_BUILD'
    | 'NATIVE_DYNAMIC_RELEASE_BUILD'
    | 'OHOS_CPP_PATCH_CR_BUILD'
    | 'OHOS_CPP_PATCH_CR_DEPLOY'
    | 'OHOS_CPP_PATCH_RELEASE_BUILD'
    | 'OHOS_TS_PATCH_CR_BUILD'
    | 'OHOS_TS_PATCH_CR_DEPLOY'
    | 'OHOS_TS_PATCH_RELEASE_BUILD'
    | 'PATCH_CR_BUILD'
    | 'PATCH_CR_DEPLOY'
    | 'PATCH_RELEASE_BUILD'
    | 'PLUGIN_DEPLOY'
    | 'RELEASE_AFTER_REVIEW'
    | 'RELEASE_AP_DEPLOY'
    | 'RELEASE_BEFORE_REVIEW'
    | 'RELEASE_BETA_AFTER_REVIEW'
    | 'RELEASE_BETA_BEFORE_REVIEW'
    | 'RELEASE_BETA_BUILD'
    | 'RELEASE_BETA_BUILD_CHANNEL'
    | 'RELEASE_BUILD'
    | 'RELEASE_BUILD_CHANNEL'
    | 'SO_PATCH_CR_BUILD'
    | 'SO_PATCH_CR_DEPLOY'
    | 'SO_PATCH_RELEASE_BUILD';
  /** stageUsage */
  stageUsage:
    | 'GOOGLE_PLAY_BUILD'
    | 'MAIN_FRAMEWORK_DEPLOY'
    | 'MODULE_BUILD'
    | 'MODULE_DEPLOY'
    | 'PUBLISH_REGRESSION_BUILD';
}
/**
 * No description
 * @tags AppPipelineTemplate
 * @name GetDefaultPipelineTemplate
 * @summary 获取特定作用域的默认流水线模板
 * @request GET:/api/v1/app/{appId}/config/pipelineTemplates/defaultPipelineTemplate
 */
export async function getDefaultPipelineTemplate(
  appId: number,
  query: GetDefaultPipelineTemplateParams,
  options?: MethodOptions,
): Promise<PipelineStageTemplate> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates/defaultPipelineTemplate`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FixNodeUsageInStageTemplateParams {
  /**
   * templateId
   * @format int64
   */
  templateId: number;
}
/**
 * No description
 * @tags AppPipelineTemplate
 * @name FixNodeUsageInStageTemplate
 * @summary 修正流水线阶段模版里面的用途
 * @request POST:/api/v1/app/{appId}/config/pipelineTemplates/fixNodeUsageInStageTemplate
 */
export async function fixNodeUsageInStageTemplate(
  appId: string,
  query: FixNodeUsageInStageTemplateParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates/fixNodeUsageInStageTemplate`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags AppPipelineTemplate
 * @name GetSupportedPipelineScenes
 * @summary 获取流水线支持的应用场景
 * @request GET:/api/v1/app/{appId}/config/pipelineTemplates/pipelineScenes
 */
export async function getSupportedPipelineScenes(appId: number, options?: MethodOptions): Promise<PipelineSceneVO[]> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates/pipelineScenes`, {
    method: 'GET',
    ...options,
  });
}

export interface GetSupportedPipelineStageUsagesParams {
  /** pipelineScene */
  pipelineScene:
    | 'AGILE_CI_ALTER_SHEET_BUILD'
    | 'AI_IOS_DEPENDENCY_ANALYSIS'
    | 'ALTER_SHEET_BUILD'
    | 'ALTER_SHEET_DEPLOY'
    | 'ALTER_SHEET_UNIT_TEST'
    | 'CONTINUOUS_INTEGRATE_AREA_BUILD'
    | 'DART_PATCH_CR_BUILD'
    | 'DART_PATCH_CR_DEPLOY'
    | 'DART_PATCH_RELEASE_BUILD'
    | 'HOT_PATCH_CR_BUILD'
    | 'HOT_PATCH_CR_DEPLOY'
    | 'HOT_PATCH_RELEASE_BUILD'
    | 'INLINE_PATCH_CR_BUILD'
    | 'INLINE_PATCH_CR_DEPLOY'
    | 'INLINE_PATCH_RELEASE_BUILD'
    | 'INSTANT_PATCH_CR_BUILD'
    | 'INSTANT_PATCH_CR_DEPLOY'
    | 'INSTANT_PATCH_RELEASE_BUILD'
    | 'INTEGRATE_AREA_BUILD'
    | 'INTEGRATE_SHEET_BUILD'
    | 'NATIVE_DYNAMIC_ALTER_SHEET_BUILD'
    | 'NATIVE_DYNAMIC_RELEASE_BUILD'
    | 'OHOS_CPP_PATCH_CR_BUILD'
    | 'OHOS_CPP_PATCH_CR_DEPLOY'
    | 'OHOS_CPP_PATCH_RELEASE_BUILD'
    | 'OHOS_TS_PATCH_CR_BUILD'
    | 'OHOS_TS_PATCH_CR_DEPLOY'
    | 'OHOS_TS_PATCH_RELEASE_BUILD'
    | 'PATCH_CR_BUILD'
    | 'PATCH_CR_DEPLOY'
    | 'PATCH_RELEASE_BUILD'
    | 'PLUGIN_DEPLOY'
    | 'RELEASE_AFTER_REVIEW'
    | 'RELEASE_AP_DEPLOY'
    | 'RELEASE_BEFORE_REVIEW'
    | 'RELEASE_BETA_AFTER_REVIEW'
    | 'RELEASE_BETA_BEFORE_REVIEW'
    | 'RELEASE_BETA_BUILD'
    | 'RELEASE_BETA_BUILD_CHANNEL'
    | 'RELEASE_BUILD'
    | 'RELEASE_BUILD_CHANNEL'
    | 'SO_PATCH_CR_BUILD'
    | 'SO_PATCH_CR_DEPLOY'
    | 'SO_PATCH_RELEASE_BUILD';
  /**
   * appId
   * @format int64
   */
  appId: number;
}
/**
 * No description
 * @tags AppPipelineTemplate
 * @name GetSupportedPipelineStageUsages
 * @summary 获取支持的阶段用途
 * @request GET:/api/v1/app/{appId}/config/pipelineTemplates/pipelineStageUsages
 */
export async function getSupportedPipelineStageUsages(
  appId: string,
  query: GetSupportedPipelineStageUsagesParams,
  options?: MethodOptions,
): Promise<PipelineStageUsageVO[]> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates/pipelineStageUsages`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags AppPipelineTemplate
 * @name ReInitIntegrateSheetPipelineTemplate
 * @summary 按照集成区的流水线模版，重新初始化集成单的流水线模版
 * @request GET:/api/v1/app/{appId}/config/pipelineTemplates/reInitIntegrateSheetPipelineTemplate
 */
export async function reInitIntegrateSheetPipelineTemplate(
  appId: number,
  options?: MethodOptions,
): Promise<PipelineTemplate> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates/reInitIntegrateSheetPipelineTemplate`, {
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags AppPipelineTemplate
 * @name GetSystemPipelineTemplate
 * @summary 获取应用可用的系统内置流水线模版
 * @request GET:/api/v1/app/{appId}/config/pipelineTemplates/systemPipelineTemplate
 */
export async function getSystemPipelineTemplate(appId: number, options?: MethodOptions): Promise<PipelineTemplate[]> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates/systemPipelineTemplate`, {
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags AppPipelineTemplate
 * @name ClonePipelineTemplate
 * @summary 克隆系统内置的流水线模版
 * @request POST:/api/v1/app/{appId}/config/pipelineTemplates/systemPipelineTemplate/clone
 */
export async function clonePipelineTemplate(
  appId: string,
  data: PipelineTemplateReq,
  options?: MethodOptions,
): Promise<PipelineTemplateApproval> {
  return request(`${baseUrl}/api/v1/app/${appId}/config/pipelineTemplates/systemPipelineTemplate/clone`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
