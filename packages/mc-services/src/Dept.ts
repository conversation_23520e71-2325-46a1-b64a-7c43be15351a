/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { BizIdentityLabel, DeptInfoVO, DeptVO } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface GetDeptListByNameParams {
  /** name */
  name?: string;
}
/**
 * @description 根据参数模糊查询相关部门
 * @tags Dept
 * @name GetDeptListByName
 * @summary 根据参数模糊查询相关部门
 * @request GET:/api/v1/dept/actions/search
 */
export async function getDeptListByName(query?: GetDeptListByNameParams, options?: MethodOptions): Promise<DeptVO[]> {
  return request(`${baseUrl}/api/v1/dept/actions/search`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetBizTagListParams {
  /** name */
  name?: string;
}
/**
 * @description 根据参数模糊查询相关业务大部门
 * @tags Dept
 * @name GetBizTagList
 * @summary 根据参数模糊查询相关业务大部门
 * @request GET:/api/v1/dept/biz_tags/actions/search
 */
export async function getBizTagList(query?: GetBizTagListParams, options?: MethodOptions): Promise<BizIdentityLabel[]> {
  return request(`${baseUrl}/api/v1/dept/biz_tags/actions/search`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetAllSubDeptsParams {
  /** deptNo */
  deptNo: string;
}
/**
 * No description
 * @tags Dept
 * @name GetAllSubDepts
 * @summary 获取所有子团队
 * @request GET:/api/v1/dept/getAllSubDepts
 */
export async function getAllSubDepts(query: GetAllSubDeptsParams, options?: MethodOptions): Promise<DeptInfoVO[]> {
  return request(`${baseUrl}/api/v1/dept/getAllSubDepts`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface SearchDeptByKeyParams {
  /** searchKey */
  searchKey?: string;
}
/**
 * No description
 * @tags Dept
 * @name SearchDeptByKey
 * @summary searchDeptByKey
 * @request GET:/api/v1/dept/searchByKeyword
 */
export async function searchDeptByKey(query?: SearchDeptByKeyParams, options?: MethodOptions): Promise<DeptInfoVO[]> {
  return request(`${baseUrl}/api/v1/dept/searchByKeyword`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
