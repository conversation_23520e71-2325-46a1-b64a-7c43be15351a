/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { AlterBranchInfo, CollaborationSpaceCodeBOReq, CollaborationSpaceCodeBORes } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags CollaborationSpaceCode
 * @name AddCollaborationSpaceCode
 * @summary 添加代码/模块到研发协作空间
 * @request POST:/api/v1/collaborationSpaceCode
 */
export async function addCollaborationSpaceCode(
  data: CollaborationSpaceCodeBOReq,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/collaborationSpaceCode`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface DeleteCollaborationSpaceCodeParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /**
   * collaborationSpaceId
   * @format int64
   */
  collaborationSpaceId: number;
}
/**
 * No description
 * @tags CollaborationSpaceCode
 * @name DeleteCollaborationSpaceCode
 * @summary 删除研发协作空间的代码/模块
 * @request DELETE:/api/v1/collaborationSpaceCode
 */
export async function deleteCollaborationSpaceCode(
  query: DeleteCollaborationSpaceCodeParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/collaborationSpaceCode`, {
    method: 'DELETE',
    params: query,
    ...options,
  });
}

export interface BatchAddCollaborationSpaceCodeParams {
  /**
   * collaborationSpaceId
   * @format int64
   */
  collaborationSpaceId: number;
}
/**
 * No description
 * @tags CollaborationSpaceCode
 * @name BatchAddCollaborationSpaceCode
 * @summary 批量添加代码/模块到研发协作空间
 * @request POST:/api/v1/collaborationSpaceCode/batch
 */
export async function batchAddCollaborationSpaceCode(
  query: BatchAddCollaborationSpaceCodeParams,
  data: CollaborationSpaceCodeBOReq[],
  options?: MethodOptions,
): Promise<number[]> {
  return request(`${baseUrl}/api/v1/collaborationSpaceCode/batch`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface QueryCrCollaborationCodeListParams {
  /**
   * collaborationSpaceId
   * @format int64
   */
  collaborationSpaceId: number;
}
/**
 * No description
 * @tags CollaborationSpaceCode
 * @name QueryCrCollaborationCodeList
 * @summary 查询研发协作空间的可以cr的模块
 * @request GET:/api/v1/collaborationSpaceCode/cr/list
 */
export async function queryCrCollaborationCodeList(
  query: QueryCrCollaborationCodeListParams,
  options?: MethodOptions,
): Promise<CollaborationSpaceCodeBORes[]> {
  return request(`${baseUrl}/api/v1/collaborationSpaceCode/cr/list`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetCollaborationSpaceCodeAlterBranchesParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
  /**
   * recentMonths
   * @format int32
   */
  recentMonths?: number;
}
/**
 * No description
 * @tags CollaborationSpaceCode
 * @name GetCollaborationSpaceCodeAlterBranches
 * @summary 获取研发协作空间代码/模块的变更分支数
 * @request GET:/api/v1/collaborationSpaceCode/getAlterBranches
 */
export async function getCollaborationSpaceCodeAlterBranches(
  query: GetCollaborationSpaceCodeAlterBranchesParams,
  options?: MethodOptions,
): Promise<AlterBranchInfo[]> {
  return request(`${baseUrl}/api/v1/collaborationSpaceCode/getAlterBranches`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface QueryCollaborationCodeListParams {
  /**
   * collaborationSpaceId
   * @format int64
   */
  collaborationSpaceId: number;
}
/**
 * No description
 * @tags CollaborationSpaceCode
 * @name QueryCollaborationCodeList
 * @summary 查询研发协作空间的代码/模块
 * @request GET:/api/v1/collaborationSpaceCode/list
 */
export async function queryCollaborationCodeList(
  query: QueryCollaborationCodeListParams,
  options?: MethodOptions,
): Promise<CollaborationSpaceCodeBORes[]> {
  return request(`${baseUrl}/api/v1/collaborationSpaceCode/list`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags CollaborationSpaceCode
 * @name UpdateCollaborationSpaceCode
 * @summary 编辑研发协作空间的代码/模块
 * @request PUT:/api/v1/collaborationSpaceCode/update
 */
export async function updateCollaborationSpaceCode(
  data: CollaborationSpaceCodeBOReq,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/collaborationSpaceCode/update`, {
    method: 'PUT',
    body: data as any,
    ...options,
  });
}
