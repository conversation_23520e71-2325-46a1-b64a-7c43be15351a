/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { OpenClientReq, OpenClientRes } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags OpenClient
 * @name Apply
 * @summary 申请注册OpenClient
 * @request POST:/api/v1/open/client/apply
 */
export async function apply(data: OpenClientReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/open/client/apply`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface DeleteByIdParams {
  /**
   * clientId
   * @format int64
   */
  clientId: number;
}
/**
 * No description
 * @tags OpenClient
 * @name DeleteById
 * @summary 删除OpenClient
 * @request POST:/api/v1/open/client/deleteById
 */
export async function deleteById(query: DeleteByIdParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/open/client/deleteById`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags OpenClient
 * @name FindAll
 * @summary 查询所有Client
 * @request POST:/api/v1/open/client/findAll
 */
export async function findAll(options?: MethodOptions): Promise<OpenClientRes[]> {
  return request(`${baseUrl}/api/v1/open/client/findAll`, {
    method: 'POST',
    ...options,
  });
}

export interface GrantClientParams {
  /**
   * consumerId
   * @format int64
   */
  consumerId: number;
  /**
   * clientId
   * @format int64
   */
  clientId: number;
}
/**
 * No description
 * @tags OpenClient
 * @name GrantClient
 * @summary 授权调用者客户端调用权限
 * @request POST:/api/v1/open/client/grantClient
 */
export async function grantClient(query: GrantClientParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/open/client/grantClient`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface HandoverParams {
  /**
   * clientId
   * @format int64
   */
  clientId: number;
  /** target */
  target: string;
}
/**
 * No description
 * @tags OpenClient
 * @name Handover
 * @summary 转交管理员
 * @request POST:/api/v1/open/client/handover
 */
export async function handover(query: HandoverParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/open/client/handover`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags OpenClient
 * @name Register
 * @summary 注册OpenClient
 * @request POST:/api/v1/open/client/register
 */
export async function register(data: OpenClientReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/open/client/register`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags OpenClient
 * @name Update
 * @summary 更新OpenClient
 * @request POST:/api/v1/open/client/update
 */
export async function update(data: OpenClientReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/open/client/update`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
