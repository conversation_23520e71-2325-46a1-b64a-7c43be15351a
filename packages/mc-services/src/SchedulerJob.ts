/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { getBaseUrl, MethodOptions } from './HttpClient';
import { request } from './Request';
import {
  JobArtifact,
  JobSchedule,
  JobTask,
  LogContent,
  LogPageContent,
  ResponseEntity,
} from './SchedulerDataContracts';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com');

/**
 * No description
 * @tags SchedulerJob
 * @name GetJobSchedule
 * @summary getJobSchedule
 * @request GET:/scheduler/jobs/{jobScheduleId}
 */
export async function getJobSchedule(jobScheduleId: number, options?: MethodOptions): Promise<JobSchedule> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name QueryJobArtifact
 * @summary queryJobArtifact
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts
 */
export async function queryJobArtifact(jobScheduleId: number, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name GetJobArtifact
 * @summary getJobArtifact
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}
 */
export async function getJobArtifact(
  jobScheduleId: number,
  artifactId: string,
  options?: MethodOptions,
): Promise<JobArtifact> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name GetArtifactDownload
 * @summary artifactDownload
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/download
 */
export async function getArtifactDownload(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/download`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name ArtifactMetadataUsingHead
 * @summary artifactMetadata
 * @request HEAD:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/download
 */
export async function artifactMetadataUsingHead(
  jobScheduleId: number,
  artifactId: string,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/download`, {
    method: 'HEAD',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name ArtifactDownloadHtml
 * @summary artifactDownloadHTML
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/download.htm
 */
export async function artifactDownloadHtml(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/download.htm`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name GetArtifactDownloadFile
 * @summary artifactDownload
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/download/{fileName}
 */
export async function getArtifactDownloadFile(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/download/${fileName}`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name ArtifactMetadataUsingHead1
 * @summary artifactMetadata
 * @request HEAD:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/download/{fileName}
 */
export async function artifactMetadataUsingHead1(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/download/${fileName}`, {
    method: 'HEAD',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name ArtifactDownloadPlist
 * @summary artifactDownloadPlist
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/plist
 */
export async function artifactDownloadPlist(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<ResponseEntity> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/plist`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name ArtifactDownload
 * @summary artifactDownload
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/{fileName}/download
 */
export async function artifactDownload(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/${fileName}/download`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name ArtifactMetadataUsingHead2
 * @summary artifactMetadata
 * @request HEAD:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/{fileName}/download
 */
export async function artifactMetadataUsingHead2(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/${fileName}/download`, {
    method: 'HEAD',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name DownloadArtifactFile
 * @summary artifactDownloadHTML
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/{fileName}/download.htm
 */
export async function downloadArtifactFile(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/${fileName}/download.htm`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name GetArtifactFilePlist
 * @summary artifactDownloadPlist
 * @request GET:/scheduler/jobs/{jobScheduleId}/artifacts/{artifactId}/{fileName}/plist
 */
export async function getArtifactFilePlist(
  jobScheduleId: number,
  artifactId: string,
  fileName: string,
  options?: MethodOptions,
): Promise<ResponseEntity> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/artifacts/${artifactId}/${fileName}/plist`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name QueryJobTask
 * @summary queryJobTask
 * @request GET:/scheduler/jobs/{jobScheduleId}/tasks
 */
export async function queryJobTask(jobScheduleId: number, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/tasks`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name GetJobTask
 * @summary getJobTask
 * @request GET:/scheduler/jobs/{jobScheduleId}/tasks/{taskId}
 */
export async function getJobTask(jobScheduleId: number, taskId: string, options?: MethodOptions): Promise<JobTask> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/tasks/${taskId}`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

export interface GetLogContentParams {
  /**
   * start
   * @format int64
   */
  start?: number;
  /**
   * length
   * @format int64
   */
  length?: number;
}
/**
 * No description
 * @tags SchedulerJob
 * @name GetLogContent
 * @summary getLogContent
 * @request GET:/scheduler/jobs/{jobScheduleId}/tasks/{taskId}/logContent
 */
export async function getLogContent(
  jobScheduleId: number,
  taskId: string,
  query?: GetLogContentParams,
  options?: MethodOptions,
): Promise<LogContent> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/tasks/${taskId}/logContent`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags SchedulerJob
 * @name LogDownload
 * @summary logDownload
 * @request GET:/scheduler/jobs/{jobScheduleId}/tasks/{taskId}/logDownload
 */
export async function logDownload(jobScheduleId: number, taskId: string, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/tasks/${taskId}/logDownload`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

export interface GetLogPageContentParams {
  /**
   * pageSize
   * @format int64
   */
  pageSize?: number;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
}
/**
 * No description
 * @tags SchedulerJob
 * @name GetLogPageContent
 * @summary getLogPageContent
 * @request GET:/scheduler/jobs/{jobScheduleId}/tasks/{taskId}/logPageContent
 */
export async function getLogPageContent(
  jobScheduleId: number,
  taskId: string,
  query?: GetLogPageContentParams,
  options?: MethodOptions,
): Promise<LogPageContent> {
  return request(`${baseUrl}/scheduler/jobs/${jobScheduleId}/tasks/${taskId}/logPageContent`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}
