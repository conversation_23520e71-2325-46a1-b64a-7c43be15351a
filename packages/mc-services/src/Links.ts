/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags Links
 * @name CreateConversation
 * @summary 创建会话
 * @request POST:/api/v1/links/createConversation
 */
export async function createConversation(
  data: Record<string, object>,
  options?: MethodOptions,
): Promise<Record<string, any>> {
  return request(`${baseUrl}/api/v1/links/createConversation`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface GetConversationCountParams {
  /** status */
  status?: string;
}
/**
 * No description
 * @tags Links
 * @name GetConversationCount
 * @summary 获取指定用户的指定状态的工单的数量
 * @request GET:/api/v1/links/getConversationCount
 */
export async function getConversationCount(
  query?: GetConversationCountParams,
  options?: MethodOptions,
): Promise<Record<string, any>> {
  return request(`${baseUrl}/api/v1/links/getConversationCount`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetConversationInfoParams {
  /** conversationId */
  conversationId: string;
}
/**
 * No description
 * @tags Links
 * @name GetConversationInfo
 * @summary 获取会话的相关信息
 * @request GET:/api/v1/links/getConversationInfo
 */
export async function getConversationInfo(
  query: GetConversationInfoParams,
  options?: MethodOptions,
): Promise<Record<string, any>> {
  return request(`${baseUrl}/api/v1/links/getConversationInfo`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetConversationsParams {
  /** status */
  status?: string;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
}
/**
 * No description
 * @tags Links
 * @name GetConversations
 * @summary 获取指定租户下的会话列表
 * @request GET:/api/v1/links/getConversations
 */
export async function getConversations(
  query?: GetConversationsParams,
  options?: MethodOptions,
): Promise<Record<string, any>> {
  return request(`${baseUrl}/api/v1/links/getConversations`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetSnippetInfoParams {
  /** snippetId */
  snippetId: string;
}
/**
 * No description
 * @tags Links
 * @name GetSnippetInfo
 * @summary 获取指定租户下的指定id的知识点
 * @request GET:/api/v1/links/getSnippetInfo
 */
export async function getSnippetInfo(
  query: GetSnippetInfoParams,
  options?: MethodOptions,
): Promise<Record<string, any>> {
  return request(`${baseUrl}/api/v1/links/getSnippetInfo`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface SearchParams {
  /** keyword */
  keyword?: string;
  /** type */
  type?: string;
  /** categoryId */
  categoryId?: string;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
}
/**
 * No description
 * @tags Links
 * @name Search
 * @summary 搜索LinkS会话及知识库
 * @request GET:/api/v1/links/search
 */
export async function search(query?: SearchParams, options?: MethodOptions): Promise<Record<string, any>> {
  return request(`${baseUrl}/api/v1/links/search`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
