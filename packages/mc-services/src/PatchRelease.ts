/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  Dependency,
  PatchRegressionInfo,
  PatchRelease,
  PatchReleaseArchive,
  PatchReleaseCreate,
  PatchReleaseUpdate,
  Pipeline,
  PipelineExecuteParams,
  RollbackCheckerVO,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface CheckIntegrationDependenciesChangedParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name CheckIntegrationDependenciesChanged
 * @summary checkIntegrationDependenciesChanged
 * @request GET:/api/v1/patch/release/checkIntegrationDependenciesChanged
 */
export async function checkIntegrationDependenciesChanged(
  query: CheckIntegrationDependenciesChangedParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/release/checkIntegrationDependenciesChanged`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ConfirmTestPassParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** isPass */
  isPass: boolean;
}
/**
 * No description
 * @tags PatchRelease
 * @name ConfirmTestPass
 * @summary confirmTestPass
 * @request GET:/api/v1/patch/release/confirmTest
 */
export async function confirmTestPass(query: ConfirmTestPassParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/release/confirmTest`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags PatchRelease
 * @name Create
 * @summary create
 * @request POST:/api/v1/patch/release/create
 */
export async function create(data: PatchReleaseCreate, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/patch/release/create`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface DeleteByPatchReleaseIdParams {
  /**
   * patchReleaseId
   * @format int64
   */
  patchReleaseId?: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name DeleteByPatchReleaseId
 * @summary deleteByPatchReleaseId
 * @request GET:/api/v1/patch/release/deleteByPatchReleaseId
 */
export async function deleteByPatchReleaseId(
  query?: DeleteByPatchReleaseIdParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/release/deleteByPatchReleaseId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface BuildParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name Build
 * @summary build
 * @request POST:/api/v1/patch/release/execute
 */
export async function build(
  query: BuildParams,
  data: PipelineExecuteParams[],
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/patch/release/execute`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface FindByCrIdParams {
  /**
   * crId
   * @format int64
   */
  crId: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name FindByCrId
 * @summary findByCrId
 * @request GET:/api/v1/patch/release/findByCrId
 */
export async function findByCrId(query: FindByCrIdParams, options?: MethodOptions): Promise<PatchRelease[]> {
  return request(`${baseUrl}/api/v1/patch/release/findByCrId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindByPublishAreaIdParams {
  /**
   * publishAreaId
   * @format int64
   */
  publishAreaId: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name FindByPublishAreaId
 * @summary findByPublishAreaId
 * @request GET:/api/v1/patch/release/findByPublishAreaId
 */
export async function findByPublishAreaId(
  query: FindByPublishAreaIdParams,
  options?: MethodOptions,
): Promise<PatchRelease[]> {
  return request(`${baseUrl}/api/v1/patch/release/findByPublishAreaId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindPipelineParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name FindPipeline
 * @summary findPipeline
 * @request GET:/api/v1/patch/release/findPipeline
 */
export async function findPipeline(query: FindPipelineParams, options?: MethodOptions): Promise<Pipeline[]> {
  return request(`${baseUrl}/api/v1/patch/release/findPipeline`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindRecordsByPatchReleaseIdParams {
  /**
   * patchReleaseId
   * @format int64
   */
  patchReleaseId?: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name FindRecordsByPatchReleaseId
 * @summary findRecordsByPatchReleaseId
 * @request GET:/api/v1/patch/release/findRecordsByPatchReleaseId
 */
export async function findRecordsByPatchReleaseId(
  query?: FindRecordsByPatchReleaseIdParams,
  options?: MethodOptions,
): Promise<PatchReleaseArchive[]> {
  return request(`${baseUrl}/api/v1/patch/release/findRecordsByPatchReleaseId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetDependenciesParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name GetDependencies
 * @summary getDependencies
 * @request GET:/api/v1/patch/release/getDependencies
 */
export async function getDependencies(query: GetDependenciesParams, options?: MethodOptions): Promise<Dependency[]> {
  return request(`${baseUrl}/api/v1/patch/release/getDependencies`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPatchInfoParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name GetPatchInfo
 * @summary getPatchInfo
 * @request GET:/api/v1/patch/release/getPatchInfo
 */
export async function getPatchInfo(query: GetPatchInfoParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/release/getPatchInfo`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPatchInfoForMtl3Params {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name GetPatchInfoForMtl3
 * @summary getPatchInfoForMtl3
 * @request GET:/api/v1/patch/release/getPatchInfoForMtl3
 */
export async function getPatchInfoForMtl3(query: GetPatchInfoForMtl3Params, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/release/getPatchInfoForMtl3`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPublishBatchDefaultParamsParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name GetPublishBatchDefaultParams
 * @summary 获取发布批次的默认参数
 * @request GET:/api/v1/patch/release/getPublishBatchDefaultParams
 */
export async function getPublishBatchDefaultParams(
  query: GetPublishBatchDefaultParamsParams,
  options?: MethodOptions,
): Promise<Record<string, any>> {
  return request(`${baseUrl}/api/v1/patch/release/getPublishBatchDefaultParams`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRegressionInfoParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name GetRegressionInfo
 * @summary getRegressionInfo
 * @request GET:/api/v1/patch/release/getRegressionInfo
 */
export async function getRegressionInfo(
  query: GetRegressionInfoParams,
  options?: MethodOptions,
): Promise<PatchRegressionInfo> {
  return request(`${baseUrl}/api/v1/patch/release/getRegressionInfo`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface RollbackParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name Rollback
 * @summary rollback
 * @request GET:/api/v1/patch/release/rollback
 */
export async function rollback(query: RollbackParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/patch/release/rollback`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface RollbackCheckerParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name RollbackChecker
 * @summary rollbackChecker
 * @request GET:/api/v1/patch/release/rollbackChecker
 */
export async function rollbackChecker(
  query: RollbackCheckerParams,
  options?: MethodOptions,
): Promise<RollbackCheckerVO> {
  return request(`${baseUrl}/api/v1/patch/release/rollbackChecker`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface StopReleaseParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name StopRelease
 * @summary stopRelease
 * @request GET:/api/v1/patch/release/stopRelease
 */
export async function stopRelease(query: StopReleaseParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/release/stopRelease`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface TestParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name Test
 * @summary test
 * @request GET:/api/v1/patch/release/test
 */
export async function test(query: TestParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/release/test`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface TestFetchPatchVersionParams {
  /**
   * appId
   * @format int64
   */
  appId?: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name TestFetchPatchVersion
 * @summary testFetchPatchVersion
 * @request GET:/api/v1/patch/release/testFetchPatchVersion
 */
export async function testFetchPatchVersion(
  query?: TestFetchPatchVersionParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/patch/release/testFetchPatchVersion`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface TestRollbackDepsParams {
  /**
   * patchReleaseId
   * @format int64
   */
  patchReleaseId?: number;
}
/**
 * No description
 * @tags PatchRelease
 * @name TestRollbackDeps
 * @summary testRollbackDeps
 * @request GET:/api/v1/patch/release/testRollbackDeps
 */
export async function testRollbackDeps(query?: TestRollbackDepsParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/release/testRollbackDeps`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags PatchRelease
 * @name Update
 * @summary update
 * @request POST:/api/v1/patch/release/update
 */
export async function update(data: PatchReleaseUpdate, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/release/update`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface UpdateStatusParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** status */
  status:
    | 'BUILD_ING'
    | 'CLOSED'
    | 'INIT'
    | 'PUBLISH_ING'
    | 'PUBLISH_STOP'
    | 'PUBLISH_STOP_ROLLBACK'
    | 'PUBLISH_SUCCESS'
    | 'TEST_FAIL'
    | 'TEST_PASS';
}
/**
 * No description
 * @tags PatchRelease
 * @name UpdateStatus
 * @summary updateStatus
 * @request GET:/api/v1/patch/release/updateStatus
 */
export async function updateStatus(query: UpdateStatusParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/release/updateStatus`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ArchivePatchParams {
  /**
   * patchReleaseId
   * @format int64
   */
  patchReleaseId: number;
  /**
   * releaseIds
   * @format int64
   */
  releaseIds?: number;
  /** patchArchiveType */
  patchArchiveType: 'ARCHIVE' | 'ARCHIVE_WITHOUT_RELEASE' | 'UNARCHIVE' | 'UNSET';
}
/**
 * No description
 * @tags PatchRelease
 * @name ArchivePatch
 * @summary archivePatch
 * @request GET:/api/v1/patch/release/virtualArchivePatch
 */
export async function archivePatch(query: ArchivePatchParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/patch/release/virtualArchivePatch`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
