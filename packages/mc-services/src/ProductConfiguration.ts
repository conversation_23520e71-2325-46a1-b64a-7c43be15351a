/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { ProductConfigCreateRequest, ProductConfigInfoVO } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * @description 增加产品配置信息
 * @tags ProductConfiguration
 * @name SaveProductConfig
 * @summary 增加产品配置信息
 * @request POST:/api/v1/product_config
 */
export async function saveProductConfig(data: ProductConfigCreateRequest, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/product_config`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * @description 查询所有的产品配置
 * @tags ProductConfiguration
 * @name GetAllProductConfig
 * @summary 查询所有的产品配置
 * @request GET:/api/v1/product_config/all
 */
export async function getAllProductConfig(options?: MethodOptions): Promise<ProductConfigInfoVO[]> {
  return request(`${baseUrl}/api/v1/product_config/all`, {
    method: 'GET',
    ...options,
  });
}

export interface GetProductConfigListParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
}
/**
 * @description 产品配置列表
 * @tags ProductConfiguration
 * @name GetProductConfigList
 * @summary 根据 appId 获取产品配置列表
 * @request GET:/api/v1/product_config/getProductConfigByAppId
 */
export async function getProductConfigList(
  query: GetProductConfigListParams,
  options?: MethodOptions,
): Promise<ProductConfigInfoVO[]> {
  return request(`${baseUrl}/api/v1/product_config/getProductConfigByAppId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * @description 更新产品配置信息
 * @tags ProductConfiguration
 * @name UpdateProductConfig
 * @summary 更新产品配置信息
 * @request PUT:/api/v1/product_config/{id}
 */
export async function updateProductConfig(
  id: number,
  data: ProductConfigCreateRequest,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/product_config/${id}`, {
    method: 'PUT',
    body: data as any,
    ...options,
  });
}

/**
 * @description 删除产品配置信息
 * @tags ProductConfiguration
 * @name DeleteProductConfig
 * @summary 删除产品配置信息
 * @request DELETE:/api/v1/product_config/{id}
 */
export async function deleteProductConfig(id: number, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/product_config/${id}`, {
    method: 'DELETE',
    ...options,
  });
}

/**
 * @description 更新部分产品配置信息
 * @tags ProductConfiguration
 * @name PatchProductConfigUsingPatch
 * @summary 更新部分产品配置信息
 * @request PATCH:/api/v1/product_config/{id}
 */
export async function patchProductConfigUsingPatch(
  id: number,
  data: ProductConfigCreateRequest,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/product_config/${id}`, {
    method: 'PATCH',
    body: data as any,
    ...options,
  });
}
