/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { FlowDefinitionInfoEntity, FlowStartDefinition, FlowTaskDefinition } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface DeleteHistoryInstancesStartBeforeParams {
  /**
   * timestamp
   * @format int64
   */
  timestamp: number;
}
/**
 * No description
 * @tags Flow
 * @name DeleteHistoryInstancesStartBefore
 * @summary 删除某个时间点之前的所有历史运行实例
 * @request GET:/api/v1/flow/deleteHistoryInstancesStartBefore
 */
export async function deleteHistoryInstancesStartBefore(
  query: DeleteHistoryInstancesStartBeforeParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/flow/deleteHistoryInstancesStartBefore`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags Flow
 * @name GetFlowDefinitionInfos
 * @summary 查询所有定义信息
 * @request GET:/api/v1/flow/getFlowDefinitionInfos
 */
export async function getFlowDefinitionInfos(options?: MethodOptions): Promise<FlowDefinitionInfoEntity[]> {
  return request(`${baseUrl}/api/v1/flow/getFlowDefinitionInfos`, {
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags Flow
 * @name GetFlowStartDefinitions
 * @summary 查询所有启动事件定义
 * @request GET:/api/v1/flow/getFlowStartDefinitions
 */
export async function getFlowStartDefinitions(options?: MethodOptions): Promise<FlowStartDefinition[]> {
  return request(`${baseUrl}/api/v1/flow/getFlowStartDefinitions`, {
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags Flow
 * @name GetFlowTaskDefinitions
 * @summary 查询所有任务定义
 * @request GET:/api/v1/flow/getFlowTaskDefinitions
 */
export async function getFlowTaskDefinitions(options?: MethodOptions): Promise<FlowTaskDefinition[]> {
  return request(`${baseUrl}/api/v1/flow/getFlowTaskDefinitions`, {
    method: 'GET',
    ...options,
  });
}
