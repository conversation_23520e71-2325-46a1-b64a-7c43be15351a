/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import {
  AoneBindAlterInfo,
  InsightGroupStatisticsDTO,
  InsightItemCount,
  InsightItemDTO,
  InsightItemIntegrateSchedule,
  InsightItemListQryVO,
  InsightItemListUpdateVO,
  ReleaseBO,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface FindByItemIdParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId?: number;
  /**
   * releaseId
   * @format int64
   */
  releaseId?: number;
  /** type */
  type?: string;
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags Insight
 * @name FindByItemId
 * @summary findByItemId
 * @request GET:/api/v1/publish/insight/findByItemId
 */
export async function findByItemId(query: FindByItemIdParams, options?: MethodOptions): Promise<InsightItemDTO> {
  return request(`${baseUrl}/api/v1/publish/insight/findByItemId`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ListGroupStatisticsParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
  /** appVersion */
  appVersion: string;
}
/**
 * No description
 * @tags Insight
 * @name ListGroupStatistics
 * @summary listGroupStatistics
 * @request GET:/api/v1/publish/insight/groups/statistics
 */
export async function listGroupStatistics(
  query: ListGroupStatisticsParams,
  options?: MethodOptions,
): Promise<InsightGroupStatisticsDTO[]> {
  return request(`${baseUrl}/api/v1/publish/insight/groups/statistics`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface DingNotifyParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /**
   * id
   * @format int64
   */
  id: number;
  /** notifyType */
  notifyType: 'DINGDING_BOSS' | 'DINGDING_MSG' | 'DINGDING_MSG_BOSS' | 'DINGDING_PHONE';
  /**
   * versionId
   * @format int64
   */
  versionId?: number;
  /** version */
  version?: string;
}
/**
 * No description
 * @tags Insight
 * @name DingNotify
 * @summary 获取已处理/未处理数据
 * @request GET:/api/v1/publish/insight/insight/dingNotify
 */
export async function dingNotify(query: DingNotifyParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/dingNotify`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ListInsightFeedbacksParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags Insight
 * @name ListInsightFeedbacks
 * @summary 获取舆情列表页
 * @request GET:/api/v1/publish/insight/insight/feedback
 */
export async function listInsightFeedbacks(query: ListInsightFeedbacksParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/feedback`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FetchInsightItemCountParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /** appVersion */
  appVersion?: string;
  /** type */
  type?: string;
  /** showMyself */
  showMyself?: boolean;
}
/**
 * No description
 * @tags Insight
 * @name FetchInsightItemCount
 * @summary 获取已处理/未处理数据
 * @request GET:/api/v1/publish/insight/insight/getCount
 */
export async function fetchInsightItemCount(
  query: FetchInsightItemCountParams,
  options?: MethodOptions,
): Promise<InsightItemCount> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/getCount`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetIntegrateScheduleItemsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId: number;
  /** showMyself */
  showMyself: boolean;
}
/**
 * No description
 * @tags Insight
 * @name GetIntegrateScheduleItems
 * @summary 获取所有紧急集成数量/已经处理的占比
 * @request GET:/api/v1/publish/insight/insight/getIntegrateScheduleItems
 */
export async function getIntegrateScheduleItems(
  query: GetIntegrateScheduleItemsParams,
  options?: MethodOptions,
): Promise<InsightItemIntegrateSchedule> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/getIntegrateScheduleItems`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface InitHistoryCrashItemsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /**
   * groupId
   * @format int64
   */
  groupId: number;
}
/**
 * No description
 * @tags Insight
 * @name InitHistoryCrashItems
 * @summary 初始化历史crash洞察项
 * @request GET:/api/v1/publish/insight/insight/initHistoryCrashItems
 */
export async function initHistoryCrashItems(
  query: InitHistoryCrashItemsParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/initHistoryCrashItems`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ListInsightReleasesParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * releaseId
   * @format int64
   */
  releaseId?: number;
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId: number;
}
/**
 * No description
 * @tags Insight
 * @name ListInsightReleases
 * @summary 获取洞察页发布单列表
 * @request GET:/api/v1/publish/insight/insight/release
 */
export async function listInsightReleases(
  query: ListInsightReleasesParams,
  options?: MethodOptions,
): Promise<ReleaseBO[]> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/release`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface TestFeedbackSyncParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /** insert */
  insert: boolean;
}
/**
 * No description
 * @tags Insight
 * @name TestFeedbackSync
 * @summary 同步舆情信息
 * @request GET:/api/v1/publish/insight/insight/testFeedbackSync
 */
export async function testFeedbackSync(query: TestFeedbackSyncParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/testFeedbackSync`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface UpdateCrashStatusParams {
  /**
   * groupId
   * @format int64
   */
  groupId: number;
}
/**
 * No description
 * @tags Insight
 * @name UpdateCrashStatus
 * @summary 更新crash状态
 * @request GET:/api/v1/publish/insight/insight/updateCrashStatus
 */
export async function updateCrashStatus(query: UpdateCrashStatusParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/updateCrashStatus`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface UpdateInsightItemValidatorParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** validator */
  validator: string;
}
/**
 * No description
 * @tags Insight
 * @name UpdateInsightItemValidator
 * @summary 修改验证人
 * @request GET:/api/v1/publish/insight/insight/updateInsightItemValidator
 */
export async function updateInsightItemValidator(
  query: UpdateInsightItemValidatorParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/publish/insight/insight/updateInsightItemValidator`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ListItemsParams {
  /**
   * groupId
   * @format int64
   */
  groupId: number;
}
/**
 * No description
 * @tags Insight
 * @name ListItems
 * @summary listItems
 * @request GET:/api/v1/publish/insight/items
 */
export async function listItems(query: ListItemsParams, options?: MethodOptions): Promise<InsightItemDTO[]> {
  return request(`${baseUrl}/api/v1/publish/insight/items`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ConfirmItemParams {
  /**
   * itemId
   * @format int64
   */
  itemId: number;
  /** pass */
  pass: boolean;
}
/**
 * No description
 * @tags Insight
 * @name ConfirmItem
 * @summary confirmItem
 * @request PUT:/api/v1/publish/insight/items/confirm
 */
export async function confirmItem(query: ConfirmItemParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/publish/insight/items/confirm`, {
    method: 'PUT',
    params: query,
    ...options,
  });
}

export interface RemoveInsightGroupParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags Insight
 * @name RemoveInsightGroup
 * @summary removeInsightGroup
 * @request DELETE:/api/v1/publish/insight/removeInsightGroup
 */
export async function removeInsightGroup(query: RemoveInsightGroupParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/publish/insight/removeInsightGroup`, {
    method: 'DELETE',
    params: query,
    ...options,
  });
}

export interface RemoveInsightItemParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags Insight
 * @name RemoveInsightItem
 * @summary removeInsightItem
 * @request DELETE:/api/v1/publish/insight/removeInsightItem
 */
export async function removeInsightItem(query: RemoveInsightItemParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/publish/insight/removeInsightItem`, {
    method: 'DELETE',
    params: query,
    ...options,
  });
}

export interface TriggerParams {
  /**
   * groupId
   * @format int64
   */
  groupId: number;
}
/**
 * No description
 * @tags Insight
 * @name Trigger
 * @summary trigger
 * @request POST:/api/v1/publish/insight/trigger
 */
export async function trigger(query: TriggerParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/publish/insight/trigger`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags Insight
 * @name ListWorkbenchItems
 * @summary 发布工作台洞察项查询
 * @request POST:/api/v1/publish/insight/workbench/items
 */
export async function listWorkbenchItems(
  data: InsightItemListQryVO,
  options?: MethodOptions,
): Promise<PaginationResult<InsightItemDTO>> {
  return request(`${baseUrl}/api/v1/publish/insight/workbench/items`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags Insight
 * @name AddItem
 * @summary 新增问题反馈
 * @request POST:/api/v1/publish/insight/workbench/items/add
 */
export async function addItem(data: InsightItemListUpdateVO, options?: MethodOptions): Promise<AoneBindAlterInfo> {
  return request(`${baseUrl}/api/v1/publish/insight/workbench/items/add`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags Insight
 * @name UpdateItems
 * @summary 发布动作台洞察项更新
 * @request POST:/api/v1/publish/insight/workbench/items/update
 */
export async function updateItems(
  data: InsightItemListUpdateVO[],
  options?: MethodOptions,
): Promise<AoneBindAlterInfo> {
  return request(`${baseUrl}/api/v1/publish/insight/workbench/items/update`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
