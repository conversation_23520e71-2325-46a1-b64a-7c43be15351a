/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface GetPhoneUrlParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
  /** packageUrl */
  packageUrl: string;
  /** appVersion */
  appVersion?: string;
}
/**
 * No description
 * @tags Phone
 * @name GetPhoneUrl
 * @summary 获取手机中台的访问地址
 * @request GET:/api/v1/phone/getPhoneUrl
 */
export async function getPhoneUrl(query: GetPhoneUrlParams, options?: MethodOptions): Promise<string> {
  return request(`${baseUrl}/api/v1/phone/getPhoneUrl`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPhoneUrlByPipelineParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
  /** packageUrl */
  packageUrl: string;
  /** appVersion */
  appVersion?: string;
}
/**
 * No description
 * @tags Phone
 * @name GetPhoneUrlByPipeline
 * @summary 获取手机中台的访问地址
 * @request GET:/api/v1/phone/getPhoneUrlByPipeline
 */
export async function getPhoneUrlByPipeline(
  query: GetPhoneUrlByPipelineParams,
  options?: MethodOptions,
): Promise<string> {
  return request(`${baseUrl}/api/v1/phone/getPhoneUrlByPipeline`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
