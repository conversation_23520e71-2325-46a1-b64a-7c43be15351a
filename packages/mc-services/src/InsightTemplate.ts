/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  InsightGroupTemplateDTOReq,
  InsightGroupTemplateDTORes,
  InsightGroupTemplateListQry,
  InsightItemTemplateDTOReq,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags InsightTemplate
 * @name AddGroupTemplate
 * @summary addGroupTemplate
 * @request POST:/api/v1/publish/insight/template/addGroupTemplate
 */
export async function addGroupTemplate(data: InsightGroupTemplateDTOReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/publish/insight/template/addGroupTemplate`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags InsightTemplate
 * @name AddItemTemplate
 * @summary addItemTemplate
 * @request POST:/api/v1/publish/insight/template/addItemTemplate
 */
export async function addItemTemplate(data: InsightItemTemplateDTOReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/publish/insight/template/addItemTemplate`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags InsightTemplate
 * @name FindGroupTemplateByQuery
 * @summary findGroupTemplateByQuery
 * @request POST:/api/v1/publish/insight/template/findGroupTemplateByQuery
 */
export async function findGroupTemplateByQuery(
  data: InsightGroupTemplateListQry,
  options?: MethodOptions,
): Promise<InsightGroupTemplateDTORes[]> {
  return request(`${baseUrl}/api/v1/publish/insight/template/findGroupTemplateByQuery`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface InitGroupAndItemParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags InsightTemplate
 * @name InitGroupAndItem
 * @summary initGroupAndItem
 * @request GET:/api/v1/publish/insight/template/initGroupAndItem
 */
export async function initGroupAndItem(query: InitGroupAndItemParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/publish/insight/template/initGroupAndItem`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface RemoveInsightGroupTemplateParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags InsightTemplate
 * @name RemoveInsightGroupTemplate
 * @summary removeInsightGroupTemplate
 * @request DELETE:/api/v1/publish/insight/template/removeInsightGroupTemplate
 */
export async function removeInsightGroupTemplate(
  query: RemoveInsightGroupTemplateParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/publish/insight/template/removeInsightGroupTemplate`, {
    method: 'DELETE',
    params: query,
    ...options,
  });
}

export interface RemoveInsightItemTemplateParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags InsightTemplate
 * @name RemoveInsightItemTemplate
 * @summary removeInsightItemTemplate
 * @request DELETE:/api/v1/publish/insight/template/removeInsightItemTemplate
 */
export async function removeInsightItemTemplate(
  query: RemoveInsightItemTemplateParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/publish/insight/template/removeInsightItemTemplate`, {
    method: 'DELETE',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags InsightTemplate
 * @name UpdateGroupTemplate
 * @summary updateGroupTemplate
 * @request POST:/api/v1/publish/insight/template/updateGroupTemplate
 */
export async function updateGroupTemplate(data: InsightGroupTemplateDTOReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/publish/insight/template/updateGroupTemplate`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags InsightTemplate
 * @name UpdateItemTemplate
 * @summary updateItemTemplate
 * @request POST:/api/v1/publish/insight/template/updateItemTemplate
 */
export async function updateItemTemplate(data: InsightItemTemplateDTOReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/publish/insight/template/updateItemTemplate`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
