/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  SlimmingResultAcceptDTO,
  SlimmingResultCompleteDTO,
  SlimmingResultRejectDTO,
  SlimmingResultUndoAcceptDTO,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * No description
 * @tags CopilotFeedback
 * @name AcceptSlimmingResult
 * @summary 采纳优化结果
 * @request POST:/api/v1/copilot/feedback/acceptSlimmingResult
 */
export async function acceptSlimmingResult(data: SlimmingResultAcceptDTO, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/copilot/feedback/acceptSlimmingResult`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags CopilotFeedback
 * @name CompleteSlimmingResult
 * @summary 完成优化结果
 * @request POST:/api/v1/copilot/feedback/completeSlimmingResult
 */
export async function completeSlimmingResult(
  data: SlimmingResultCompleteDTO,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/copilot/feedback/completeSlimmingResult`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags CopilotFeedback
 * @name RejectSlimmingResult
 * @summary 拒绝优化结果
 * @request POST:/api/v1/copilot/feedback/rejectSlimmingResult
 */
export async function rejectSlimmingResult(data: SlimmingResultRejectDTO, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/copilot/feedback/rejectSlimmingResult`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags CopilotFeedback
 * @name UndoAcceptSlimmingResult
 * @summary 移除采纳优化结果
 * @request POST:/api/v1/copilot/feedback/undoAcceptSlimmingResult
 */
export async function undoAcceptSlimmingResult(
  data: SlimmingResultUndoAcceptDTO,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/copilot/feedback/undoAcceptSlimmingResult`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
