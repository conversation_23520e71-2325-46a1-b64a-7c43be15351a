/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import {
  CasualBuildPipelineVO,
  PipelineExecuteParams,
  PipelineExecuteRecordVO,
  PipelineInitRequest,
  UserModuleRelation,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface CopyPipelineParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
  /** pipelineName */
  pipelineName: string;
}
/**
 * No description
 * @tags CasualBuild
 * @name CopyPipeline
 * @summary 复制流水线
 * @request POST:/api/v1/casual/build/copyPipeline
 */
export async function copyPipeline(query: CopyPipelineParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/casual/build/copyPipeline`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface CopyPipelineFromTemplateParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
  /** pipelineName */
  pipelineName: string;
}
/**
 * No description
 * @tags CasualBuild
 * @name CopyPipelineFromTemplate
 * @summary 根据最新的流水线模版克隆流水线
 * @request POST:/api/v1/casual/build/copyPipelineFromTemplate
 */
export async function copyPipelineFromTemplate(
  query: CopyPipelineFromTemplateParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/casual/build/copyPipelineFromTemplate`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface GetExecuteParamsParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags CasualBuild
 * @name GetExecuteParams
 * @summary 获取执行流水线的参数
 * @request GET:/api/v1/casual/build/executeParams
 */
export async function getExecuteParams(
  query: GetExecuteParamsParams,
  options?: MethodOptions,
): Promise<PipelineExecuteParams[]> {
  return request(`${baseUrl}/api/v1/casual/build/executeParams`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPipelineExecuteRecordParams {
  /**
   * pipelineInstanceId
   * @format int64
   */
  pipelineInstanceId?: number;
}
/**
 * No description
 * @tags CasualBuild
 * @name GetPipelineExecuteRecord
 * @summary 根据pipelineInstanceId获取执行记录详情
 * @request GET:/api/v1/casual/build/executeRecord
 */
export async function getPipelineExecuteRecord(
  query?: GetPipelineExecuteRecordParams,
  options?: MethodOptions,
): Promise<PipelineExecuteRecordVO> {
  return request(`${baseUrl}/api/v1/casual/build/executeRecord`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface ExecutePipelineWithParamsParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags CasualBuild
 * @name ExecutePipelineWithParams
 * @summary 执行流水线
 * @request POST:/api/v1/casual/build/executeWithParams
 */
export async function executePipelineWithParams(
  query: ExecutePipelineWithParamsParams,
  data: PipelineExecuteParams[],
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/casual/build/executeWithParams`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface GetFirstPipelineExecuteRecordParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId?: number;
}
/**
 * No description
 * @tags CasualBuild
 * @name GetFirstPipelineExecuteRecord
 * @summary 根据pipelineId获取初始的执行记录
 * @request GET:/api/v1/casual/build/firstExecuteRecord
 */
export async function getFirstPipelineExecuteRecord(
  query?: GetFirstPipelineExecuteRecordParams,
  options?: MethodOptions,
): Promise<PipelineExecuteRecordVO> {
  return request(`${baseUrl}/api/v1/casual/build/firstExecuteRecord`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetCasualBuildInitParamsParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
  /**
   * moduleId
   * @format int64
   */
  moduleId?: number;
  /**
   * versionPlanId
   * @format int64
   */
  versionPlanId?: number;
  /** dependencyType */
  dependencyType?: 'INTEGRATE_AREA' | 'RELEASE_PRODUCT';
  /**
   * integrateAreaId
   * @format int64
   */
  integrateAreaId?: number;
  /** appVersion */
  appVersion?: string;
}
/**
 * No description
 * @tags CasualBuild
 * @name GetCasualBuildInitParams
 * @summary 获取某个应用的流水线初始化参数
 * @request GET:/api/v1/casual/build/initParams
 */
export async function getCasualBuildInitParams(
  query: GetCasualBuildInitParamsParams,
  options?: MethodOptions,
): Promise<PipelineExecuteParams> {
  return request(`${baseUrl}/api/v1/casual/build/initParams`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags CasualBuild
 * @name InitPipeline
 * @summary 初始化流水线
 * @request POST:/api/v1/casual/build/initPipeline
 */
export async function initPipeline(data: PipelineInitRequest, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/casual/build/initPipeline`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface GetUserPipelineDependentModulesParams {
  /**
   * appId
   * @format int64
   */
  appId?: number;
}
/**
 * No description
 * @tags CasualBuild
 * @name GetUserPipelineDependentModules
 * @summary 获取用户流水线依赖过的模块列表
 * @request GET:/api/v1/casual/build/module/deps
 */
export async function getUserPipelineDependentModules(
  query?: GetUserPipelineDependentModulesParams,
  options?: MethodOptions,
): Promise<UserModuleRelation[]> {
  return request(`${baseUrl}/api/v1/casual/build/module/deps`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface DeletePipelineParams {
  /**
   * pipelineId
   * @format int64
   */
  pipelineId: number;
}
/**
 * No description
 * @tags CasualBuild
 * @name DeletePipeline
 * @summary 删除流水线
 * @request DELETE:/api/v1/casual/build/pipeline
 */
export async function deletePipeline(query: DeletePipelineParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/casual/build/pipeline`, {
    method: 'DELETE',
    params: query,
    ...options,
  });
}

export interface GetPipelinesParams {
  /**
   * appId
   * @format int64
   */
  appId?: number;
  /** name */
  name?: string;
  /**
   * spaceId
   * @format int64
   */
  spaceId?: number;
  /** onlyShowMine */
  onlyShowMine?: boolean;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
}
/**
 * No description
 * @tags CasualBuild
 * @name GetPipelines
 * @summary 获取相关流水线
 * @request GET:/api/v1/casual/build/pipelines
 */
export async function getPipelines(
  query?: GetPipelinesParams,
  options?: MethodOptions,
): Promise<PaginationResult<CasualBuildPipelineVO>> {
  return request(`${baseUrl}/api/v1/casual/build/pipelines`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
