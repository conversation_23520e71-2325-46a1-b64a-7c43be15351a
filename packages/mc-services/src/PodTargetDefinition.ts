/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PodTargetDefinition } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface InitPodTargetDefinitionsParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
  /** repo */
  repo: string;
  /** doc */
  doc: string;
}
/**
 * No description
 * @tags PodTargetDefinition
 * @name InitPodTargetDefinitions
 * @summary initPodTargetDefinitions
 * @request POST:/api/v1/podTargetDefinition/init
 */
export async function initPodTargetDefinitions(
  query: InitPodTargetDefinitionsParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/podTargetDefinition/init`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags PodTargetDefinition
 * @name SavePodTargetDefinition
 * @summary savePodTargetDefinition
 * @request POST:/api/v1/podTargetDefinition/save
 */
export async function savePodTargetDefinition(data: PodTargetDefinition, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/podTargetDefinition/save`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags PodTargetDefinition
 * @name UpdatePodTargetDefinition
 * @summary updatePodTargetDefinition
 * @request PUT:/api/v1/podTargetDefinition/update
 */
export async function updatePodTargetDefinition(data: PodTargetDefinition, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/podTargetDefinition/update`, {
    method: 'PUT',
    body: data as any,
    ...options,
  });
}
