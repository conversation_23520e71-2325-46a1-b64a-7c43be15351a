import { useEffect } from 'react';
import { useNavigate } from 'ice';
import { useIterationContext } from '@/components/Iteration/IterationProvider';
import { camelCase } from 'lodash-es';
// 新项目支持的迭代类型
const SupportedIterationTypes = ['bizDynamicPublish', 'bizDynamicDevelopment', 'sdkAgileCiIntegration', 'sdkVerifyIntegration'];
export default function IterationDetail() {
  const navigate = useNavigate();
  const { data: iterationVO } = useIterationContext();
  useEffect(() => {
    if (iterationVO) {
      const pageType = camelCase(iterationVO?.workflow?.workflowScope?.identifier?.toLowerCase());
      if (SupportedIterationTypes.includes(pageType)) {
        navigate(`/iterations/alterSheet/detail?entityId=${iterationVO?.mainEntity?.id}`);
      } else {
        navigate(`/iteration/${pageType}/detail?iterationId=${iterationVO?.workflow?.id}`);
      }
    }
  }, [iterationVO, navigate]);
}
