import { DynamicReleaseBO } from '@ali/mc-services';

export interface AlterSheetInfoBO{
  alterSheetId: number;
  alterSheetName: string;
  alterSheetDescription: string;
  applicationId: number;
  targetReleaseId: number;
  targetReleaseVersion: string;
}
export interface CreateDynamicPublishModalProps{
  alterSheetInfo: AlterSheetInfoBO;
  refresh: () => Promise<DynamicReleaseBO[]>;
}

export interface ModalContentProps extends CreateDynamicPublishModalProps{
  open: boolean;
  onCancel: () => void;
}

