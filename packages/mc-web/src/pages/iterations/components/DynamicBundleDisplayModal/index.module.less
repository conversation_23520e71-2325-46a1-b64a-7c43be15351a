.dynamicBundleWrap{
  width: 100%;
  // padding-inline: var(--mc-padding-sm);
  gap:var(--mc-margin);
  div{
    box-sizing: border-box;
  }

  .labelStyle{
      white-space: no-wrap;
      &::after {
          content: ':';
          margin-right: var(--mc-padding-xs);
      }
  }

  .left{
      min-height: 276px;
      width: 232px;
      // border-radius: var(--mc-border-radius);
      // padding: var(--mc-padding);
      // background-color: var(--mc-color-fill-content);
  }
  .right{
      min-height: 276px;
      width: 557px;
      padding-inline: var(--mc-padding-lg);
      border-left:1px solid var(--mc-color-split);
      // border-radius: var(--mc-border-radius);
      // padding: var(--mc-padding);
      // background-color: var(--mc-color-fill-content);
  }
  .title{
    font-size: var(--mc-font-size);
    color:var(--mc-color-text);
    font-weight: var(--mc-font-weight-strong);
    margin-bottom: var(--mc-margin);
  }
  .desc{
    font-size: var(--mc-font-size-sm);
    color:var(--mc-color-text-secondary);
    width: 100%;
    word-break: break-all;
   }
   .qrCodeWrap{
      width: 162px;
      height: 162px;
      background-color: var(--mc-color-fill);
      padding: var(--mc-padding-xxs);
      border:1px solid var(--mc-color-border);
   }
    .bundleInfoWrap{
      color: var(--mc-color-text-secondary);
      font-size: var(--mc-font-size-sm);
      padding-top: var(--mc-padding-sm);
    }

}