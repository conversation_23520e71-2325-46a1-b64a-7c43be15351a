import React, { useState, useCallback } from 'react';
import { Modal, Button } from 'antd';
import IterationCodeReviewList from './IterationCodeReviewList';
import ModuleCodeReviewList from './ModuleCodeReviewList';
import PipelineCodeReviewList from './PipelineCodeReviewList';

type CodeReviewModalProps = {
  open: boolean;
  type?: 'iteration' | 'module' | 'pipeline';
  alterSheetId?: number;
  moduleId?: number;
  pipelineInstanceId?: number;
  onClose?: () => void;
};

export default function CodeReviewModal(props: CodeReviewModalProps) {
  const { type = 'iteration', open, alterSheetId, moduleId, pipelineInstanceId, onClose } = props;
  const [title, setTitle] = useState('代码评审合并');

  const onTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
  }, []);

  return (
    <Modal
      width={900}
      title={title}
      open={open}
      onOk={onClose}
      onCancel={onClose}
      footer={[
        <Button key="submit" type="primary" onClick={onClose}>
          确定
        </Button>,
      ]}
    >
      {type === 'iteration' && <IterationCodeReviewList alterSheetId={alterSheetId} onTitleChange={onTitleChange} />}
      {type === 'module' && (
        <ModuleCodeReviewList alterSheetId={alterSheetId} moduleId={moduleId} onTitleChange={onTitleChange} />
      )}
      {type === 'pipeline' && (
        <PipelineCodeReviewList pipelineInstanceId={pipelineInstanceId} onTitleChange={onTitleChange} />
      )}
    </Modal>
  );
}
