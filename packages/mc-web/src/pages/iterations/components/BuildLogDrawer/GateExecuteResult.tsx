import React, { ReactNode } from 'react';
import styles from './index.module.less';
import { Flex } from 'antd';
import { useRequest } from '@ali/mc-request';
import { fetchOuterGateResult, FetchOuterGateResultParams } from '@ali/mc-services/GateApi';
import CreateGateApprovalModal from './CreateGateApprovalModal';
import ManualSkipModal from './ManualSkipModal';
import { PipelineInstanceExecuteSummary } from '@ali/mc-services';
import { InlineButton } from '@ali/mc-uikit';
type GATE_APPROVAL_STATUS_TYPE = 'PENDING' | 'APPROVED' | 'DENIED' | 'CANCELLED';
const GATE_APPROVAL_STATUS_MAP: { [P in GATE_APPROVAL_STATUS_TYPE]: string } = {
  PENDING: '审批中',
  APPROVED: '通过',
  DENIED: '拒绝',
  CANCELLED: '取消',
};

const GateExecuteResult = ({ gateCheckSummary, refreshPipelineInstanceExecuteSummary }:
  {
    gateCheckSummary: any;
    refreshPipelineInstanceExecuteSummary: () => Promise<PipelineInstanceExecuteSummary>;
  }) => {
  const {
    id,
    supportManualSkip,
    supportApprovalSkip,
    gateCheckResult,
    approvalStatus,
    gateCheckDetailLink,
    gateCheckDetailFileUrl,
    approvalUrl,
    supportOuterResultCallback,
    gateCheckStatus,
    manualSkipUrl,
    gateId,
  } = gateCheckSummary || {};

  const isAiCrPluginName = gateId === 'com.alibaba.mtl4.plugin.incr-code-scan-plugin';
  const { runAsync: reloadGateCheck } = useRequest<boolean, [FetchOuterGateResultParams]>(fetchOuterGateResult);

  const reloadGateCheckResult = async (gateCheckId: number) => {
    const res = await reloadGateCheck({ gateCheckId });
    if (res) {
      await refreshPipelineInstanceExecuteSummary?.();
    }
  };
  const results: ReactNode[] = [];
  if (gateCheckDetailLink) {
    results.push(
      <InlineButton
        type="text"
        size="small"
        href={isAiCrPluginName ? `${gateCheckDetailLink}&gateCheckId=${id}` : gateCheckDetailLink}
        target="_blank"
        style={{ fontSize: 'inherit', lineHeight: 'inherit', paddingBlock: 0, height: 'auto', border: 'none' }}
      >
        {isAiCrPluginName ? '卡口处理' : '执行详情'}
      </InlineButton>,
    );
  }
  if (gateCheckDetailFileUrl) {
    results.push(
      <InlineButton
        type="text"
        size="small"
        href={gateCheckDetailFileUrl}
        target="_blank"
        style={{ fontSize: 'inherit', lineHeight: 'inherit', paddingBlock: 0, height: 'auto', border: 'none' }}
      >
        结果详情
      </InlineButton>,
    );
  }
  if (!gateCheckResult || gateCheckResult === 'NOT_PASS') {
    if (supportApprovalSkip) {
      results.push(
        <CreateGateApprovalModal gateCheckId={id} refresh={refreshPipelineInstanceExecuteSummary} />,
      );
      if (approvalUrl) {
        results.push(
          <InlineButton
            type="text"
            size="small"
            href={approvalUrl}
            target="_blank"
            style={{ fontSize: 'inherit', lineHeight: 'inherit', paddingBlock: 0, height: 'auto', border: 'none' }}
          >
            {`审批详情(${GATE_APPROVAL_STATUS_MAP[approvalStatus as GATE_APPROVAL_STATUS_TYPE] ?? approvalStatus})`}
          </InlineButton>,
        );
      }
    }
    if (supportManualSkip && gateCheckStatus !== 'RUNNING') {
      if (manualSkipUrl) {
        results.push(
          <InlineButton type="text" size="small" target="_blank" href={isAiCrPluginName ? `${gateCheckDetailLink}&gateCheckId=${id}` : manualSkipUrl}>
            手动通过
          </InlineButton>,
        );
      } else {
        results.push(
          <ManualSkipModal gateCheckId={id} refresh={refreshPipelineInstanceExecuteSummary} />,
        );
      }
    }
    if (supportOuterResultCallback) {
      results.push(
        <Flex
          className={styles.entryTextStyle}
          onClick={() => reloadGateCheckResult(id)}
        >
          重新检测
        </Flex>,
      );
    }
  }
  return (<>
    {results}
  </>);
};

export default React.memo(GateExecuteResult);
