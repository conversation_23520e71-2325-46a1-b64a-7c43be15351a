import { Button, Empty, Flex, Popconfirm, Popover, Select, theme } from 'antd';
import React, { useEffect, useState } from 'react';
import { queryTestReport, genTestReportEmailVelocity, GenTestReportEmailVelocityParams } from '@ali/mc-services/TestReport';
import { TestReport, TestReportQuery } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import EmailInfoModal from './EmailInfoModal';
import { User } from '@ali/mc-uikit';
import { isEmpty } from 'lodash-es';
import styles from './TestReportPreview.module.less';
import TestReportCard from './components/TestReportCard';

interface TestReportPreviewProps {
  report?: TestReport;
  onEdit?: () => void;
  onImport?: (report: TestReport) => void;
  loading?: boolean;
  scope?: string;
  testType?: boolean;
  testReportQuery?: TestReport;
  sendShow?: boolean;
  onDelete?: (id: number | undefined) => void;
}

const TestReportPreview = (props: TestReportPreviewProps) => {
  const { token } = theme.useToken();
  const { testReportQuery, report, onEdit, sendShow, testType, onImport, onDelete } = props;
  const [selectedReport, setSelectedReport] = useState<TestReport>();
  const [open, setOpen] = useState(false);

  const { runAsync: requestTestReport, data: reportList = [] } = useRequest<
    TestReport[], [TestReportQuery]
  >(queryTestReport);

  const { runAsync: requestGenTestReportEmailVelocity, data: content } = useRequest<
    string, [GenTestReportEmailVelocityParams]
  >(genTestReportEmailVelocity);

  const fetchReportList = () => {
    if (testReportQuery) {
      requestTestReport(testReportQuery);
    }
  };

  const onChangeSelectedReport = (selectedReportId: number) => {
    const selectedReportList = reportList.filter((_report) => _report.id === selectedReportId).pop();
    if (selectedReport?.category === 'TEMPLATE') {
      selectedReport.category = 'NORMAL';
    }
    setSelectedReport(selectedReportList);
  };

  const onSendVisible = () => {
    if (report?.id) {
      requestGenTestReportEmailVelocity({ id: report.id }).then((res) => {
        if (res) {
          setOpen(true);
        }
      });
    }
  };

  useEffect(() => {
    fetchReportList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [testReportQuery]);

  const popContent = (
    <Flex vertical>
      <Flex style={{ marginTop: token.marginMD }}>
        <Select
          allowClear
          onChange={onChangeSelectedReport}
          style={{ width: '400px' }}
        >
          {
            reportList?.map((_report) => {
              return (
                <Select.Option key={_report.id} value={_report.id}>
                  <Flex>
                    <span>{_report.name}</span>
                    <span style={{ marginLeft: token.marginXS }}>创建人:</span>
                    <User empIds={_report.creator} />
                  </Flex>
                </Select.Option>
              );
            })
          }
        </Select>
      </Flex>
      <Flex justify="right" style={{ marginTop: token.marginXS }}>
        <Button
          onClick={() => {
            onImport && onImport({
              ...selectedReport,
              id: report?.id,
            });
          }}
          type="primary"
          disabled={isEmpty(selectedReport)}
        >
          确定
        </Button>
      </Flex>
    </Flex>
  );

  return (
    <Flex vertical flex={1}>
      {testType && (
        <Flex justify="right" gap={token.marginXS}>
          <Button type="primary" onClick={onEdit}>编辑</Button>
          <Popover content={popContent} trigger="click" placement="topRight">
            <Button type="primary">导入</Button>
          </Popover>
          {
            sendShow && <Button type="primary" onClick={onSendVisible}>发送</Button>
          }
          {
            testType && report?.id &&
            <Popconfirm
              title="确定要删除该测试报告?"
              onConfirm={() => onDelete && onDelete(report?.id)}
            >
              <Button>删除</Button>
            </Popconfirm>
          }
        </Flex>
      )}
      {(report && report?.name) ? (
        <Flex vertical className={styles.report}>
          {
            <Flex vertical className={styles.reportHeader}>
              <img
                className={styles.reportHeaderLogo}
                src="https://img.alicdn.com/imgextra/i2/O1CN01zKDXD01PRqjHsNfqG_!!6000000001838-2-tps-120-36.png"
              />
              {
                report?.name && <Flex className={styles.reportHeaderName}>{report?.name}</Flex>
              }
              {
                report?.desc && <Flex className={styles.reportHeaderDesc}>{report?.desc}</Flex>
              }
            </Flex>
          }
          <Flex vertical className={styles.reportContent}>
            <Flex vertical className={styles.content}>
              {
                <>
                  <Flex>
                    {
                      report?.mainReportItem && <TestReportCard report={report?.mainReportItem} />
                    }
                  </Flex>
                  <Flex vertical>
                    {
                      report?.subReportItems?.map((item, index) => (
                        <Flex key={index}>
                          <TestReportCard report={item} />
                        </Flex>
                      ))
                    }
                  </Flex>
                </>
              }
            </Flex>
          </Flex>
        </Flex>
      ) : <Empty description={testType ? '暂无测试报告，请点击编辑或导入' : '暂无测试报告'} />}
      <EmailInfoModal
        open={open}
        defaultValue={{
          title: report?.name,
          content,
          srcUrl: `https://mtl4.alibaba-inc.com/dev/api/v1/report/test/genTestReportEmailIframe.json?id=${report?.id}`,
        }}
        onCancel={() => { }}
      />
    </Flex>
  );
};

export default TestReportPreview;
