.card {
  flex: 1;
  .itemName {
    padding-top: var(--mc-padding-lg);
    color: var(--mc-color-info);
    font-weight: 600;
    font-size: var(--mc-font-size-heading-1);
    letter-spacing: 0.38px;
    text-align: center;
  }

  .itemDesc {
    margin-top: var(--mc-margin-sm);
    font-size: var(--mc-font-size);
  }

  .contentStyle {
    margin-top: var(--mc-margin-lg);
    padding: var(--mc-padding) var(--mc-padding) var(--mc-padding-lg);
    zoom: 1;
    background-color: var(--mc-color-bg-base);

    .subHeader {
      font-size: var(--mc-font-size-heading-3);
      font-weight: 600;
      color: var(--mc-color-info);
      letter-spacing: 0.18px;
    }

    .mt {
      margin-top: var(--mc-margin-sm);
    }

    .summaryStyle {
      margin-top: var(--mc-margin-lg);

      .summaryItem {
        font-size: var(--mc-font-size);
        margin-top: var(--mc-margin-sm);
      }
    }

    .tableStyle {
      margin-top: var(--mc-margin-lg);
      font-size: var(--mc-font-size) !important;
    }
  }


  :global {
    .ant-descriptions .ant-descriptions-title {
      font-size: var(--mc-font-size-sm);
    }
  }
}

.conclusionStyle {
  font-size: var(--mc-font-size-lg);
  font-weight: bold;
}

.rowStyle {
  background-color: #B9E9FF;
}
