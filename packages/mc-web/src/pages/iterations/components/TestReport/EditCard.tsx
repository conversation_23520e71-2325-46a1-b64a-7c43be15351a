import { CodeTrashOutlined } from '@ali/mc-icons';
import { Flex, Input, Popconfirm, Tag, theme } from 'antd';
import React from 'react';
import EditTable from './EditTable';
import { TestReportItem, TestReportSummaryItem, TestReportTableContent, TestReportTableContentHeader } from '@ali/mc-services';

interface EditCardProps {
  onChange?: (reportItem: TestReportItem) => void;
  reportItem?: TestReportItem;
}

const EditCard = (props: EditCardProps) => {
  const { token } = theme.useToken();
  const { onChange, reportItem } = props;

  const changeReportItem = (item: TestReportItem) => {
    onChange && onChange(item);
  };

  // 内容修改
  const handleReportChange = (type: string, event: React.ChangeEvent<HTMLInputElement>) => {
    changeReportItem({
      ...reportItem,
      [type]: event.target.value,
    });
  };

  // 摘要信息新增
  const handleSummaryAdd = () => {
    const item = { ...reportItem };
    const summary = { ...item.summary };

    if (summary?.data) {
      summary.data?.push({
        itemName: undefined,
        conclusion: undefined,
      });
    } else {
      summary.data = [
        {
          itemName: undefined,
          conclusion: undefined,
        },
      ];
    }
    changeReportItem({ ...item, summary });
  };

  // 摘要信息删除
  const handleSummaryDelete = (deletedIndex: number) => {
    const item = { ...reportItem };
    const { summary = {} } = item;
    const { data = [] } = summary;

    const newData = data.filter((value: TestReportSummaryItem, index: number) => index !== deletedIndex);
    summary.data = newData;

    changeReportItem(item);
  };

  // 摘要信息修改
  const handleSummary = (type: 'conclusion' | 'itemName', index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const item = { ...reportItem };
    const { summary = {} } = item;
    const { data = [] } = summary;

    data[index][type] = event.target.value;

    changeReportItem(item);
  };

  // 新增表格
  const handleTableAdd = () => {
    const item = { ...reportItem };
    const { tableContents = [] } = item;

    tableContents.push({
      tableName: undefined,
      headers: [],
      data: [],
    });

    changeReportItem(item);
  };

  // 表格名称修改
  const handleTableName = (index: number, tableName: string) => {
    const item = { ...reportItem };
    const { tableContents = [] } = item;

    tableContents[index].tableName = tableName;

    changeReportItem(item);
  };


  // 表格新增删除一行后的data & 修改后的data
  const handleTableData = (index: number, data: Record<string, string>[]) => {
    const item = { ...reportItem };
    const { tableContents = [] } = item;

    tableContents[index].data = data;

    changeReportItem(item);
  };

  // 表格新增删除一列headers
  const handleTableHeader = (index: number, headers: TestReportTableContentHeader[]) => {
    const item = { ...reportItem };
    const { tableContents = [] } = item;
    if (tableContents[index]) {
      tableContents[index].headers = headers;
    }

    changeReportItem(item);
  };

  const deleteTable = (index: number) => {
    const item = { ...reportItem };
    const { tableContents = [] } = item;

    item.tableContents = [
      ...tableContents?.filter((_item: TestReportTableContent, _index: number) => _index !== index),
    ];

    changeReportItem(item);
  };

  return (
    <Flex vertical>
      <Flex gap={token.margin} vertical>
        <Flex vertical>
          <span>测试报告项名称：</span>
          <Input
            value={reportItem?.itemName}
            onChange={(event) => {
              handleReportChange('itemName', event);
            }}
          />
        </Flex>
        <Flex vertical>
          <span>测试报告项描述：</span>
          <Input
            value={reportItem?.itemDesc}
            onChange={(event) => {
              handleReportChange('itemDesc', event);
            }}
          />
        </Flex>
        <Flex vertical>
          <span>测试报告项结论：</span>
          <Input
            value={reportItem?.conclusion}
            onChange={(event) => {
              handleReportChange('conclusion', event);
            }}
          />
        </Flex>
        <Flex vertical>
          <span>测试报告项链接：</span>
          <Input
            value={reportItem?.detailUrl}
            onChange={(event) => {
              handleReportChange('detailUrl', event);
            }}
          />
        </Flex>
      </Flex>
      <Flex vertical>
        <Flex align="center">
          <h4>摘要简述</h4>
          <Flex>
            <Tag
              color="blue"
              style={{
                cursor: 'pointer',
                borderRadius: token.borderRadiusSM,
                marginLeft: token.marginSM,
              }}
              onClick={handleSummaryAdd}
            >+新增摘要项
            </Tag>
          </Flex>
        </Flex>
        <Flex vertical gap={token.margin}>
          {reportItem?.summary?.data?.map((item: TestReportSummaryItem, index: number) => {
            return (
              <Flex
                align="center"
                gap={token.marginXS}
                key={index}
                style={{ marginRight: token.marginSM }}
              >
                <Flex style={{ whiteSpace: 'nowrap' }}>摘要名称：</Flex>
                <Input
                  value={item.itemName}
                  onChange={(event) => {
                    handleSummary('itemName', index, event);
                  }}
                />
                <Flex style={{ whiteSpace: 'nowrap' }}>结论：</Flex>
                <Input
                  value={item.conclusion}
                  onChange={(event) => {
                    handleSummary('conclusion', index, event);
                  }}
                />
                <Popconfirm
                  title="确定要删除该摘要项?"
                  onConfirm={() => {
                    handleSummaryDelete(index);
                  }}
                >
                  <CodeTrashOutlined style={{ marginLeft: token.marginSM }} />
                </Popconfirm>
              </Flex>
            );
          })}
        </Flex>
        <Flex align="center">
          <h4>明细表格</h4>
          <Flex>
            <Tag
              color="blue"
              style={{
                cursor: 'pointer',
                borderRadius: token.borderRadiusSM,
                marginLeft: token.marginSM,
              }}
              onClick={handleTableAdd}
            >+新增表格
            </Tag>
          </Flex>
        </Flex>
        <Flex vertical>
          {reportItem?.tableContents?.map((table: TestReportTableContent, index: number) => {
            return (
              <EditTable
                key={`t-${index}`}
                dataSource={table.data}
                columns={table.headers}
                tableName={table.tableName}
                handleTableName={handleTableName.bind(this, index)}
                handleTableData={handleTableData.bind(this, index)}
                handleTableHeader={handleTableHeader.bind(this, index)}
                deleteTable={deleteTable.bind(this, index)}
              />
            );
          })}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default EditCard;
