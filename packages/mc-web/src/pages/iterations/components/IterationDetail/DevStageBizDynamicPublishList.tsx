import React, { useEffect } from 'react';
import { Flex, List, theme } from 'antd';
import { useIterationContext } from '@/components/Iteration/IterationProvider';
import { DataEntryList, StatusTag, User } from '@ali/mc-uikit';
import { useRequest } from '@ali/mc-request';
import { getDynamicReleaseListByAlterSheetId, GetDynamicReleaseListByAlterSheetIdParams } from '@ali/mc-services/DynamicReleaseSheet';
import { DynamicReleaseBO } from '@ali/mc-services';
import { PUBLISH_STATUS_MAP, DYNAMIC_RELEASE_TYPE } from '@/constants/dynamicPublish';
import dayjs from 'dayjs';
import CreateDynamicPublishModal from '../CreateDynamicPublishModal';
import AutoPopOver from '../AutoPopOver';
import { getMcDynamicReleaseDetailUrl, GetMcDynamicReleaseDetailUrlParams } from '@ali/mc-services/Common';
import styles from './iterationDetail.module.less';

export default function DevStageBizDynamicPublishList() {
  const { token } = theme.useToken();
  const { data: iterationVO = {} } = useIterationContext();
  const { mainEntity } = iterationVO;
  const {
    id: alterSheetId,
    name: alterSheetName,
    targetReleaseId,
    targetReleaseVersion,
    applicationId,
    description: alterSheetDescription,
   } = mainEntity || {} as any;

  const {
    runAsync: requestDynamicReleaseListByAlterSheetId,
    data: releaseList,
    loading,
    refreshAsync: refreshDynamicReleaseList,
  } = useRequest<
    DynamicReleaseBO[], [GetDynamicReleaseListByAlterSheetIdParams]
  >(getDynamicReleaseListByAlterSheetId);

  const {
    runAsync: getDynamicPublishPageUrl,
  } = useRequest<string, [GetMcDynamicReleaseDetailUrlParams]>(getMcDynamicReleaseDetailUrl);

  useEffect(() => {
    requestDynamicReleaseListByAlterSheetId({
      alterSheetId,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [alterSheetId]);

  const handleClick = async ({ appId, releaseId }: GetMcDynamicReleaseDetailUrlParams) => {
    if (!appId || !releaseId) return;
    const res = await getDynamicPublishPageUrl({ appId, releaseId });
    if (res) {
      window.open(res?.substring(res?.indexOf('.com') + '.com'.length), '_blank', 'noopener');
    }
  };

  return (
    <Flex
      vertical
      style={{ width: '100%' }}
    >
      <Flex justify="flex-end" className={styles.actionArea}>
        <CreateDynamicPublishModal
          refresh={refreshDynamicReleaseList}
          alterSheetInfo={{
            alterSheetId,
            alterSheetName,
            targetReleaseVersion,
            targetReleaseId,
            applicationId,
            alterSheetDescription,
          }}
        />
      </Flex>
      <DataEntryList
        loading={loading}
        title={
          <Flex
            style={{
              fontSize: token.fontSize,
              fontWeight: token.fontWeightStrong,
            }}
          >
            动态发布
          </Flex>
        }
        dataSource={releaseList}
        renderItem={(item: DynamicReleaseBO) => {
          const {
            id,
            name,
            creator,
            gmtCreate,
            gmtModified,
            releaseType,
            featureUpdateVersion,
            status,
            automated,
          } = item;
          let statusObj;
          if (status) {
            statusObj = PUBLISH_STATUS_MAP[status];
          }
          return (
            <List.Item>
              <Flex vertical>
                <Flex
                  gap={token.marginXS}
                  align="center"
                  style={{
                    fontSize: token.fontSizeLG,
                    lineHeight: token.lineHeightLG,
                    marginBottom: token.marginXS,
                    fontWeight: token.fontWeightStrong,
                  }}
                >
                  <span
                    className={styles.titleLinkStyle}
                    onClick={() => {
                      if (id) {
                        handleClick({
                          appId: applicationId,
                          releaseId: id,
                        });
                      }
                    }}
                  >{`【${id}】${name}`}</span>
                  <AutoPopOver isShow={automated} useType="table" releaseType={releaseType} direction="topRight" />
                  {statusObj && <StatusTag color={statusObj.color}>{statusObj.name}</StatusTag>}
                </Flex>
                <Flex
                  gap={token.margin}
                  align="center"
                  style={{
                    fontSize: token.fontSizeSM,
                    lineHeight: token.lineHeightSM,
                    marginBottom: token.marginXXS,
                    color: token.colorTextTertiary,
                  }}
                >
                  <Flex>
                    <span>创建人：</span>
                    <User empIds={creator} size={18} showAvatar />
                  </Flex>
                  <Flex>
                    <span>创建时间：</span>
                    <span>{dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </Flex>
                  <Flex>
                    <span>最后修改时间：</span>
                    <span>{dayjs(gmtModified).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </Flex>
                </Flex>
                <Flex
                  gap={token.margin}
                  align="center"
                  style={{
                    fontSize: token.fontSizeSM,
                    lineHeight: token.lineHeightSM,
                    color: token.colorTextTertiary,
                  }}
                >
                  <Flex>
                    <span>类型：</span>
                    <span>{DYNAMIC_RELEASE_TYPE[releaseType]?.name}</span>
                  </Flex>
                  <Flex>
                    <span>featureUpdateVersion：</span>
                    <span>{featureUpdateVersion}</span>
                  </Flex>
                </Flex>
              </Flex>
            </List.Item>
          );
        }}
      />
    </Flex>
  );
}

