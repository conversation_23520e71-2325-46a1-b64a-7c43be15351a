import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Flex, theme, Skeleton, Tooltip } from 'antd';
import { useRequest } from '@ali/mc-request';
import { SubmitTestVO, PaginationResult } from '@ali/mc-services';
import { findSubmitTestList, FindSubmitTestListParams } from '@ali/mc-services/TestSheet';
import { useIterationContext } from '@/components/Iteration/IterationProvider';
import NeedTestComponent from '@/pages/iterations/components/DevTest/NeedTestComponent';
import DevTestInfoList from '@/pages/iterations/components/DevTest/DevTestInfoList';
import styles from './iterationDetail.module.less';
import { InlineButton } from '@ali/mc-uikit';
import SubmitTestDrawer from '../SubmitTestDrawer';
import EmptyCard from '../EmptyCard';
import { ArrowSwitchOutlined } from '@ali/mc-icons';
import SubmitTestItemSelector from '../DevTest/SubmitTestItemSelector';

const DevStageTest = () => {
  const { token } = theme.useToken();
  const { data: iterationVO = {} } = useIterationContext();
  const { mainEntity } = iterationVO;
  const { id: alterSheetId } = mainEntity || ({} as any);
  const { scope } = iterationVO?.workflow || ({} as any);
  const spaceId = iterationVO?.space?.id;
  const [submitTestList, setSubmitTestItemList] = useState<SubmitTestVO[]>();
  const [selectSubmitTestItemId, setSelectSubmitTestItemId] = useState<number>();
  const [submitTestVisible, setSubmitTestVisible] = useState<boolean>(false);

  const {
    runAsync: getSubmitTestList,
    refreshAsync: refreshSubmitTestList,
    loading,
  } = useRequest<PaginationResult<SubmitTestVO>, [FindSubmitTestListParams]>(findSubmitTestList, {
    onSuccess: (res) => {
      setSubmitTestItemList(res?.items ?? [0]);
      if (res?.items && res?.items?.length > 0) {
        setSelectSubmitTestItemId(res?.items[0]?.id);
      }
    },
  });
  const [submitTestDetail, setSubmitTestDetail] = useState<SubmitTestVO>();

  const isShowLatest = selectSubmitTestItemId === submitTestList?.[0]?.id;

  const selectSubmitTestItem = useMemo(() => {
    return (submitTestList ?? []).find((item) => item.id === selectSubmitTestItemId);
  }, [selectSubmitTestItemId, submitTestList]);

  const handleSelectSubmitTestItem = useCallback((newSubmitTestDetail: SubmitTestVO) => {
    setSelectSubmitTestItemId(newSubmitTestDetail?.id);
  }, []);

  useEffect(() => {
    if (alterSheetId) {
      getSubmitTestList({
        alterSheetId,
      });
    }
  }, [alterSheetId, getSubmitTestList]);

  const initialValues = useMemo(() => {
    return (submitTestDetail ? {
      verifier: submitTestDetail?.verifier || iterationVO?.mainEntity?.testOwner,
      ccUserIds: submitTestDetail?.ccUserIds,
      emailAddressList: submitTestDetail?.emailAddressList,
      description: submitTestDetail?.description,
    } : {});
  }, [submitTestDetail, iterationVO?.mainEntity?.testOwner]);

  if (loading) {
    return <Skeleton />;
  }

  const submitDisabled =
    mainEntity?.type === 'AGILE_CI' && (mainEntity?.status === 'PUBLISHED' || mainEntity?.status === 'CLOSED');

  return (
    <Flex vertical gap={token.margin} style={{ width: '100%' }}>
      <NeedTestComponent scope={scope} alterSheetId={alterSheetId} />
      <Flex vertical className={styles.devTestContent}>
        <Flex align="center" justify="space-between" className={styles.actionArea}>
          <Flex align="center" justify="space-between">
            <SubmitTestItemSelector
              selectedSubmitTestItemId={selectSubmitTestItemId}
              dataSource={submitTestList ?? []}
              handleSelectSubmitTestItem={handleSelectSubmitTestItem}
            />
            {(submitTestList ?? []).length > 0 ? (
              <Flex className={styles.toLatestWrap}>
                {isShowLatest ? (
                  <span className={styles.displayText}>最新测试记录</span>
                ) : (
                  <InlineButton
                    type="text"
                    size="small"
                    className={styles.switchToLatest}
                    onClick={() => setSelectSubmitTestItemId(submitTestList?.[0]?.id)}
                  >
                    <ArrowSwitchOutlined style={{ color: token.colorTextSecondary }} />
                    切换到最新测试记录
                  </InlineButton>
                )}
              </Flex>
            ) : (
              <Flex className={styles.noRecordText}>无测试记录</Flex>
            )}
          </Flex>
          <Flex gap={token.marginXS}>
            {spaceId && submitTestDetail?.incrCodeScanGateRecordId && (
              <Button
                href={`/#/efficiency/${spaceId}/restrict-task-record/code-scan/${submitTestDetail?.incrCodeScanGateRecordId}?recordId=${submitTestDetail?.incrCodeScanGateRecordId}&recordType=code-scan`}
                target="_blank"
              >
                查看CR详情
              </Button>
            )}
            <Tooltip title={submitDisabled ? '生命周期已结束，禁止操作' : undefined}>
              <Button disabled={submitDisabled} type="primary" onClick={() => setSubmitTestVisible(true)}>
                提交测试
              </Button>
            </Tooltip>
          </Flex>
        </Flex>
        <Flex className={styles.content}>
          {selectSubmitTestItemId ? (
            <DevTestInfoList
              mainEntity={mainEntity}
              submitTestItem={selectSubmitTestItem}
              refreshSubmitTestList={refreshSubmitTestList}
              onSubmitTestDetailUpdate={(data) => setSubmitTestDetail(data)}
            />
          ) : (
            <Flex justify="center" style={{ width: '100%' }}>
              <EmptyCard bordered description="未提交测试，暂无记录" />
            </Flex>
          )}
        </Flex>
      </Flex>
      <SubmitTestDrawer
        visible={submitTestVisible}
        onClose={(isRefresh = false) => {
          if (isRefresh) {
            refreshSubmitTestList?.();
          }
          setSubmitTestVisible(false);
        }}
        iterationInfo={iterationVO}
        initialValues={initialValues}
      />
    </Flex>
  );
};

export default DevStageTest;
