.reportStatus {
  :global {
    .mc-icon {
      font-size: var(--mc-font-size-heading-3);
    }

    &:not(.mc-icon) {
      font-size: var(--mc-font-size-lg)
    }
  }

  &Pass {
    :global {
      .mc-icon {
        color: var(--mc-color-success);
      }
    }
  }
}

.tmqDescriptions {
  background-color: var(--mc-color-fill-secondary);
  padding: var(--mc-padding);
  border-radius: var(--mc-border-radius);

  :global {
    .mc-descriptions-title {
      font-size: var(--mc-font-size);
      font-weight: normal;
    }

    .mc-descriptions-header {
      margin-bottom: var(--mc-margin-xs);
    }

    .mc-descriptions-item-label {
      color: var(--mc-color-text);
    }

    .mc-descriptions-row >td {
      padding-bottom: var(--mc-padding-xs);
    }
  }
}
