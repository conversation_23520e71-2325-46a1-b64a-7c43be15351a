import React, { useMemo } from 'react';
import type { TestReportItem } from '@ali/mc-services';
import { Descriptions, DescriptionsProps, Flex, Table, Typography } from 'antd';

import styles from './TestReportItem.module.less';
import { Link } from 'ice';

export type TestReportItemProps = {
  isMain?: boolean;
} & TestReportItem;
export default function TestReportItem(props: TestReportItemProps) {
  const { isMain, ...report } = props;

  const summaryItems = useMemo(() => {
    return report.summary?.data?.map((item) => {
      const { itemName, conclusion } = item;

      return {
        key: itemName,
        label: itemName,
        children: conclusion,
      };
    });
  }, [report]);

  const reportDescriptionItems = useMemo(() => {
    const items: DescriptionsProps['items'] = [{
        key: 'conclusion',
        label: '测试结论',
        children: report.conclusion,
    }];

    if (report.detailUrl) {
      items.push({
        key: 'detailUrl',
        label: '测试报告',
        children: <Link to={report.detailUrl} target="_blank">点击查看详情</Link>,
      });
    }

    if (!!report.pictureUrls?.length) {
      items.push({
        key: 'pictureUrls',
        children: report.pictureUrls?.map((url) => <img key={url} src={url} alt="测试图片" style={{ maxWidth: '100%' }} />),
      });
    }

    return items;
  }, [report]);

  return (
    <Flex vertical className={styles.testReportItem} gap="middle">
      <Flex vertical>
        <Typography.Title level={isMain ? 3 : 4}>{report.itemName}</Typography.Title>
        <Typography.Paragraph type="secondary">{report.itemDesc}</Typography.Paragraph>
        <Descriptions
          column={1}
          items={reportDescriptionItems}
        />
      </Flex>

      <Flex vertical gap="middle">
        {report.summary && <Descriptions column={1} title="报告摘要" items={summaryItems} />}
        {!!report.tableContents?.length && (
          <Flex vertical gap="small">
            {report.tableContents.map((item) => {
              return (
                <Table
                  bordered
                  key={item.tableName}
                  title={() => item.tableName}
                  columns={item.headers}
                  dataSource={item.data}
                  pagination={false}
                />
              );
            })}
          </Flex>
        )}
      </Flex>
    </Flex>
  );
}
