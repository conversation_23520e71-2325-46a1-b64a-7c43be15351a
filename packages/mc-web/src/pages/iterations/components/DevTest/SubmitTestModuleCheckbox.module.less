.SubmitTestModuleCheckboxList {
  .list {
    :global {
      .mc-list-item {
        padding: 0 !important;
        margin-bottom: var(--mc-margin-xs);
      }
      .mc-list-header {
        border-block-end: none !important;
      }
      .mc-list-header {
        padding: 0 var(--mc-padding-sm) var(--mc-padding-xs) !important;
      }
    }
    .cardTitleWrap {
      font-weight: normal;
      font-size: calc(var(--mc-font-size-sm) / 2 + var(--mc-font-size-lg) / 2);
      color: var(--mc-color-text-secondary);
      .rightTitleWrap {
        font-size: var(--mc-font-size-sm);
        line-height: var(--mc-line-height-sm);
        .submodulesWrap {
          &:hover {
            color: var(--mc-color-link-hover);
            cursor: pointer;
          }
        }
      }
    }
    .contentRightWrap,
    .contentRightNullContent {
      flex: 1;
      padding-left: var(--mc-padding);
      border-left: var(--mc-line-width) solid var(--mc-color-border);
      font-size: var(--mc-font-size-sm);
      margin-left: var(--mc-margin);
      .selectTitle {
        white-space: nowrap;
        min-width: auto !important;
      }
    }
    .contentRightNullContent {
      border-left: none;
    }
    .selectTitle {
      min-width: 75px;
      word-break: keep-all;
      // text-align: right;
    }
  }
}
