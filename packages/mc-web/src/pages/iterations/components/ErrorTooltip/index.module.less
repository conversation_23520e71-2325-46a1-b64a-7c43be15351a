.errorTooltip{
  color:antiquewhite;
  :global{
    .mc-tooltip-arrow{
        color: var(--mc-color-error);
    }
    .mc-alert-message {
      font-weight: unset;
    }
  }
  p {
    margin-block: 0px !important;
  }

}

.labelStyle {
  white-space: no-wrap;
  margin-right: var(--mc-padding-xs);
  min-width: fit-content;
  &::after {
    content: ':';
  }
}

.errorAlertContent {
  .markdownBox::-webkit-scrollbar {
    display: none;
  }

  .markdownBox {
    max-width: calc(100% - 16px);
    max-height: 400px;
    overflow-y: auto;

    * {
      white-space: wrap;
    }

    ul {
      list-style: disc inside;
      color: rgba(4, 15, 36, 0.65);
      margin-block-start: var(--mc-margin-xs);
    }

    ol {
      list-style: decimal;
    }

    ul,
    ol {
      padding-left: var(--padding-lg);
    }
    p {
      color: rgba(4, 15, 36, 0.65);
    }
    h3 {
      margin-block-end: 0px;
      font-weight: 500;

    }

  }

  // .title {
  //   width: 760px;
  //   // -webkit-line-clamp: 3; // 设置两行文字溢出
  //   display: -webkit-box;
  //   /** 对象作为伸缩盒子模型显示 **/
  //   -webkit-box-orient: vertical;
  //   /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  //   overflow: hidden;
  //   /** 隐藏超出的内容 **/
  //   white-space: pre-wrap;

  // }

  .desc {
    margin-top: var(--mc-margin-xs);
    display: flex;
    align-items: center;


  }

  .tag {
    height: 20px;
    border-radius: 4px;
    background: rgba(255, 80, 0, 0.07);
    border: 1px solid rgba(255, 80, 0, 0.16);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--mc-padding-xs);
    font-size: var(--mc-font-size-sm);
    font-weight: normal;
    line-height: 12px;
    color: #ff5000;
    margin-right: 10px;
  }
}
