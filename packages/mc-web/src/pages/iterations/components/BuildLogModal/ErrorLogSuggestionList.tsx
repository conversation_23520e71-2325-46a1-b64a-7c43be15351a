import React from 'react';
import { Flex } from 'antd';
import styles from './index.module.less';
import { KeyLogAnalyzeResultType } from './type';
import SuggestionItem from './SuggestionItem';

interface ErrorLogSuggestionListProps{
    infoList: any[];
    height: string | number;
    aiSuggestionShow: boolean;
    similarSolutionsShow: boolean;
    rootDetailReal: {
        rootCause: string;
        coordinators: string;
    };
    keyLogAnalyzeResult: KeyLogAnalyzeResultType;
}

const ErrorLogSuggestionList = (props: ErrorLogSuggestionListProps) => {
  const { infoList, height, aiSuggestionShow, similarSolutionsShow,
      rootDetailReal, keyLogAnalyzeResult } = props;
  return (
    <Flex
      vertical
      className={styles.aiSoluteBig}
      style={{
        height,
        top: '0',
      }}
    >
      {
        (infoList ?? []).map((item: any, index: number) => {
            const isSuggestion = index === 0;
            if ((isSuggestion && aiSuggestionShow) || (!isSuggestion && similarSolutionsShow)) {
              return (
                <SuggestionItem
                  item={item}
                  index={index}
                  keyLogAnalyzeResult={keyLogAnalyzeResult}
                  key={item.key}
                  defaultExpanded={isSuggestion}
                  rootDetailReal={rootDetailReal}
                />
              );
            } else {
              return null;
            }
        })
      }
    </Flex>
  );
};
export default ErrorLogSuggestionList;
