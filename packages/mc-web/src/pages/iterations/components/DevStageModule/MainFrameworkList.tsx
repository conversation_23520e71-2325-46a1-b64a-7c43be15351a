import React, { useEffect, useState } from 'react';
import { Drawer, Button, Popconfirm, message, theme, Form, Input, Space, Flex, List } from 'antd';
import dayjs from 'dayjs';
import { User, StatusTag, UserSelect, DataEntryList, Copy } from '@ali/mc-uikit';
import { CopyOutlined } from '@ali/mc-icons';
import { useRequest } from '@ali/mc-request';
import {
  getMainFrameworkChangeRecordDefaultReviewer,
  deleteMainFrameworkChangeRecord,
  reviewMainFrameworkChangeRecord,
  GetMainFrameworkChangeRecordDefaultReviewerParams,
  DeleteMainFrameworkChangeRecordParams,
} from '@ali/mc-services/AlterSheet';
import { MainFrameworkChangeRecordReviewRequest, MainFrameworkChangeRecord } from '@ali/mc-services';
import { useIterationContext } from '@/components/Iteration/IterationProvider';
import { getProjectPath } from '@/util/utils';

const STATUS_MAP: { [key: string]: string } = {
  wait: '待评审',
  opened: '评审中',
  reopened: '评审中',
  accepted: '评审通过',
  merged: '已合并',
  closed: '已废弃',
};

type FieldType = {
  scmAddress?: string;
  sourceBranch?: string;
  targetBranch?: string;
  reviewer?: string[];
};

interface AlterSheetMainFrameworkListPageProps {
  data: MainFrameworkChangeRecord[];
  loading: boolean;
  getMainFrameworkList: () => void;
  targetBranch: string | null;
  scope: string;
}

// 迭代模块 - 壳工程
const MainFrameworkList = (props: AlterSheetMainFrameworkListPageProps) => {
  const { data: iterationVO = {} } = useIterationContext();
  const { mainEntity } = iterationVO;
  const {
    application,
  } = mainEntity || ({} as any);
  const scmAddress = application?.codeLibraryAddress ?? '';
  const { token } = theme.useToken();
  const { data, loading, getMainFrameworkList, targetBranch } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [checkData, setCheckData] = useState<MainFrameworkChangeRecord>({});
  const [defaultReviewer, setDefaultReviewer] = useState<string[]>([]);

  const { runAsync: requestMainFrameworkChangeRecordDefaultReviewer, loading: createLoading } = useRequest<
    string[],
    [GetMainFrameworkChangeRecordDefaultReviewerParams]
  >(getMainFrameworkChangeRecordDefaultReviewer);

  const { runAsync: requestDeleteMainFrameworkChangeRecord } = useRequest<
    boolean,
    [DeleteMainFrameworkChangeRecordParams]
  >(deleteMainFrameworkChangeRecord);

  const { runAsync: requestReviewMainFrameworkChangeRecord } = useRequest<
    MainFrameworkChangeRecord,
    [MainFrameworkChangeRecordReviewRequest]
  >(reviewMainFrameworkChangeRecord);

  const refreshMainFrameworkList = () => {
    getMainFrameworkList();
  };

  // 获取默认评审人
  useEffect(() => {
    if (scmAddress) {
      requestMainFrameworkChangeRecordDefaultReviewer({
        scmAddress,
      }).then((res) => {
        if (res && res?.length > 0) {
          setDefaultReviewer(res);
        }
      });
    }
  }, [scmAddress, requestMainFrameworkChangeRecordDefaultReviewer]);

  // 代码评审
  const onCodeReview = (record: MainFrameworkChangeRecord) => {
    setCheckData({
      ...record,
      reviewer: defaultReviewer,
    });
    setVisible(true);
  };

  // 代码评审提交
  const onSubmit = (values: FieldType) => {
    requestReviewMainFrameworkChangeRecord({
      id: checkData.id,
      linkUrl: window.location.href,
      reviewer: values.reviewer,
      scmAddress: values.scmAddress,
      sourceBranch: values.sourceBranch,
      targetBranch: values.targetBranch,
    }).then((res) => {
      if (res) {
        setVisible(false);
        refreshMainFrameworkList();
        message.success('发起评审成功！');
      }
    });
  };

  // 删除
  const onDelete = (record: MainFrameworkChangeRecord) => {
    requestDeleteMainFrameworkChangeRecord({
      id: Number(record.id),
    }).then((res) => {
      if (res) {
        refreshMainFrameworkList();
        message.success('删除成功！');
      }
    });
  };

  const getBranchUrl = (scmAddressArg: string, branch: string) => {
    const projectPath = getProjectPath(scmAddressArg);
    if (scmAddressArg.indexOf('*************************') > -1) {
      return `https://code.alipay.com/${projectPath}/tree/${branch}`;
    }
    return `https://code.alibaba-inc.com/${projectPath}/tree/${branch}`;
  };

  return (
    <>
      <DataEntryList
        loading={loading}
        dataSource={data}
        renderItem={(item: MainFrameworkChangeRecord) => {
          const { codeReviewRecord, sourceBranch } = item;
          const branchUrl = getBranchUrl(item.scmAddress || '', sourceBranch || '');
          let status = 'default';
          if (!codeReviewRecord || codeReviewRecord?.state === 'opened' || codeReviewRecord?.state === 'reopened') {
            status = 'processing';
          } else if (codeReviewRecord?.state === 'accepted' || codeReviewRecord?.state === 'merged') {
            status = 'success';
          } else if (codeReviewRecord?.state === 'closed') {
            status = 'default';
          }
          return (
            <List.Item
              actions={[
                <Flex
                  key="actions"
                  style={{
                    color: token.colorText,
                  }}
                >
                  {item.codeReviewId ? (
                    <Button
                      type="text"
                      onClick={() => {
                        if (item.codeReviewRecord?.detail_url) {
                          window.open(item.codeReviewRecord.detail_url, '_blank', 'noopener');
                        }
                      }}
                    >
                      评审详情
                    </Button>
                  ) : (
                    <Flex gap={token.marginXS} style={{ cursor: 'pointer' }}>
                      <Button
                        type="text"
                        onClick={() => {
                          onCodeReview(item);
                        }}
                      >
                        代码评审
                      </Button>
                      <Popconfirm
                        title="是否确认删除?"
                        onConfirm={() => {
                          onDelete(item);
                        }}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button type="text">删除</Button>
                      </Popconfirm>
                    </Flex>
                  )}
                </Flex>,
              ]}
            >
              <Flex
                align="center"
                justify="space-between"
                style={{
                  width: '100%',
                  fontSize: token.fontSizeSM,
                  color: token.colorTextSecondary,
                }}
              >
                <Flex vertical gap={token.marginXXS}>
                  <Flex
                    align="center"
                    gap={token.marginXS}
                    style={{
                      fontSize: token.fontSizeLG,
                      color: token.colorText,
                      fontWeight: token.fontWeightStrong,
                    }}
                  >
                    <span>壳工程</span>
                    <StatusTag color={status}>{STATUS_MAP[codeReviewRecord?.state || 'wait']}</StatusTag>
                  </Flex>
                  <Flex align="center" gap={token.margin}>
                    <Flex>
                      <span>创建人：</span>
                      <User size="xsmall" showAvatar empIds={item.creator} />
                    </Flex>
                    <Flex>
                      <span>创建时间：</span>
                      {dayjs(item.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
                    </Flex>
                    <Flex>
                      <span>评审人：</span>
                      <User size="xsmall" showAvatar empIds={item?.codeReviewRecord?.assignees} />
                    </Flex>
                  </Flex>
                  <Flex>
                    <span>分支名：</span>
                    <a
                      href={branchUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ marginRight: token.marginXS }}
                    >
                      {sourceBranch}
                    </a>
                    <Copy text={sourceBranch || ''}>
                      <CopyOutlined style={{ fontSize: token.fontSizeLG }} />
                    </Copy>
                  </Flex>
                </Flex>
              </Flex>
            </List.Item>
          );
        }}
      />
      <Drawer
        destroyOnClose
        maskClosable={false}
        title="壳工程代码评审"
        open={visible}
        closable
        width={480}
        onClose={() => {
          setCheckData({});
          setVisible(false);
        }}
      >
        <Form
          onFinish={onSubmit}
          initialValues={{
            scmAddress: checkData?.scmAddress,
            sourceBranch: checkData?.sourceBranch,
            targetBranch: targetBranch || checkData?.targetBranch,
            reviewer: checkData?.reviewer,
          }}
        >
          <Form.Item<FieldType> label="代码仓库" name="scmAddress">
            <Input disabled />
          </Form.Item>

          <Form.Item<FieldType> label="评审分支" name="sourceBranch">
            <Input disabled />
          </Form.Item>

          <Form.Item<FieldType>
            label="目标分支"
            name="targetBranch"
            rules={[{ required: true, message: '请输入必填参数!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item<FieldType> label="评审人" name="reviewer" rules={[{ required: true, message: '请输入必填参数!' }]}>
            <UserSelect mode="multiple" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button loading={createLoading} type="primary" htmlType="submit">
                确定
              </Button>
              <Button
                loading={createLoading}
                htmlType="button"
                onClick={() => {
                  setCheckData({});
                  setVisible(false);
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Drawer>
    </>
  );
};

export default MainFrameworkList;
