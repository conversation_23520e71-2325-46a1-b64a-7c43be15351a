.hasLessSubmoduleModal {
  .title {
    color: var(--mc-color-text-secondary);
  }
  .mainInfoCard {
    margin-bottom: var(--mc-margin-lg);
    .itemLabel {
      color: var(--mc-color-text-secondary);
    }
    .mainModuleLink {
      font-weight: var(--mc-font-weight-strong);
    }
    .primaryText {
      color: var(--mc-color-text);
    }
    .submoduleItem {
      &:not(:last-child) {
        padding-bottom: var(--mc-padding-lg);
        border-bottom: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
      }
    }
  }
}
