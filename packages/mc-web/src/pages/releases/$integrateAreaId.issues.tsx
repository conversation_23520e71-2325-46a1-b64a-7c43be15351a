import React, { useState, useEffect, useContext } from 'react';
import { Flex } from 'antd';
import { definePageConfig, useSearchParams } from 'ice';
import dayjs from 'dayjs';
import * as ExcelJS from 'exceljs';
import * as FileSaver from 'file-saver';
import { listWorkbenchItems } from '@ali/mc-services/Insight';
import { InsightItemDTO, InsightItemListQryVO, PaginationResult, User as UserType } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import { DONE_STATUS, PROBLEM_TYPE, METHODS, LEVEL } from './common/statusMap';
import { PURPOSES_ALL } from '@/constants/efficiency';
import { extractBetweenBrackets } from '@/util/utils';
import EventBus from '@/util/EventBus';
import PublishLayoutContext from '@/context/PublishLayoutContext';
import IssuesHeader from '@/pages/releases/components/IssuesHeader';
import IssuesList from '@/pages/releases/components/IssuesList';
import { getUserByEmpIds } from '@ali/mc-services/User';
import { uniq } from 'lodash-es';
import { FilterValue } from '@ali/mc-uikit/esm/DataEntryList/interface';
import AllFollowUp from './components/AllFollowUp';

interface ExtendInsightItemDTO extends InsightItemDTO {
  key?: number;
  releaseVersion?: string;
  processorInfo?: any;
}

const DEFAULT_PAGE_SIZE = 10000000;

export default function PublishIssue() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { integrateArea = {}, insightReleasesList = [] } = useContext(PublishLayoutContext);
  const { versionPlanReleaseVersion, versionPlanName, integrateApplication = {}, id: integrateAreaId } = integrateArea;
  const { id: applicationId } = integrateApplication;
  const version = versionPlanReleaseVersion ?? extractBetweenBrackets(versionPlanName);

  const [keyword, setKeyword] = useState('');
  const [showMine, setShowMine] = useState<boolean | undefined>(undefined);
  const [count, setCount] = useState<Record<string, number>>();
  const [exportLoading, setExportLoading] = useState(false);
  const [dataSource, setDataSource] = useState<ExtendInsightItemDTO[]>([]);
  const [loading, setLoading] = useState(false);
  const [categoryType, setCategoryType] = useState<string>('level');
  const [filterData, setFilterData] = useState<FilterValue>();

  const { runAsync: getWorkbench } = useRequest<PaginationResult<InsightItemDTO>, [InsightItemListQryVO]>(
    listWorkbenchItems,
    {
      debounceWait: 300,
    },
  );

  const status = searchParams.get('status') || 'UNDO';

  // 获取问题列表
  const getWorkbenchFunction = () => {
    setLoading(true);
    const idList = insightReleasesList.map((item: any) => item.id);
    getWorkbench({
      applicationIds: [Number(applicationId)],
      releaseIds: idList,
      pageNum: 0,
      pageSize: DEFAULT_PAGE_SIZE,
      planList: ['URGENT_INTEGRATE', 'ROLLBACK', 'TODO_NOTHING', 'OTHER', 'PUSH_SWITCH', 'PATCH_BUGFIX'],
    }).then((res) => {
      if (res) {
        const { items = [] } = res || {};
        const curHasHandle: ExtendInsightItemDTO[] = [];
        const curNoHandle: ExtendInsightItemDTO[] = [];
        const curCancelHandle: ExtendInsightItemDTO[] = [];
        items?.length > 0 &&
          items.forEach((item: InsightItemDTO) => {
            let newItem = {
              ...item,
              key: item.id,
              releaseVersion: item?.release?.version,
            };
            // 根据阻塞性排序 阻塞性在前
            if (item.status === 'DONE') {
              if (item.level === 'P0') {
                curHasHandle.unshift(newItem);
              } else {
                curHasHandle.push(newItem);
              }
            } else if (item.status === 'UNDO') {
              if (item.level === 'P0') {
                curNoHandle.unshift(newItem);
              } else {
                curNoHandle.push(newItem);
              }
            } else if (item.status === 'CANCEL') {
              if (item.level === 'P0') {
                curCancelHandle.unshift(newItem);
              } else {
                curCancelHandle.push(newItem);
              }
            }
          });
        curHasHandle.sort((itemM: InsightItemDTO, itemN: InsightItemDTO) => {
          return Number(itemN?.gmtProcess) - Number(itemM?.gmtProcess);
        });
        curNoHandle.sort((itemM: InsightItemDTO, itemN: InsightItemDTO) => {
          return Number(itemN?.gmtCreate) - Number(itemM?.gmtCreate);
        });

        let concatArr = curNoHandle.concat(curHasHandle).concat(curCancelHandle);
        // 拿处理人的组织架构信息
        let promiseArr = concatArr.map((item) => {
          return getUserByEmpIds({
            empIds: item.processor ?? '',
          }).then((info) => {
            return {
              ...item,
              processorInfo: info,
            };
          });
        });
        Promise.all(promiseArr).then((values) => {
          setDataSource(values);
          setLoading(false);
        });
        setCount({
          UNDOCount: curNoHandle.length,
          DONECount: curHasHandle.length,
          CANCELCount: curCancelHandle.length,
        });
      }
    });
  };
  const statusFilterChange = (value: string) => {
    setSearchParams((prev) => {
      prev.set('status', value);
      return prev;
    });
  };

  // 导出问题列表
  const exportData = async () => {
    setExportLoading(true);
    const workbook = new ExcelJS.Workbook();
    const sheet = workbook.addWorksheet('My Sheet');

    sheet.columns = [
      { header: '严重性', key: 'level' },
      { header: '问题', key: 'name' },
      { header: '处理方式', key: 'plan' },
      { header: '其他处理方式', key: 'planDetail' },
      { header: '问题类型', key: 'type' },
      { header: '关联发布', key: 'release' },
      { header: '反馈人', key: 'creator' },
      { header: '反馈时间', key: 'gmtCreate' },
      { header: '处理人', key: 'processor' },
      { header: '处理人所属组织', key: 'bu' },
      { header: '处理时间', key: 'gmtProcess' },
      { header: '处理状态', key: 'status' },
      { header: '问题进展', key: 'feedbackList' },
    ];

    dataSource.forEach((item: ExtendInsightItemDTO) => {
      sheet.addRow({
        level: item.level ? LEVEL[item?.level]?.label || item.level : '',
        name: item.content?.title || '',
        plan: item.plan ? METHODS[item?.plan] || item.plan : '',
        planDetail: item.planDetail ? item.planDetail : '',
        type: item.type ? PROBLEM_TYPE[item?.type] || item.type : '',
        release:
          (item?.release?.purpose
            ? PURPOSES_ALL[item.release.purpose]?.label
            : item?.release?.publishType === 'BETA'
              ? '灰度发布'
              : '正式发布') + item?.release?.version,
        creator: item.creatorNickName,
        gmtCreate: item.gmtCreate ? dayjs(item.gmtCreate).format('YYYY-MM-DD HH:mm:ss') : '',
        processor: item.processorNickName,
        bu: uniq(item.processorInfo.map((itemV: UserType) => itemV.bu)).join(','),
        gmtProcess: item.gmtProcess ? dayjs(item.gmtProcess).format('YYYY-MM-DD HH:mm:ss') : '',
        status: item.status ? DONE_STATUS[item?.status]?.label || item.status : '',
        feedbackList: item?.feedbackList?.map((itemV, index) => (itemV.desc ? `${index + 1}、${itemV.desc}` : '')).join('\n'),
      });
    });

    try {
      let buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      // 使用 file-saver 库保存 Blob 为文件
      FileSaver(blob, `【${version}发版问题】— ${integrateApplication?.name}.xlsx`); // 这里会触发文件下载
      setExportLoading(false);
    } catch (error) {
      console.error('Error writing file:', error);
      setExportLoading(false);
    }
  };

  useEffect(() => {
    if (applicationId && insightReleasesList.length > 0) {
      getWorkbenchFunction();
    }
  }, [applicationId, insightReleasesList, version]);

  useEffect(() => {
    EventBus.on('PROBLEM_ADD_SUCCESS', () => {
      getWorkbenchFunction();
    });
    return () => {
      EventBus.removeAllListeners('PROBLEM_ADD_SUCCESS');
    };
  }, [applicationId, insightReleasesList]);

  const allFollowUpButton = (
    <AllFollowUp
      dataSource={dataSource}
      releaseId={searchParams.get('releaseId')}
      integrateAreaId={integrateAreaId}
    />
  );

  return (
    <Flex vertical gap="middle">
      <IssuesHeader
        count={count}
        onExport={exportData}
        onReload={getWorkbenchFunction}
        onChange={setKeyword}
        onShowMineChange={setShowMine}
        status={status}
        exportLoading={exportLoading}
        dataSource={dataSource}
        loading={loading}
        categoryType={categoryType}
        keyword={keyword}
        allFollowUpButton={allFollowUpButton}
        onCategoryTypeChange={(value: string) => {
          setFilterData(undefined);
          setCategoryType(value);
          setKeyword('');
          setSearchParams((prev) => {
            prev.set('status', 'UNDO');
            return prev;
          });
        }}
      />
      <IssuesList
        dataSource={dataSource}
        loading={loading}
        insightReleasesList={insightReleasesList}
        count={count}
        integrateArea={integrateArea}
        statusFilterChange={statusFilterChange}
        status={status}
        keyword={keyword}
        showMine={showMine}
        onProcess={() => {
          getWorkbenchFunction();
        }}
        categoryType={categoryType}
        filterData={filterData}
        setFilterData={setFilterData}
      />
    </Flex>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '发布工作台 - 问题列表',
}));

