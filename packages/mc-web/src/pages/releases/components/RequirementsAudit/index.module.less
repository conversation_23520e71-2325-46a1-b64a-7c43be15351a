.desc {
  padding-bottom: var(--mc-padding);
  color: var(--mc-color-text-secondary);
  :global {
    .mc-table-tbody {
      .highlightRow {
        background: #f6f8fa !important;
        cursor: pointer;
      }
    }
    .mc-table-thead > tr > th {
      background: #f6f8fa !important;
    }
    .mc-table-tbody > tr:hover > td {
      background-color: #f6f8fa !important;
    }
    .mc-table-wrapper .mc-table-cell,
    .mc-table-wrapper .mc-table-thead > tr > th {
      padding: var(--mc-padding-sm) calc(var(--mc-padding-sm) / 2 + var(--mc-padding-md) / 2);
    }
  }
}
