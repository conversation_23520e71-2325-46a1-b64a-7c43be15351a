import React from 'react';
import { Flex, theme } from 'antd';
import { InsightItemListQryVO, InsightItemCount } from '@ali/mc-services';
import { TabUndoIcon, Done } from '@/components/Icons';
import styles from '../index.module.less';

interface ReleaseInfoAddonTopProps {
  mySelf: string;
  query: InsightItemListQryVO;
  setQuery: (query: InsightItemListQryVO) => void;
  listLoading: boolean;
  countData: InsightItemCount | null;
  setAllCheckItemNum: (params: any) => void;
  setWorkbenchItems: (params: any) => void;
  getTabsCount: (params: { status: string; mySelf: boolean }) => void;
}
// 已处理未处理状态筛选
export default function ReleaseInfoAddonTop(props: ReleaseInfoAddonTopProps) {
  const {
    query,
    mySelf,
    listLoading,
    setQuery,
    countData,
    setAllCheckItemNum,
    setWorkbenchItems,
    getTabsCount,
  } = props;
  const { token: themeToken } = theme.useToken();

  return (
    <Flex gap={themeToken.margin} className={styles.headerAddonTop}>
      <Flex
        gap={themeToken.marginXXS}
        className={query.status === 'UNDO' ? styles.select : styles.statusIcon}
        align="center"
        onClick={() => {
          if (!listLoading) {
            setWorkbenchItems(null);
            setQuery({
              ...query,
              status: 'UNDO',
              isFirstCrash: undefined,
              crashType: undefined,
              crashCountAsc: false,
            });
            if (countData?.undoCount !== 0) {
              getTabsCount({ status: 'UNDO', mySelf: mySelf === 'my' });
            }
            if (countData?.undoCount === 0) {
              setAllCheckItemNum({
                regressionNum: 0,
                crashNum: 0,
                feedbackNum: 0,
                checkItemNum: 0,
                customNum: 0,
              });
              setQuery({
                ...query,
                status: 'UNDO',
                type: 'REGRESSION',
              });
            }
          }
        }}
      >
        <TabUndoIcon />
        <span>待办</span>
        <span>{countData?.undoCount}</span>
      </Flex>
      <Flex
        gap={themeToken.marginXXS}
        className={query.status === 'DONE' ? styles.select : styles.statusIcon}
        align="center"
        onClick={() => {
          if (!listLoading) {
            setQuery({
              ...query,
              status: 'DONE',
              isFirstCrash: undefined,
              crashType: undefined,
              crashCountAsc: undefined,
            });
            if (countData?.doneCount !== 0) {
              getTabsCount({ status: 'DONE', mySelf: mySelf === 'my' });
            }
            if (countData?.doneCount === 0) {
              setAllCheckItemNum({
                regressionNum: 0,
                crashNum: 0,
                feedbackNum: 0,
                checkItemNum: 0,
                customNum: 0,
              });
              setWorkbenchItems(null);
              setQuery({
                ...query,
                status: 'DONE',
                type: 'REGRESSION',
              });
            }
          }
        }}
      >
        <Done />
        <span>已处理</span>
        <span>{countData?.doneCount} </span>
      </Flex>
    </Flex>
  );
}
