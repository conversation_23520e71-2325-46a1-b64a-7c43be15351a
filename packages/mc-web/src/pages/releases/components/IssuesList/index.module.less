
.issuesList {
  .issuesCategoryTypeListItem {
    .title {
      padding: var(--mc-padding-content-vertical) var(--mc-padding-content-horizontal);
      background: var(--mc-list-header-bg);
      cursor: pointer;
    }

    :global {
      .mc-list-item {
        .mc-list-item-action {
          width: 450px;
          justify-content: right;
        }
      }
    }
  }

  .content {
    font-size: var(--mc-font-size-sm);
    font-weight: 400;
    color: var(--mc-color-text-secondary);
  }

  .normal {
    color: var(--mc-color-text-secondary);
    &:hover {
      color: var(--mc-color-text);
    }
  }
  .active {
    color: var(--mc-color-text);
    font-weight: 500;
  }

  :global {
    .mc-list-item {
      .mc-list-item-meta {
        .mc-list-item-meta-content {
          .mc-list-item-meta-title {
            position: absolute;
            right: 16px;
            left: 16px;

            a {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .mc-list-item-meta-description {
            margin-top: 29px;
          }
        }
      }

      >div:not([class*="-list-item-meta"]):not([class*="-list-item-action"]) {
        margin-top: 30px;
      }
    }
  }
}
