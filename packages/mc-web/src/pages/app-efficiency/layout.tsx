import React, { useEffect, useMemo } from 'react';
import { defineDataLoader, Outlet, useParams, useLocation } from 'ice';
import { ShouldRevalidateFunctionArgs, useNavigate } from 'react-router-dom';
import ApplicationProvider, { useApplicationContext } from '@/components/Application/ApplicationProvider';
import { RequestContext } from '@ice/runtime/types';
import { matchRouteParams } from '@/util/router';
import { findAppDetailById } from '@ali/mc-services/Application';
import { ApplicationBO } from '@ali/mc-services';
import GlobalLayout from '@/components/GlobalLayout';
import { TabsProps } from 'antd';
// import AppEfficiencyPageHeader from './components/AppEfficiencyPageHeader';
import { useLayoutContext } from '@ali/mc-uikit';
import { GlobeOutlined } from '@ali/mc-icons';
import EfficiencyScopeSelect from '@/components/EfficiencyScopeSelect';
import { useDeferData } from '@/hooks/useDeferData';

const tabItems: TabsProps['items'] = [
  {
    key: 'insights',
    label: '效能洞察',
    icon: <GlobeOutlined />,
  },
];

export function ApplicationEfficiencyLayout(_props: { applicationId: number }) {
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const { setBreadcrumbItems } = useLayoutContext();

  // const { applicationId } = props;
  const { data: applicationBO, loading } = useApplicationContext();

  const breadcrumbItems = useMemo(
    () => [
      {
        title: '效能洞察',
      },
      {
        title: (
          <EfficiencyScopeSelect
            variant="filled"
            type="client"
            value={applicationBO?.id}
            onChange={(type, value) => {
              if (type === 'client') {
                navigate(`/app-efficiency/${value}/insights`);
              } else {
                navigate(`/efficiency/${value}/insights`);
              }
            }}
          />
        ),
      },
    ],
    [applicationBO?.id, navigate],
  );

  // const onTabClick = (key: string) => {
  //   navigate(`/app-efficiency/${applicationId}/${key}`);
  // };

  useEffect(() => {
    const [key] = pathname.split('/').reverse();
    const currentItem = tabItems?.find((item) => item.key === key);
    if (currentItem) {
      setBreadcrumbItems([
        ...breadcrumbItems,
        // {
        //   title: currentItem.label,
        // },
      ]);
    } else {
      setBreadcrumbItems(breadcrumbItems);
    }
  }, [breadcrumbItems, pathname, setBreadcrumbItems]);

  return (
    <ApplicationProvider
      applicationBO={applicationBO}
    >
      <GlobalLayout
        size="xl"
        loading={loading}
        // pageHeader={
        //   loading ? (
        //     <Skeleton.Button active style={{ minWidth: 300 }} />
        //   ) : (
        //     <AppEfficiencyPageHeader
        //       applicationId={applicationId}
        //       items={tabItems}
        //       pathname={pathname}
        //       onTabClick={onTabClick}
        //     />
        //   )
        // }
      >
        <Outlet />
      </GlobalLayout>
    </ApplicationProvider>
  );
}

export default function LayoutWrapper() {
  const params = useParams();
  const applicationId = parseInt(params.applicationId as string, 10);

  const { data } = useDeferData<{
    applicationBO: ApplicationBO;
  }>();

  return (
    <ApplicationProvider applicationBO={data?.applicationBO}>
      <ApplicationEfficiencyLayout applicationId={applicationId} />
    </ApplicationProvider>
  );
}

export const dataLoader = defineDataLoader(
  async (ctx: RequestContext) => {
    let applicationBO;
    const params = matchRouteParams('/app-efficiency/:applicationId/*', ctx.pathname);

    if (params?.applicationId) {
      const { applicationId } = params;
      applicationBO = await findAppDetailById({
        id: parseInt(applicationId as string, 10),
      });
    }

    return { applicationBO };
  },
  { defer: true },
);

export const shouldRevalidate = (args: ShouldRevalidateFunctionArgs) => {
  if (window.force_revalidate) {
    return true;
  }

  const { currentParams, nextParams } = args;
  return currentParams.applicationId !== nextParams.applicationId;
};
