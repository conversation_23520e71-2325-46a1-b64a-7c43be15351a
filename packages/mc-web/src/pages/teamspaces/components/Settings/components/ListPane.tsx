import React, { useCallback, useEffect } from 'react';

import { Table, TableProps, message, Modal, Flex, theme } from 'antd';

import {
  GetAnnouncementParams,
  getAnnouncement,
  RemoveAnnouncementStickinessParams,
  removeAnnouncementStickiness,
  stickAnnouncementOnTop,
  StickAnnouncementOnTopParams,
  deleteAnnouncement,
  DeleteAnnouncementParams,
} from '@ali/mc-services/CollaborationSpaceOverview';
import { StatusTag, User } from '@ali/mc-uikit';
import { CollaborationSpaceAnnouncementBO } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import dayjs from 'dayjs';

interface ListPaneProps {
  spaceId?: number;
  onEdit: (annInfo: CollaborationSpaceAnnouncementBO) => void;
}
const ListPane = (props: ListPaneProps) => {
  const { spaceId, onEdit } = props;
  const [modal, contextHolder] = Modal.useModal();
  const { token } = theme.useToken();

  const {
    data: announcements,
    runAsync: requestAnnouncements,
    loading = true,
  } = useRequest<CollaborationSpaceAnnouncementBO[], [GetAnnouncementParams]>(getAnnouncement);
  const { runAsync: requestRemoveAnnouncements } = useRequest<boolean, [RemoveAnnouncementStickinessParams]>(
    removeAnnouncementStickiness,
  );
  const { runAsync: requestStickAnnouncements } = useRequest<boolean, [StickAnnouncementOnTopParams]>(
    stickAnnouncementOnTop,
  );
  const { runAsync: requestDeleteAnnouncements } = useRequest<boolean, [DeleteAnnouncementParams]>(deleteAnnouncement);

  const getAnnouncements = useCallback(() => {
    if (spaceId) {
      requestAnnouncements({
        collaborationSpaceId: spaceId,
      });
    }
  }, [requestAnnouncements, spaceId]);

  const offlineAnnouncement = (id: number | undefined) => {
    if (spaceId && id) {
      requestRemoveAnnouncements({
        collaborationSpaceId: spaceId,
        id,
      }).then((res) => {
        if (res) {
          message.success('下线成功');
          getAnnouncements();
        }
      });
    }
  };

  const stickAnnouncement = (id: number | undefined) => {
    if (spaceId && id) {
      requestStickAnnouncements({
        collaborationSpaceId: spaceId,
        id,
      }).then((res) => {
        if (res) {
          message.success('删除成功');
          getAnnouncements();
        }
      });
    }
  };

  const deleteMineAnnouncement = (id: number | undefined) => {
    if (spaceId && id) {
      requestDeleteAnnouncements({
        collaborationSpaceId: spaceId,
        id,
      }).then((res) => {
        if (res) {
          message.success('删除成功');
          getAnnouncements();
        }
      });
    }
  };

  const confirmOperation = (titleType: string, callBack: () => void) => {
    modal.confirm({
      title: `确认¥${titleType}`,
      content: `确认${titleType}该公告`,
      onOk: () => {
        callBack.call(this);
      },
    });
  };

  useEffect(() => {
    getAnnouncements();
  }, [getAnnouncements, spaceId]);

  const columns: TableProps<CollaborationSpaceAnnouncementBO>['columns'] = [
    {
      title: '发布人',
      dataIndex: 'creator',
      key: 'creator',
      width: 90,
      render: (text: string) => <User empIds={text} showAvatar size={20} />,
    },
    {
      title: '状态',
      dataIndex: 'stickyOnTop',
      key: 'stickyOnTop',
      width: 70,
      render: (text: boolean) => (
        <StatusTag color={text ? 'success' : 'default'}>{text ? '生效中' : '已下线'}</StatusTag>
      ),
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      width: 175,
      render: (content: string) => {
        const data = content && content.indexOf('{') !== -1 && content.indexOf('}') !== -1 ? JSON.parse(content) : {};
        return <div dangerouslySetInnerHTML={{ __html: data.htmlContent }} />;
      },
    },
    {
      title: '发布时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      width: 130,
      render: (gmtCreate) => dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'actions',
      key: 'actions',
      width: 140,
      render: (_, record) => {
        return (
          <Flex gap={token.marginXS}>
            {record.stickyOnTop ? (
              <a
                onClick={() => {
                  confirmOperation('下线', () => {
                    offlineAnnouncement(record?.id);
                  });
                }}
              >
                下线
              </a>
            ) : (
              <a
                onClick={() => {
                  confirmOperation('上线', () => {
                    stickAnnouncement(record?.id);
                  });
                }}
              >
                上线
              </a>
            )}
            <a
              onClick={() => {
                onEdit?.(record);
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                confirmOperation('删除', () => {
                  deleteMineAnnouncement(record?.id);
                });
              }}
            >
              删除
            </a>
          </Flex>
        );
      },
    },
  ];

  return (
    <>
      <Table
        loading={loading}
        rowKey="empId"
        columns={columns}
        dataSource={announcements}
        pagination={{
          position: ['bottomCenter'],
        }}
      />
      {contextHolder}
    </>
  );
};

export default ListPane;
