import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useAppData } from 'ice';

import classNames from 'classnames';
import { SearchOutlined } from '@ali/mc-icons';
import { useRequest } from '@ali/mc-request';
import { CollaborationSpaceBO, CollaborationSpaceModuleBizVO, ModuleBizIdentityListItemVO } from '@ali/mc-services';
import { getSpaceBizModules, getSpaceBizs, GetSpaceBizsParams } from '@ali/mc-services/ModuleBiz';
import { DataEntryList } from '@ali/mc-uikit';
import { DataEntryFilterType } from '@ali/mc-uikit/esm/FilterGroup';
import { Badge, Button, Checkbox, Flex, Input, Skeleton, theme } from 'antd';
import { debounce, lowerCase } from 'lodash-es';
import BizModuleConfigModal from './BizModuleConfigModal';
import BizModuleListItem from './BizModuleListItem';

import styles from './BizModuleEntryList.module.less';

const DEFAULT_PAGE_SIZE = 20;
const defaultFilters: DataEntryFilterType[] = [
  {
    name: 'platform',
    label: '平台类型',
    items: [
      {
        value: '',
        label: '全部',
        title: '全部',
      },
      {
        value: 'ANDROID',
        label: 'Android',
        title: 'Android',
      },
      {
        value: 'IOS',
        label: 'iOS',
        title: 'iOS',
      },
      {
        value: 'WINDOWS',
        label: 'Windows',
        title: 'Windows',
      },
      {
        value: 'MAC',
        label: 'Mac',
        title: 'Mac',
      },
      {
        value: 'HARMONY',
        label: 'Harmony',
        title: 'Harmony',
      },
    ],
  },
  {
    name: 'owner',
    label: '负责人',
    type: 'user',
    multiple: false,
  },
];

type BizModuleEntryListProps = {
  collaborationSpace?: CollaborationSpaceBO;
};
export default function BizModuleEntryList(props: BizModuleEntryListProps) {
  const { collaborationSpace } = props;
  const { id: spaceId } = collaborationSpace || {};

  const { token } = theme.useToken();
  const { user } = useAppData() || {};

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [activeKey, setActiveKey] = useState<string>();
  const [platform, setPlatform] = useState<string>();
  const [keyword, setKeyword] = useState<string>();
  const [moduleOwner, setModuleOwner] = useState<string>();
  const [bizModules, setBizModules] = useState<Record<string, ModuleBizIdentityListItemVO[]>>({});

  const {
    data: spaceBizs,
    runAsync: requestSpaceBizs,
    loading = true,
  } = useRequest<CollaborationSpaceModuleBizVO[], [GetSpaceBizsParams]>(getSpaceBizs);

  const [selectItems, setSelectItems] = useState<ModuleBizIdentityListItemVO | ModuleBizIdentityListItemVO[]>();
  const [open, setOpen] = useState<boolean>(false);

  const requestData = useCallback(async () => {
    if (!spaceId) return;

    const bizs = await requestSpaceBizs({ spaceId });
    const modules = await Promise.all(
      bizs.map((biz) => {
        return getSpaceBizModules({ spaceId, bizCode: biz.bizCode as string });
      }),
    );

    const data: typeof bizModules = {};
    bizs?.forEach((spaceBiz, index) => {
      data[spaceBiz.bizCode as string] = modules[index] || [];
    });
    setBizModules(data);
  }, [requestSpaceBizs, spaceId]);

  useEffect(() => {
    if (spaceId) {
      requestData();
    }
  }, [requestData, spaceId]);

  const currentWorkId = user?.empId;
  const isSystemAdmin = user?.isAdmin;
  const isAdmin =
    collaborationSpace?.admins?.split(',').includes(currentWorkId) || collaborationSpace?.owner === currentWorkId;

  const filters = useMemo(() => {
    return [...defaultFilters];
  }, []);

  const onDelete = useCallback((record: ModuleBizIdentityListItemVO) => {
    setSelectItems((prev) => {
      if (Array.isArray(prev)) {
        return prev.filter((item) => item.moduleId !== record.moduleId);
      } else {
        return [];
      }
    });
  }, []);

  const closeModal = useCallback(() => {
    setOpen(false);
  }, []);

  const onOk = useCallback(
    (records: Record<string, ModuleBizIdentityListItemVO>) => {
      // 由于目前需要全量获取列表，这里在更新信息之后，直接更新本地数据来展示
      setBizModules((prev) => {
        const items = prev[activeKey || (spaceBizs?.[0].bizCode as string)];
        for (const item of items) {
          if (records[item.identifier as string]) {
            item.pendingBizCode = records[item.identifier as string].pendingBizCode;
            item.pendingBizName = records[item.identifier as string].pendingBizName;
          }
        }
        return {
          ...prev,
          [activeKey as string]: records,
        };
      });
      closeModal();
    },
    [activeKey, closeModal, spaceBizs],
  );

  const dataSource = useMemo(() => {
    let modules: ModuleBizIdentityListItemVO[] = [];
    if (activeKey) {
      modules = bizModules[activeKey] || [];
    } else {
      modules = bizModules?.[spaceBizs?.[0].bizCode as string] || [];
    }

    if (platform) {
      modules = modules.filter((item) => item.platformType === platform);
    }

    if (keyword?.length) {
      modules = modules.filter((item) => lowerCase(item.name).includes(lowerCase(keyword)));
    }

    if (moduleOwner?.length) {
      modules = modules.filter((item) => item.owner?.includes(moduleOwner));
    }

    return modules;
  }, [activeKey, platform, keyword, moduleOwner, bizModules, spaceBizs]);

  const tabs: Array<{ key: string; label: string }> = useMemo(() => {
    const items: any[] = [];

    if (spaceBizs) {
      spaceBizs.forEach((spaceBiz) => {
        items.push({
          key: spaceBiz.bizCode as string,
          label: spaceBiz.bizName,
        });
      });
    }

    return items;
  }, [spaceBizs]);

  /**
   * 计算当前页的实际条目数量，用于计算全选  Checkbox 的状态
   */
  const totalCount: number = dataSource.length || 0;
  const currentPageSize: number = useMemo(() => {
    const counter = Math.floor(totalCount / DEFAULT_PAGE_SIZE);
    const remainder = totalCount % DEFAULT_PAGE_SIZE;
    // 刚好完整分页
    if (remainder === 0) {
      return DEFAULT_PAGE_SIZE;
    }

    return counter >= currentPage ? DEFAULT_PAGE_SIZE : remainder;
  }, [currentPage, totalCount]);

  // 分组加载完之后，是否还在在加载数据
  const isDataLoading = !spaceBizs?.length || !bizModules[String(spaceBizs?.[0]?.bizCode)];
  const isMultiSelect = !!selectItems && Array.isArray(selectItems);

  return (
    <Flex vertical gap="middle">
      <Flex>
        <Flex align="center" gap="small">
          <Input
            prefix={<SearchOutlined />}
            style={{ width: 443 }}
            placeholder="输入名称过滤搜索"
            onChange={debounce((e) => setKeyword(e.target.value), 300)}
            allowClear
          />
        </Flex>
      </Flex>
      <DataEntryList
        headerAddonTop={
          loading || isDataLoading ? (
            <Skeleton.Input active style={{ width: '33%' }} block />
          ) : (
            <Flex gap="small">
              {tabs.map((item) => {
                return (
                  <Button
                    type="text"
                    className={classNames({ [styles.activeTab]: (activeKey || tabs?.[0]?.key) === item.key })}
                    key={item.key}
                    onClick={() => {
                      setSelectItems([]);
                      setActiveKey(item.key);
                    }}
                  >
                    <Flex gap={token.paddingXXS} align="center">
                      {item.label}
                      <Badge
                        count={bizModules?.[item.key]?.length}
                        overflowCount={99}
                        style={{
                          color: token.colorTextSecondary,
                          backgroundColor: token.colorBgContainer,
                          borderColor: token.colorBgContainer,
                          paddingLeft: token.paddingXXS,
                          paddingRight: token.paddingXXS,
                        }}
                        // offset={[10, -5]}
                      />
                    </Flex>
                  </Button>
                );
              })}
            </Flex>
          )
        }
        title={
          isAdmin || isSystemAdmin ? (
            <Flex gap="small">
              <Checkbox
                indeterminate={isMultiSelect && selectItems.length > 0 && selectItems.length < currentPageSize}
                checked={isMultiSelect && selectItems.length === currentPageSize}
                onChange={(e) => {
                  const { checked } = e.target;
                  if (checked) {
                    setSelectItems(
                      dataSource.slice(DEFAULT_PAGE_SIZE * (currentPage - 1), DEFAULT_PAGE_SIZE * currentPage),
                    );
                  } else {
                    setSelectItems([]);
                  }
                }}
              />
              全选
            </Flex>
          ) : null
        }
        filters={filters}
        onFilter={(values) => {
          setPlatform(values.platform as string);
          setModuleOwner(values.owner as string);
        }}
        dataSource={dataSource}
        loading={isDataLoading}
        extra={
          <Button
            disabled={(!isAdmin && !isSystemAdmin) || !isMultiSelect || selectItems.length < 1}
            style={{ marginLeft: 'auto' }}
            onClick={() => setOpen(true)}
          >
            批量配置项目
          </Button>
        }
        renderItem={(item: ModuleBizIdentityListItemVO) => {
          return (
            <BizModuleListItem
              item={item}
              isAdmin={isAdmin || isSystemAdmin}
              onSetting={() => {
                setSelectItems(item);
                setOpen(true);
              }}
              selectItems={isMultiSelect ? selectItems : []}
              onChange={(currentItem, selected) => {
                if (selected) {
                  const exists = isMultiSelect && selectItems.find((i) => i.moduleId === currentItem.moduleId);
                  if (!exists) {
                    setSelectItems(isMultiSelect ? [...selectItems, currentItem] : [currentItem]);
                  }
                } else {
                  setSelectItems(isMultiSelect ? selectItems.filter((i) => i.moduleId !== currentItem.moduleId) : []);
                }
              }}
            />
          );
        }}
        pagination={
          totalCount < DEFAULT_PAGE_SIZE
            ? false
            : {
                pageSize: DEFAULT_PAGE_SIZE,
                onChange: (page: number /* , pageSize: number */) => {
                  setSelectItems([]);
                  setCurrentPage(page);
                },
              }
        }
      />
      <BizModuleConfigModal
        spaceId={spaceId}
        bizModuleItems={isMultiSelect ? (selectItems as ModuleBizIdentityListItemVO[]) : [selectItems!]}
        open={open}
        onOk={onOk}
        onDelete={onDelete}
        onClose={closeModal}
        onCancel={closeModal}
      />
    </Flex>
  );
}
