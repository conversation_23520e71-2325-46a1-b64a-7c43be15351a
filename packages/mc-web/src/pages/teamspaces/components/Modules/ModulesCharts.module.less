.modulesNumCharts {
  @lineItemCount: 4;
  width: calc((100% - calc(@lineItemCount - 1) * var(--mc-margin)) / @lineItemCount);
  border: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
  border-radius: var(--mc-border-radius-lg);
  padding: var(--mc-padding);

  .numCharts_titleBox {
    color: var(--mc-color-text-label);
    // line-height: var(--mc-line-height-lg);
    line-height:  1.7142857143; // 特殊情况，这里不是标准的行高
    font-size: var(--mc-font-size);

    > span:last-of-type {
      margin-left: auto;
    }
  }
  .numCharts_numBox {
    color: var(--mc-color-text);
    font-size: var(--mc-font-size-heading-4);
    line-height: var(--mc-line-height);
  }
}

.chartCard {
  display: flex;
  flex-direction: column;

  :global {
    .mc-card-head {
      background-color: transparent;
      border-bottom: none;
    }
    .mc-card-body {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    // .mc-card-body:has(.chartsLoading) {
    //   display: flex;
    //   flex: 1;
    // }
  }

  .chartsLoading {
    width: 100%;
  }

  .chartsTitle {
    width: 100%;
    color: var(--mc-color-text);
    font-size: var(--mc-font-size);
    min-height: calc(var(--mc-control-height-lg) * 1.4);
  }
}

.activeCard {
  width: calc((100% - var(--mc-margin)) * 0.6);
}
.platformCard {
  width: calc((100% - var(--mc-margin)) * 0.4);
}
