import { requiredRule } from '@/pages/iterations/common/utils';
import { useRequest } from '@ali/mc-request';
import { Pipeline } from '@ali/mc-services';
import { savePipelineBasicInfo, SavePipelineBasicInfoParams } from '@ali/mc-services/Pipeline';
import { UserSelect } from '@ali/mc-uikit';
import { Button, Drawer, Flex, Form, Input, message, theme } from 'antd';
import React, { ReactNode, useEffect, useState } from 'react';
const PipelineInfoEditDrawer = ({ btnText, pipelineDetail, refresh }:
  {
    btnText?: ReactNode;
    pipelineDetail?: Pipeline;
    refresh: () => void;
  }) => {
  const [form] = Form.useForm();
  const { setFieldsValue } = form;
  const { token } = theme.useToken();
  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    if (open && pipelineDetail) {
      setFieldsValue({
        name: pipelineDetail?.name,
        notifier: pipelineDetail?.notifier,
      });
    }
  }, [pipelineDetail, setFieldsValue, open]);

  const {
    runAsync: savePipelineInfo,
    loading: savePipelineInfoLoading,
  } = useRequest<object, [SavePipelineBasicInfoParams]>(savePipelineBasicInfo);

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      // console.log('values', values);
      const { name, notifier } = values;
      if (pipelineDetail?.id) {
        savePipelineInfo({
          pipelineId: pipelineDetail?.id,
          pipelineName: name,
          notifier: Array.isArray(notifier) ? notifier.join(',') : notifier,
        }).then(res => {
          if (res) {
            message.success('流水线信息修改成功');
            setOpen(false);
            refresh?.();
          } else {
            message.error('流水线信息修改失败');
          }
        }).catch(err => {
          console.error(err);
        });
      }
    }).catch(err => {
      console.error(err);
    });
  };
  return (<>
    <Button onClick={() => setOpen(true)}>
      {btnText || '流水线设置'}
    </Button>
    <Drawer
      getContainer={() => document.getElementById('pipelineDetailGraphContainer') || document.body}
      rootStyle={{ position: 'absolute' }}
      open={open}
      closeIcon={null}
      title="流水线设置"
      styles={{
        wrapper: {
          width: '594px',
          boxShadow: 'none',
        },

        header: {
          borderBottom: 'none',
          paddingBottom: 0,
        },
      }}
      destroyOnClose
      onClose={() => {
        setOpen(false);
      }}
      footer={<Flex
        justify="flex-end"
        align="center"
        style={{ width: '100%' }}
        gap={token.margin}
      >
        <Button onClick={() => setOpen(false)} disabled={savePipelineInfoLoading}>
          取消
        </Button>
        <Button type="primary" onClick={handleSubmit} loading={savePipelineInfoLoading}>
          保存
        </Button>
      </Flex>}
    >
      <Form form={form} layout="vertical">
        <Form.Item name="name" label="名称" rules={[requiredRule]}>
          <Input />
        </Form.Item>
        <Form.Item name="notifier" label="流水线消息订阅人" >
          <UserSelect mode="multiple" />
        </Form.Item>
      </Form>
    </Drawer>

  </>);
};
export default PipelineInfoEditDrawer;
