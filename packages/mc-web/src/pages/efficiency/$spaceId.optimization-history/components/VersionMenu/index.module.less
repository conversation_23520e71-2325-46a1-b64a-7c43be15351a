.versionMenuContainer {
  position: relative;
  height: fit-content;
  min-height: 104px;
  .versionMenu {
    width: 180px;
    transition: all 0.5s ease;
    overflow: hidden;
    .versionMenuHeader {
      width: 100%;
      height: 52px;
      padding: var(--mc-padding);
      font-weight: bold;
      background-color: var(--mc-gray-1);
      color: var(--mc-color-text-secondary);
      border-radius: var(--mc-border-radius) var(--mc-border-radius) 0px 0px;
      border: var(--mc-line-width) solid var(--mc-color-border);
      .versionMenuHeaderItem {
        overflow: hidden;
      }
    }
    .versionMenuContent {
      width: 100%;
      border-radius: 0px 0px calc(var(--mc-padding-xxs) * 3 / 2) calc(var(--mc-padding-xxs) * 3 / 2);
      border-left: var(--mc-line-width) solid var(--mc-color-border);
      border-right: var(--mc-line-width) solid var(--mc-color-border);
    }
    .versionMenuItem {
      width: 100%;
      padding: var(--mc-padding-xs) var(--mc-padding);
      border-bottom: var(--mc-line-width) solid var(--mc-color-border);
      cursor: pointer;
      position: relative;
      overflow: hidden;
      &:hover {
        background-color: var(--mc-color-fill-secondary);
      }
      &:last-child {
        border-radius: calc(var(--mc-padding-xxs) * 3 / 2);
      }
      .versionMenuItemSpan {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .versionMenuItemDescription {
        font-size: var(--mc-font-size-sm);
        color: var(--mc-color-text-secondary);
      }
    }
    .activeMenuItem::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: var(--mc-margin-xxs);
      height: 100%;
      background-color: var(--mc-coral-4);
    }
    .paginationContainer {
      overflow: hidden;
      height: var(--mc-font-height-lg);
    }
  }

  .collapseTool {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    background-color: #238636;
    width: 10px;
    height: 68px;
    cursor: pointer;
    font-size: var(--mc-font-size-xl);
    transition: all 0.5s ease;
    border-radius: calc(var(--mc-padding-xxs) * 3 / 2) 0px 0px calc(var(--mc-padding-xxs) * 3 / 2);
  }
}
