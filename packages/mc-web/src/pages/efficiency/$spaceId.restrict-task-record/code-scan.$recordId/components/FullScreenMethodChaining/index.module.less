.fullScreenMethodChaining {
  height: 800px;
  .canvas {
    position: relative;
    height: 100%;
  }
}
.moduleHeader {
  padding: var(--mc-padding-sm);
  border-bottom: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
  .returnBtn {
    cursor: pointer;
    &:hover {
      color: var(--mc-color-link-hover);
    }
  }
  .moduleHeaderTitle {
    padding-left: var(--mc-padding-xs);
    border-left: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
  }
  .headerCodeInfo {
    border-left: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
    padding-left: var(--mc-padding-xs);
    margin-left: var(--mc-padding-xs);
  }
  .moduleSelect {
    .moduleSelectDivider {
      width: 1px;
      height: 15px;
      background-color: var(--mc-color-border);
    }
    // :global {
    //   .mc-select-selector {
    //     background-color: transparent;
    //     border: none;
    //   }
    // }
  }
}
