import React, { useState } from 'react';
import { Flex, Input, Modal, Form, Button } from 'antd';
import { useRequest } from '@ali/mc-request';
import { createGateApproval } from '@ali/mc-services/GateApi';

interface AuditModalProps {
  open: boolean;
  gateCheckId?: number;
  success?: () => void;
  onChange?: (open: boolean) => void;
}

export default function AuditModal(props: AuditModalProps) {
  const { open, gateCheckId, success, onChange } = props;
  const [form] = Form.useForm();
  const { runAsync: requestCreateGateApproval, loading } = useRequest(createGateApproval);
  const [auditMsg, setAuditMsg] = useState('');

  const handleAuditModalOk = async () => {
    const values = await form.validateFields();
    form.setFieldsValue(values);
    const data = await requestCreateGateApproval({
      gateCheckId: gateCheckId ?? 0,
      reason: auditMsg,
      detailLink: window.location.href,
    });
    if (data) {
      success?.();
    }
    onChange?.(false);
  };

  const handleAuditModalCancel = () => {
    onChange?.(false);
    setAuditMsg('');
  };

  return (
    <Modal
      title="是否发起审批"
      open={open}
      onOk={handleAuditModalOk}
      onCancel={handleAuditModalCancel}
      footer={[
        <Button key="back" onClick={handleAuditModalCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleAuditModalOk}>
          提交
        </Button>,
      ]}
    >
      <Flex vertical>
        <Form form={form}>
          <Form.Item label="申请原因" name="reason" rules={[{ required: true }]}>
            <Flex>
              <Input
                placeholder="请输入"
                value={auditMsg}
                onChange={(e: any) => setAuditMsg(e?.currentTarget?.value)}
              />
            </Flex>
          </Form.Item>
        </Form>
      </Flex>
    </Modal>
  );
}
