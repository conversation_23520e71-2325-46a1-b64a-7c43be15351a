import React, { useEffect, useMemo } from 'react';
import { Flex, Row, Col, theme, Skeleton, Empty } from 'antd';

import StatusCard from '../InsightDeliveryEfficiency/StatusCard';

import { useRequest } from '@ali/mc-request';
import { getProcessDuration, GetProcessDurationParams } from '@ali/mc-services/Efficiency';
import { MetricComparativeData } from '@ali/mc-services';

type InsightProcessMetricListProps = {
  spaceId?: number;
  applicationId?: number;
  versionPlanId?: number;
};
export default function InsightProcessMetricList(props: InsightProcessMetricListProps) {
  const { token } = theme.useToken();
  const { spaceId, applicationId, versionPlanId } = props;

  const { data, run, loading } = useRequest<MetricComparativeData[], [GetProcessDurationParams]>(getProcessDuration);

  useEffect(() => {
    if (spaceId && applicationId && versionPlanId) {
      run({
        spaceId,
        appId: applicationId,
        versionPlanId: versionPlanId,
      });
    }
  }, [spaceId, applicationId, versionPlanId, run]);

  const [,/* count = 0 */ items = []] = useMemo(() => {
    // const _items = data?.sort((item) => {
    //   if (item.flag === 'DOWN') {
    //     return -1;
    //   }
    //   return 0;
    // });

    const _count = data?.filter((item) => item.flag === 'DOWN').length;

    return [_count, data];
  }, [data]);

  return (
    <Flex vertical gap={token.margin}>
      <span>
        平台全流程耗时
        {/* {count > 0 && (
          <>
            （<span style={{ color: token.colorError }}>{count}</span> 个待优化指标）
          </>
        )} */}
      </span>
      <Row gutter={[token.marginSM, token.marginSM]}>
        {loading ? (
          <Col span={6} style={{ height: '109px', overflowY: 'hidden' }}>
            <Skeleton active loading={loading} title={false} paragraph={{ rows: 3 }} />
          </Col>
        ) : items?.length ? (
          items?.map((item: MetricComparativeData) => {
            return (
              <Col span={6} key={item.identifier}>
                <StatusCard
                  spaceId={spaceId}
                  applicationId={applicationId}
                  versionPlanId={versionPlanId}
                  data={item}
                />
              </Col>
            );
          })
        ) : (
          <Col span={24} style={{ height: '109px' }}>
            <Empty imageStyle={{ height: '79px' }} description="暂无数据" />
          </Col>
        )}
      </Row>
    </Flex>
  );
}
