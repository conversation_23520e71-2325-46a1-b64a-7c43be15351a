import React, { useEffect } from 'react';
import { Link } from 'ice';
import { isEmpty } from 'lodash-es';
import { Flex, Tooltip, Skeleton, Space, Empty, theme } from 'antd';
import { Line } from '@ant-design/plots';
import { RightOutlined, QuestionCircleOutlined, CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import { User } from '@ali/mc-uikit';
import { useRequest } from '@ali/mc-request';
import { getRelation } from '@ali/mc-services/DependencyGraph';
import { getNumberWithUnit, transferToPercentage } from '@/util/number';
import { DYNAMIC_ANDROID_DOC_URL, DYNAMIC_IOS_DOC_URL, DeliveryEfficiency } from '@/constants/efficiency';
import { getFullPathWithContext } from '../../common/searchParams';
import { useGateRecordContext } from '../../components/GateRecordContext';
import styles from './index.module.less';
import EfficiencyStatusTag from '../EfficiencyStatusTag';

interface ModulesRightPanelProps {
  loading?: boolean;
  spaceId?: number;
  appId?: number;
  appVersion?: string;
  moduleId?: number;
}

export default function ModulesRightPanel(props: ModulesRightPanelProps) {
  const { loading, spaceId, appId, appVersion, moduleId } = props;
  const { recordData } = useGateRecordContext();
  const { token } = theme.useToken();
  const { runAsync: requestRelation, data: relationData, loading: relationLoading } = useRequest(getRelation);

  useEffect(() => {
    const initData = () => {
      if (appId && appVersion && moduleId) {
        requestRelation({ appId, appVersion, moduleId });
      }
    };
    initData();
  }, [appId, appVersion, moduleId, requestRelation]);

  const moduleSizeLineConfig = {
    height: 150,
    xField: 'appVersion',
    yField: 'moduleSize',
    autoFit: true,
    axis: {
      // eslint-disable-next-line id-length
      y: {
        labelFormatter: (size: number) => getNumberWithUnit(size),
      },
    },
    tooltip: {
      items: [
        { channel: 'x', name: '版本' },
        { channel: 'y', name: '包大小' },
      ],
    },
  };

  return (
    <div className={styles.rightPanel}>
      {loading || relationLoading ? (
        <Skeleton />
      ) : isEmpty(relationData) ? (
        <Empty />
      ) : (
        <div>
          <div>
            <div className={styles.rightPanelTitle}>基础信息</div>
            <Flex vertical gap={token.paddingXS}>
              <div>
                名称： <span className={styles.rightPanelTextHighlight}>{relationData?.name}</span>
              </div>
              <div>
                DepKey： <span className={styles.rightPanelTextHighlight}>{relationData?.depKey}</span>
              </div>
              <div>
                集成版本： <span className={styles.rightPanelTextHighlight}>{relationData?.version}</span>
              </div>
              <Space>
                负责人： <User empIds={relationData?.owner} showAvatar />
              </Space>
              <Space>
                <Link to={`/modules/${moduleId}/setting`} target="_blank" className={styles.rightPanelLink}>
                  模块详情
                  <RightOutlined />
                </Link>
                {relationData?.codeLibraryLink && (
                  <Link className={styles.rightPanelLink} to={relationData.codeLibraryLink} target="_blank">
                    代码仓库 <RightOutlined />
                  </Link>
                )}
              </Space>
            </Flex>
          </div>
          <div className={styles.rightPanelItem}>
            <div className={styles.rightPanelSubtitle}>模块统计</div>
            <Flex vertical gap={token.paddingXS}>
              <div>
                {relationData?.dependOnModuleAmount} 个被依赖方模块，{relationData?.dependOnModuleLowSpeedAmount}{' '}
                个慢速交付；
              </div>
              <div>
                {relationData?.dependByModuleAmount} 个依赖方模块，{relationData?.dependByModuleLowSpeedAmount}{' '}
                个慢速交付；
              </div>
            </Flex>
          </div>
          <div className={styles.rightPanelItem}>
            <Flex gap={token.paddingXS} align="center" className={styles.rightPanelSubtitle}>
              交付效率
              <EfficiencyStatusTag efficiency={(relationData?.speed as DeliveryEfficiency) ?? 'UNKNOWN'} />
            </Flex>
            <Flex vertical gap={token.paddingXS}>
              <Space size={token.paddingXXS}>
                <span>动态化能力</span>
                <span>：</span>
                {relationData?.isDynamic ? (
                  <span className={styles.rightPanelTextHighlight}>已具备</span>
                ) : (
                  <>
                    <span className={styles.rightPanelTextHighlight}>未具备</span>
                    <a
                      href={relationData?.platform === 'ANDROID' ? DYNAMIC_ANDROID_DOC_URL : DYNAMIC_IOS_DOC_URL}
                      target="_blank"
                      rel="noreferrer"
                      className={styles.rightPanelLink}
                    >
                      去接入
                      <RightOutlined />
                    </a>
                  </>
                )}
              </Space>
              <Space size={4}>
                <span>代码可优化</span>
                <span>：</span>
                {typeof relationData?.optimizeCount === 'number' && relationData?.optimizeCount > 0 ? (
                  <>
                    <span className={styles.rightPanelTextHighlight}>
                      {relationData?.optimizeCount} (可节省 {getNumberWithUnit(relationData?.optimizeSize)})
                    </span>
                    <Link
                      to={getFullPathWithContext({
                        path: `/efficiency/${spaceId}/optimization-details/${relationData?.messageId}/optimizable-code`,
                        context: recordData,
                      })}
                      className={styles.rightPanelLink}
                    >
                      去优化
                      <RightOutlined />
                    </Link>
                  </>
                ) : (
                  <span className={styles.rightPanelTextHighlight}>暂无可优化代码</span>
                )}
              </Space>
              <Space size={4}>
                <span>代码可下线</span>
                <span>：</span>
                {typeof relationData?.offlineCount === 'number' && relationData?.offlineCount > 0 ? (
                  <>
                    <span className={styles.rightPanelTextHighlight}>
                      {relationData?.offlineCount} (可节省 {getNumberWithUnit(relationData?.offlineSize)})
                    </span>
                    <Link
                      to={getFullPathWithContext({
                        path: `/efficiency/${spaceId}/optimization-details/${relationData?.messageId}/redundant-code`,
                        context: recordData,
                      })}
                      className={styles.rightPanelLink}
                    >
                      去下线
                      <RightOutlined />
                    </Link>
                  </>
                ) : (
                  <span className={styles.rightPanelTextHighlight}>暂无可下线代码</span>
                )}
              </Space>
            </Flex>
          </div>
          <div>
            <div className={styles.rightPanelSubtitle}>包大小信息</div>
            <Flex vertical gap={token.paddingXS}>
              <Flex gap={token.paddingXXS}>
                <span>包大小</span>
                <span>：</span>
                <span style={{ color: token.colorText }}>{getNumberWithUnit(relationData?.baseSize)}</span>
              </Flex>
              <Flex gap={token.paddingXXS}>
                <span>增量变化</span>
                <span>：</span>
                <span
                  style={{
                    color:
                      typeof relationData?.deltaSize === 'number' && relationData?.deltaSize < 0
                        ? token.colorSuccess
                        : token.colorError,
                  }}
                >
                  {typeof relationData?.deltaSize === 'number' && relationData?.deltaSize < 0 ? (
                    <CaretDownOutlined />
                  ) : (
                    <CaretUpOutlined />
                  )}
                  {`${transferToPercentage(relationData?.growthRate)}（${getNumberWithUnit(relationData?.deltaSize, true)}）`}
                </span>
                <Tooltip title="此处增量变化的对比基线指的是App客户端最新归档版本下该模块的包大小。">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Flex>
            </Flex>
            <div className={styles.rightPanelTitle}>包大小趋势变化</div>
            <Line data={relationData?.trend ?? []} {...moduleSizeLineConfig} />
          </div>
        </div>
      )}
    </div>
  );
}
