.efficiencyModules {
  .mainPanel {
    padding-left: var(--mc-padding-lg);
    padding-right: calc(var(--mc-padding-lg) * 2);
    position: relative;
  }
  .rightPanel {
    width: 434px;
    min-height: 100%;
    padding-left: var(--mc-padding-lg);
    border-left: 1px solid var(--mc-color-border);
    color: var(--mc-color-text-description);
    .rightPanelTitle {
      font-size: var(--mc-font-size-lg);
      font-weight: var(--mc-font-weight-strong);
      color: var(--mc-color-text);
      margin: var(--mc-margin) 0;
    }
    .rightPanelSubtitle {
      font-size: var(--mc-font-size-lg);
      color: var(--mc-color-text);
      margin: var(--mc-margin) 0;
    }
    .rightPanelLink {
      color: var(--mc-color-text-description);
      &:hover {
        color: var(--mc-color-primary);
      }
    }
    .rightPanelItem {
      padding-bottom: var(--mc-padding);
      border-bottom: 1px solid var(--mc-color-border);
    }
    .rightPanelTextHighlight {
      margin-left: calc(var(--mc-padding-xxs) * -1 / 2);
      color: var(--mc-color-text);
    }
  }
  .canvas {
    width: 100%;
    height: 700px;
    background-color: #f6f8fa;
    border-radius: var(--mc-margin-xs);
    border: 1px solid var(--mc-color-border);
    padding: var(--mc-padding-lg);
    margin-top: var(--mc-margin-lg);
    .canvasInput {
      margin-bottom: var(--mc-margin-lg);
      background-color: transparent;
    }
  }
}

.drawerContent {
  .drawerHeaderTitle {
    font-size: var(--mc-font-size-lg);
    font-weight: var(--mc-font-weight-strong);
  }
  .divider {
    margin-top: var(--mc-margin);
    margin-bottom: var(--mc-margin);
  }
  .drawerItem {
    font-size: var(--mc-font-size);
    .drawerTitle {
      color: var(--mc-color-text-description);
    }
  }
  .drawerLink {
    color: var(--mc-color-text-description);
    &:hover {
      color: var(--mc-color-primary);
    }
  }
  .methodDependencyItem {
    border: calc(var(--mc-padding-xxs) / 2) solid var(--mc-color-border);
    border-radius: var(--mc-padding);
    padding: var(--mc-padding-xs);
    .methodDependencyMethod {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
