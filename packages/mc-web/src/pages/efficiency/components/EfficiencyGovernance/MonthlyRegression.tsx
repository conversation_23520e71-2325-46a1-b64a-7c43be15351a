import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Divider, Flex, Skeleton, Timeline, theme } from 'antd';
import { StopOutlined, VersionsOutlined } from '@ali/mc-icons';
import { Link } from 'ice';
import { useRequest } from '@ali/mc-request';
import { MonthlyRegressionDetailVO, MonthlyRegressionVO } from '@ali/mc-services';
import {
  GetMonthlyRegressionDataParams,
  GetMonthlyRegressionDetailParams,
  getMonthlyRegressionData,
  getMonthlyRegressionDetail,
} from '@ali/mc-services/Efficiency';
import { keyBy, isEmpty } from 'lodash-es';
import { UnitMap } from '@/util/utils';
import styles from './index.module.less';
import Empty from './Empty';

const identifierMap: Record<string, string> = {
  REGRESSION: '业务回归',
  INTEGRATION: '集成',
  TESTING: '测试',
  AI_PACKAGE_SIZE_GATE: 'AI包大小卡口',
  CODE_REVIEW_MERGE: '代码评审合并',
  FIRST_BETA_EXPIRED_REGRESSION: '一灰发布的发布单',
  RELEASE_EXPIRED_REGRESSION: '正式发布',
  INCREMENT_RELEASE_INTEGRATION: '正式增发',
};

interface MonthlyRegressionProps {
  spaceId?: number;
  appId?: number;
  title?: string;
  month?: string;
}

export default function MonthlyRegression(props: MonthlyRegressionProps) {
  const { token } = theme.useToken();
  const { spaceId, appId, title, month } = props;

  const {
    runAsync: requestMonthlyRegressionData,
    data: monthlyRegressionData = {},
    loading,
  } = useRequest<
    MonthlyRegressionVO, [GetMonthlyRegressionDataParams]
  >(getMonthlyRegressionData);

  const {
    runAsync: requestMonthlyRegressionDetail,
    data: monthlyRegressionDetail = {},
    loading: detailLoading,
  } = useRequest<
    MonthlyRegressionDetailVO, [GetMonthlyRegressionDetailParams]
  >(getMonthlyRegressionDetail);

  useEffect(() => {
    if (appId && spaceId && month) {
      const params = {
        appId,
        spaceId,
        month: month,
      };
      Promise.all([
        requestMonthlyRegressionData(params),
        requestMonthlyRegressionDetail(params),
      ]).catch((err) => {
        console.error(err);
      });
    }
  }, [
    appId,
    spaceId,
    month,
    requestMonthlyRegressionData,
    requestMonthlyRegressionDetail,
  ]);

  const {
    averageBizRegressionDuration = {},
    versionPlanBizRegressionDurations = [],
    slowReq = {},
    versionPlanSlowReqs = [],
  } = monthlyRegressionData;

  // 平均业务回归累积耗时
  const averageBizRegressionDurationValue = averageBizRegressionDuration.value as any;
  // 平均业务回归累积耗时（小时）
  const averageBizRegressionDurationHour =
    averageBizRegressionDuration.unit === 'HOUR'
      ? averageBizRegressionDurationValue
      : averageBizRegressionDurationValue * 24;
  // 慢需求占比是否大于等于50%
  const slowReqPercent50 = (slowReq?.slowReqPercent && parseInt(slowReq?.slowReqPercent, 10) >= 50) || false;

  const { nodeMaxDurationCounts = {}, slowReqReasonPercents = {} } = monthlyRegressionDetail;

  if (isEmpty(monthlyRegressionData) && isEmpty(monthlyRegressionDetail)) {
    return <Empty title={title} />;
  }

  return (
    <Flex vertical gap={token.marginXS}>
      <Flex className={styles.title}>{title}</Flex>
      {(loading || detailLoading) ? (
        <Flex className={styles.card}>
          <Skeleton active />
        </Flex>
      ) : (
        <Flex vertical gap={token.margin} className={styles.card}>
          <Flex vertical gap={token.marginXXS}>
            <Flex align="center" className={styles.identifier}>
              <span className={styles.label}>平均业务回归累积耗时：</span>
              <Flex gap={token.marginXXS} className={styles.warningValue}>
                {averageBizRegressionDurationValue || '-'}
                {averageBizRegressionDuration.unit && UnitMap[averageBizRegressionDuration.unit]}
              </Flex>
            </Flex>
            <Flex className={styles.versionList}>
              {versionPlanBizRegressionDurations?.map((item) => {
                const metricDataMap = keyBy(item.metricDataList, 'identifier');
                return (
                  <Flex
                    vertical
                    gap={token.marginXXS / 2}
                    className={styles.versionItem}
                    key={item.versionPlanReleaseVersion}
                  >
                    <Flex
                      align="center"
                      gap={token.marginXXS}
                      className={styles.top}
                    >
                      <VersionsOutlined className={styles.itemIcon} />
                      <span className={styles.itemLabel}>v {item.versionPlanReleaseVersion}</span>
                    </Flex>
                    <Flex
                      align="center"
                      className={styles.bottom}
                    >
                      <Flex gap={token.marginXXS}>
                        <span>耗时</span>
                        <span className={styles.warningValue}>
                          {metricDataMap['biz_regression_duration']?.value as any || '-'}
                          {metricDataMap['biz_regression_duration']?.unit && UnitMap[metricDataMap['biz_regression_duration']?.unit]}
                        </span>
                      </Flex>
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          </Flex>
          <Flex vertical gap={token.marginXXS}>
            <Flex align="center" className={styles.identifier}>
              <span className={styles.label}>慢需求数：</span>
              <Flex align="center">
                <span className={styles.warningValue}>{slowReq?.slowReqCount || 0} 个</span>
                <Divider type="vertical" />
                <Flex>
                  占比 <span className={styles.value}>{slowReq?.slowReqPercent || '-'}</span>
                </Flex>
              </Flex>
            </Flex>
            <Flex className={styles.versionList}>
              {versionPlanSlowReqs?.map((item) => {
                return (
                  <Flex
                    vertical
                    gap={token.marginXXS / 2}
                    className={styles.versionItem}
                    key={item.versionPlanReleaseVersion}
                  >
                    <Flex
                      align="center"
                      gap={token.marginXXS}
                      className={styles.top}
                    >
                      <VersionsOutlined className={styles.itemIcon} />
                      <span className={styles.itemLabel}>v {item.versionPlanReleaseVersion}</span>
                    </Flex>
                    <Flex
                      align="center"
                      className={styles.bottom}
                    >
                      <span className={styles.warningValue}>{item?.slowReqCount} 个</span>
                      <Divider type="vertical" />
                      <Flex>
                        占比 <span className={styles.value}>{item?.slowReqPercent}</span>
                      </Flex>
                      {spaceId && appId && item?.versionPlanId && (
                        <Link
                          target="_blank"
                          to={`/efficiency/${spaceId}/insights?applicationId=${appId}&versionPlanId=${item?.versionPlanId}&tabKey=slow`}
                        >
                          <span className={styles.detail}>查看详情</span>
                        </Link>
                      )}
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          </Flex>
          <Alert
            showIcon
            type="error"
            icon={<StopOutlined />}
            message={
              averageBizRegressionDurationHour >= 72
                ? <Flex>
                  <span style={{ fontWeight: 500 }}>业务回归完成时间较晚{slowReqPercent50 ? '，产生慢需求较多' : ''}。</span>
                  <span>例如业务回归开始时间晚、回归时间久、识别和修复bug耗时长等，导致15:00未回归通过，造成版本发布延期。</span>
                </Flex>
                : <Flex>
                  <span style={{ fontWeight: 500 }}>业务回归完成时间略晚{slowReqPercent50 ? '，产生慢需求较多' : ''}。</span>
                  <span>由于15:00未全部回归通过，会影响版本计划的正常进展。</span>
                </Flex>
            }
          />
          <Flex vertical className={styles.timelineContent}>
            <Timeline
              items={[
                {
                  color: 'red',
                  children: (
                    <Flex vertical gap={4}>
                      <span className={styles.timelineTitle}>业务回归耗时概览</span>
                      {monthlyRegressionDetail?.versionPlanBizRegressionDurations?.map((item) => (
                        <Flex key={item?.versionPlanReleaseVersion} align="center">
                          <span className={styles.value}>{item?.versionPlanReleaseVersion}：</span>
                          {item?.metricDataList?.map((metricItem, index: number) => {
                            return (
                              <Flex key={metricItem.releaseId} align="center">
                                {index !== 0 && <Divider type="vertical" />}
                                <Flex gap={token.marginXXS}>
                                  <span>{metricItem?.stageDesc}</span>
                                  <span className={styles.warningValue}>
                                    {metricItem.value} {metricItem.unit && UnitMap[metricItem.unit]}
                                  </span>
                                </Flex>
                                {spaceId && appId && item?.versionPlanId && metricItem?.releaseId && (
                                  <Link
                                    target="_blank"
                                    to={`/efficiency/${spaceId}/metric?applicationId=${appId}&versionPlanId=${item?.versionPlanId}&releaseId=${metricItem?.releaseId}&identifier=biz_regression_duration&stage=${metricItem?.stage}&backUrl=governance&month=${month}`}
                                  >
                                    <span className={styles.detail}>查看详情</span>
                                  </Link>
                                )}
                              </Flex>
                            );
                          })}
                        </Flex>
                      ))}
                    </Flex>

                  ),
                },
                {
                  color: 'red',
                  children: (
                    <Flex vertical gap={4}>
                      <span className={styles.timelineTitle}>慢需求的形成原因分布</span>
                      {Object.entries(slowReqReasonPercents).map(([key, value]) => {
                        return (
                          <Flex key={key} align="center">
                            <Flex gap={token.marginXXS}>
                              <Flex>
                                {identifierMap[value?.reason]}
                                {value?.reason === 'INCREMENT_RELEASE_INTEGRATION' ? '的发布单内的新增集成：占比' : '的发布单第一轮回归发起5小时后，未回归通过或发生集成：占比'}
                              </Flex>
                              <span className={styles.warningValue}>
                                {value?.percent || '-'}
                              </span>
                            </Flex>
                            <Divider type="vertical" />
                            <Flex gap={token.marginXXS}>
                              <span>共</span>
                              <span className={styles.value}>
                                {value?.count || 0} 个
                              </span>
                            </Flex>
                          </Flex>
                        );
                      })}
                    </Flex>
                  ),
                },
                {
                  color: 'red',
                  children: (
                    <Flex vertical gap={4}>
                      <span className={styles.timelineTitle}>慢需求所参与的流程节点最大执行耗时{'>'}1小时的分布（从高到底排序）</span>
                      {Object.entries(nodeMaxDurationCounts)?.map(([key]) => {
                        return (
                          <Flex key={key} gap={token.marginXXS}>
                            <span>{identifierMap[key]}：共 </span>
                            <span className={styles.value}>
                              {nodeMaxDurationCounts[key] || '-'} 个
                            </span>
                          </Flex>
                        );
                      })}
                    </Flex>
                  ),
                },
              ]}
            />
            <Alert
              className={styles.alert}
              type="success"
              message={
                <Flex vertical gap={token.marginSM}>
                  <Flex className={styles.title}>优化建议</Flex>
                  <Flex vertical gap={token.marginXS}>
                    <Flex className={styles.item}>
                      <span className={styles.label}>1 提升回归效率：</span>
                      提交集成时指派正确的测试同学作为回归人，避免无人响应；评估回归人力资源是否充足，减少回归响应等待；接入自动化回归能力，减少回归耗时。
                    </Flex>
                    <Flex className={styles.item}>
                      <span className={styles.label}>2 慢需求收敛：</span>
                      优化项目包大小余额至余额充足，减少AI包大小卡口处理耗时；优化AICR卡口内命中扫描问题的模块代码，减少bug修复耗时；按时集成和回归，降低对版本延期的影响。
                    </Flex>
                  </Flex>
                </Flex>
              }
            />
          </Flex>
        </Flex>
      )}
    </Flex>
  );
}
