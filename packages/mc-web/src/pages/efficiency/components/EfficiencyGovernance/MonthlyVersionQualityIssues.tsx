import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Divider, Flex, Skeleton, Timeline, theme } from 'antd';
import { StopOutlined, VersionsOutlined } from '@ali/mc-icons';
import { Link } from 'ice';
import { VersionQualityDetailVO, VersionQualityIssueVO } from '@ali/mc-services';
import {
  GetMonthlyVersionQualityDetailParams,
  GetMonthlyVersionQualityIssuesParams,
  getMonthlyVersionQualityDetail,
  getMonthlyVersionQualityIssues,
} from '@ali/mc-services/Efficiency';
import { useRequest } from '@ali/mc-request';
import { keyBy, isEmpty } from 'lodash-es';
import { transferToPercentage } from '@/util/number';
import styles from './index.module.less';
import Empty from './Empty';

interface MonthlyVersionQualityIssuesProps {
  spaceId?: number;
  appId?: number;
  title?: string;
  month?: string;
}

export default function MonthlyVersionQualityIssues(props: MonthlyVersionQualityIssuesProps) {
  const { token } = theme.useToken();
  const { spaceId, appId, month, title } = props;

  const {
    runAsync: requestMonthlyVersionQualityIssues,
    data: monthlyVersionQualityIssues = {},
    loading,
  } = useRequest<
    VersionQualityIssueVO, [GetMonthlyVersionQualityIssuesParams]
  >(getMonthlyVersionQualityIssues);

  const {
    runAsync: requestMonthlyVersionQualityDetail,
    data: monthlyVersionQualityDetail = {},
    loading: detailLoading,
  } = useRequest<
    VersionQualityDetailVO, [GetMonthlyVersionQualityDetailParams]
  >(getMonthlyVersionQualityDetail);

  useEffect(() => {
    if (appId && spaceId && month) {
      const params = {
        appId,
        spaceId,
        month: month,
      };
      Promise.all([
        requestMonthlyVersionQualityIssues(params),
        requestMonthlyVersionQualityDetail(params),
      ]).catch((err) => {
        console.error(err);
      });
    }
  }, [
    appId,
    spaceId,
    month,
    requestMonthlyVersionQualityIssues,
    requestMonthlyVersionQualityDetail,
  ]);

  const {
    blockedIssueMetrics = [],
    versionPlanBlockedIssueMetrics = [],
    incrementBlockedIssueMetrics = [],
    versionPlanIncrementBlockedIssueMetrics = [],
  } = monthlyVersionQualityIssues;

  const blockedIssueMetricsMap = keyBy(blockedIssueMetrics, 'identifier');
  const incrementBlockedIssueMetricsMap = keyBy(incrementBlockedIssueMetrics, 'identifier');
  // 阻塞性问题占比
  const blockedIssuePercent = blockedIssueMetricsMap['blocked_issue_percent']?.value as any;

  const { qualityRecordVOList = [] } = monthlyVersionQualityDetail;

  if (isEmpty(monthlyVersionQualityIssues) && isEmpty(monthlyVersionQualityDetail)) {
    return <Empty title={title} />;
  }

  return (
    <Flex vertical gap={token.marginXS}>
      <Flex className={styles.title}>{title}</Flex>
      {(loading || detailLoading) ? (
        <Flex className={styles.card}>
          <Skeleton active />
        </Flex>
      ) : (
        <Flex vertical gap={token.margin} className={styles.card}>
          <Flex vertical gap={token.marginXXS}>
            <Flex align="center" className={styles.identifier}>
              <span className={styles.label}>阻塞性问题占比：</span>
              <Flex align="center">
                <span className={styles.warningValue}>{blockedIssueMetricsMap['blocked_issue_percent']?.value as any || '-'}</span>
                <Divider type="vertical" />
                <Flex>共<span className={styles.value}>{blockedIssueMetricsMap['blocked_issue_count']?.value as any || 0}个</span></Flex>
              </Flex>
            </Flex>
            <Flex className={styles.versionList}>
              {versionPlanBlockedIssueMetrics?.map((item) => {
                const metricDataMap = keyBy(item.metricDataList, 'identifier');
                return (
                  <Flex
                    vertical
                    gap={token.marginXXS / 2}
                    className={styles.versionItem}
                    key={item.versionPlanReleaseVersion}
                  >
                    <Flex
                      align="center"
                      gap={token.marginXXS}
                      className={styles.top}
                    >
                      <VersionsOutlined className={styles.itemIcon} />
                      <span className={styles.itemLabel}>v {item.versionPlanReleaseVersion}</span>
                    </Flex>
                    <Flex
                      align="center"
                      className={styles.bottom}
                    >
                      <span className={styles.warningValue}>{metricDataMap['blocked_issue_percent']?.value as any}</span>
                      <Divider type="vertical" />
                      <Flex>共<span className={styles.value}>{metricDataMap['blocked_issue_count']?.value as any}个</span></Flex>
                      {item.integrateAreaId && (
                        <Link
                          target="_blank"
                          to={`/releases/${item.integrateAreaId}/issues?status=ALL`}
                        >
                          <span className={styles.detail}>查看详情</span>
                        </Link>
                      )}
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          </Flex>
          <Flex vertical gap={token.marginXXS}>
            <Flex align="center" className={styles.identifier}>
              <span className={styles.label}>增发版本发现的阻塞性问题数：</span>
              <Flex align="center">
                <span className={styles.warningValue}>{incrementBlockedIssueMetricsMap['increment_blocked_issue_count']?.value as any || 0}个</span>
                <Divider type="vertical" />
                <Flex>
                  灰度增发
                  <span className={styles.value}>{incrementBlockedIssueMetricsMap['increment_beta_blocked_issue_count']?.value as any || 0}个</span>
                </Flex>
                <Divider type="vertical" />
                <Flex>
                  正式增发
                  <span className={styles.value}>{incrementBlockedIssueMetricsMap['increment_release_blocked_issue_count']?.value as any || 0}个</span>
                </Flex>
              </Flex>
            </Flex>
            <Flex className={styles.versionList}>
              {versionPlanIncrementBlockedIssueMetrics?.map((item) => {
                const metricDataMap = keyBy(item.metricDataList, 'identifier');
                return (
                  <Flex
                    vertical
                    gap={token.marginXXS / 2}
                    className={styles.versionItem}
                    key={item.versionPlanReleaseVersion}
                  >
                    <Flex
                      align="center"
                      gap={token.marginXXS}
                      className={styles.top}
                    >
                      <VersionsOutlined className={styles.itemIcon} />
                      <span className={styles.itemLabel}>v {item.versionPlanReleaseVersion}</span>
                    </Flex>
                    <Flex
                      align="center"
                      className={styles.bottom}
                    >
                      <span className={styles.warningValue}>{metricDataMap['increment_blocked_issue_count']?.value as any}个</span>
                      <Divider type="vertical" />
                      <Flex>
                        灰度增发
                        <span className={styles.value}>{metricDataMap['increment_beta_blocked_issue_count']?.value as any}个</span>
                      </Flex>
                      <Divider type="vertical" />
                      <Flex>
                        正式增发
                        <span className={styles.value}>{metricDataMap['increment_release_blocked_issue_count']?.value as any}个</span>
                      </Flex>
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          </Flex>
          <Alert
            showIcon
            type="error"
            icon={<StopOutlined />}
            message={<Flex>
              <span style={{ fontWeight: 500 }}>阻塞性问题{blockedIssuePercent >= 30 ? '较多' : '略多'}。</span>
              <span>阻塞性问题会引起紧急集成，又可能需要通过增发版本进行修复，造成版本计划延期。</span>
            </Flex>}
          />
          <Flex vertical className={styles.timelineContent}>
            {qualityRecordVOList?.length > 0 && (
              <Timeline
                items={[
                  {
                    color: 'red',
                    children: (
                      <Flex vertical gap={4}>
                        <span className={styles.timelineTitle}>代码质量</span>
                        {qualityRecordVOList?.map((item) => {
                          return (
                            <Flex key={item?.id} align="center">
                              <span className={styles.value}>{item?.appVersion}：</span>
                              <Flex gap={token.marginXXS}>
                                <span>（命中问题数）第</span>
                                <span className={styles.warningValue}>{item?.teamRank}</span>
                                <span>名</span>
                              </Flex>
                              <Divider type="vertical" />
                              <Flex gap={token.marginXXS}>
                                <span> 处于前</span>
                                <span className={styles.value}>
                                  {item?.teamRank && item?.teamCount &&
                                    transferToPercentage(item?.teamRank / item?.teamCount, false, 0)}
                                </span>
                              </Flex>
                              <Divider type="vertical" />
                              <Flex gap={token.marginXXS}>
                                <span>高危代码质量问题命中</span>
                                <span className={styles.value}>{item?.criticalIssuesCount} 次</span>
                              </Flex>
                              <Divider type="vertical" />
                              <Flex gap={token.marginXXS}>
                                <span>架构代码规约问题命中</span>
                                <span className={styles.value}>{item?.architecturalStandardsCount} 次</span>
                              </Flex>
                              <Divider type="vertical" />
                              <Flex gap={token.marginXXS}>
                                <span>基础代码质量问题命中</span>
                                <span className={styles.value}>{item?.basicIssuesCount} 次</span>
                              </Flex>
                              {spaceId && appId && item?.appVersion && (
                                <Link
                                  target="_blank"
                                  to={`/efficiency/${spaceId}/code-quality?appId=${appId}&appVersion=${item?.appVersion}`}
                                >
                                  <span className={styles.detail}>查看详情</span>
                                </Link>
                              )}
                            </Flex>
                          );
                        })}
                      </Flex>
                    ),
                  },
                ]}
              />
            )}
            <Alert
              className={styles.alert}
              type="success"
              message={
                <Flex vertical gap={token.marginSM}>
                  <Flex className={styles.title}>优化建议</Flex>
                  <Flex vertical gap={token.marginXS}>
                    <Flex className={styles.item}>
                      <span className={styles.label}>1 阻塞性问题调整：</span>
                      若识别出阻塞性问题并不会阻塞版本向下一个环节推进，可将问题分类改为[非阻塞性]。
                    </Flex>
                    <Flex className={styles.item}>
                      <span className={styles.label}>2 提升测试和回归质量：</span>
                      确保测试和回归用例的场景覆盖度，接入AI或自动化测试和回归能力，提高研发期测试和回归期的问题发现率。
                    </Flex>
                    {qualityRecordVOList?.length > 0 && (
                      <Flex className={styles.item}>
                        <span className={styles.label}>3 提升代码质量：</span>
                        根据模块命中代码检查规则情况，优化模块代码，提升代码质量。
                      </Flex>
                    )}
                  </Flex>
                </Flex>
              }
            />
          </Flex>
        </Flex>
      )}
    </Flex>
  );
}
