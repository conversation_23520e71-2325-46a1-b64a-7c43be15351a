.methodChainingNode {
  border-radius: var(--mc-border-radius);
  width: 364px;
  min-height: 100%;
  border: var(--mc-line-width-bold) var(--mc-line-type) var(--mc-color-border);
  background-color: var(--mc-color-bg-layout);
  .nodeHeader {
    padding: var(--mc-padding-sm);
    font-weight: var(--mc-font-weight-strong);
    .nodeHeaderTitle {
      overflow: hidden;
      .nodeHeaderTitleItem {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    &:is(:last-child) {
      border-bottom: none !important;
    }
  }
  .nodeContent {
    padding: var(--mc-padding-xs);
    font-size: var(--mc-font-size-sm);
    .nodeContentDesc {
      color: var(--mc-color-text-description);
    }
    .nodeContentRules {
      color: var(--mc-color-text-description);
      line-height: var(--mc-line-height-sm);
      .nodeContentRulesRed {
        color: var(--mc-color-error);
        text-decoration: underline;
        cursor: pointer;
        &:hover {
          color: var(--mc-color-primary);
        }
      }
    }
    .nodeContentLink {
      margin-left: auto;
      color: var(--mc-color-text-description);
      &:hover {
        color: var(--mc-color-primary);
      }
      span:last-child {
        margin-left: calc(var(--mc-margin-xxs) / 2);
      }
    }
  }
}

.methodChainingModuleNode {
  width: 364px;
  min-height: 100%;
  border: var(--mc-line-width-bold) dashed var(--mc-color-border);
  border-radius: var(--mc-border-radius);
  padding: var(--mc-padding-sm);
  // 特殊定制样式
  background-color: #fef3e7;
  cursor: pointer;
  .moduleNodeHeader {
    width: 100%;
    color: var(--mc-color-text-description);
    font-weight: var(--mc-font-weight-strong);
    .moduleNodeText {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.methodChainingGraph {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .miniMap {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: var(--mc-z-index-popup-base);
  }
  .loading {
    z-index: var(--mc-z-index-popup-base);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, 50%);
  }
  .empty {
    z-index: var(--mc-z-index-popup-base);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  :global {
    .x6-graph-scroller.x6-graph-scroller-paged .x6-graph {
      box-shadow: none;
    }
  }
}

.canvasLegend {
  font-size: var(--mc-font-size-sm);
  color: var(--mc-color-text-description);
  margin: var(--mc-padding-xs) var(--mc-margin);
  z-index: var(--mc-z-index-popup-base);
  .legendSquare {
    width: 16px;
    height: 12px;
    box-sizing: border-box;
  }
  .legendCube {
    width: 16px;
    height: 12px;
    // 特殊定制样式
    background: rgba(250, 140, 22, 0.1);
    border: var(--mc-line-width) dashed var(--mc-grey-3);
  }
  .legendLine {
    width: 16px;
    border: var(--mc-line-width-bold) var(--mc-line-type) var(--mc-gray-6);
  }
  .legendDashedLine {
    width: 16px;
    border: var(--mc-line-width-bold) dashed var(--mc-grey-6);
  }
}

.canvasFilterGroupHeader {
  margin-top: var(--mc-margin-sm);
  margin-left: var(--mc-margin);
}

.canvasFilterGroup {
  width: fit-content;
  color: var(--mc-color-text-description);
  .canvasFilterGroupItem {
    padding: var(--mc-padding-xs);
    cursor: pointer;
    &:hover {
      background-color: var(--mc-color-bg-text-hover);
    }
    &:not(:last-child) {
      padding-right: var(--mc-padding-xs);
      border-right: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
    }
  }
  :global {
    .mc-radio-button-wrapper.mc-radio-button-wrapper-disabled:hover {
      color: var(--mc-color-text-disabled);
      background-color: var(--mc-color-bg-container-disabled);
    }
  }
}

.multipleMethodsNodeContainer {
  position: relative;
  width: 100%;
  height: 100%;
  .multipleMethodsNode {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  :global {
    // Badge 组件
    .mc-badge .mc-badge-count {
      color: var(--mc-color-text);
      background-color: var(--mc-color-bg-container-disabled);
      .mc-scroll-number-only-unit {
        color: var(--mc-color-text);
      }
    }
  }
}
