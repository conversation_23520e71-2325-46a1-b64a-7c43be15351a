import React from 'react';
import { Flex, theme } from 'antd';
import styles from './index.module.less';

const LegendSquare = ({ color }: { color: string }) => {
  const { token } = theme.useToken();

  return <span className={styles.legendSquare} style={{ border: `${token.lineWidthBold}px ${token.lineType} ${color}` }} />;
};

export default function CanvasLegend() {
  const { token } = theme.useToken();

  const data = [
    {
      label: '外部依赖模块',
      legend: <div className={styles.legendCube} />,
    },
    {
      label: '非本次变更方法',
      legend: <LegendSquare color="#D0D7DE" />,
    },
    {
      label: '新增方法',
      legend: <LegendSquare color="rgba(74, 194, 107, 0.4)" />,
    },
    {
      label: '修改方法',
      legend: <LegendSquare color="rgba(22, 119, 255, 0.6)" />,
    },
    {
      label: '删除方法',
      legend: <LegendSquare color="rgba(255, 129, 130, 0.4)" />,
    },
    {
      label: '直接调用',
      legend: <div className={styles.legendLine} />,
    },
    {
      label: '间接调用',
      legend: <div className={styles.legendDashedLine} />,
    },
  ];

  return (
    <Flex className={styles.canvasLegend} gap={token.margin}>
      {data.map((item) => (
        <Flex align="center" key={item.label} gap={token.marginXXS}>
          {item.legend}
          <span className={styles.legendLabel}>{item.label}</span>
        </Flex>
      ))}
    </Flex>
  );
}
