import React, { useEffect, useState, useMemo } from 'react';
import { isEmpty } from 'lodash-es';
import { Empty, Skeleton, theme } from 'antd';
import type { NodeData, ComboData, EdgeData } from '@antv/g6';
import { useRequest } from '@ali/mc-request';
import {
  listNodes,
  listEdges,
  listPath,
  type ListEdgesParams,
  type ListPathParams,
} from '@ali/mc-services/DependencyGraph';
import { reportTime } from '../../common/eventReporter';
import CardWithSelectors from './CardWithSelector';
import Grid from '../AntVCharts/Grid';

interface GridCardProps {
  spaceId?: number;
  appId?: number;
  appVersion?: string;
  searchModuleName?: string;
}

type DependencyEdgeType = 'CYCLIC' | 'REVERSED' | 'ALL';

const moduleDependenciesItems: Array<{
  key: DependencyEdgeType;
  label: string;
}> = [
  {
    key: 'CYCLIC',
    label: '循环依赖',
  },
  {
    key: 'REVERSED',
    label: '反向依赖',
  },
];

const NODE_CONFIG_MAP: Record<string, any> = {
  BUSINESS_COMPONENT: {
    bgColor: '#1A7F37',
    borderColor: '#116329',
  },
  CONTAINER: {
    bgColor: '#9A6700',
    borderColor: '#7D4E00',
  },
  MIDDLEWARE: {
    bgColor: '#0969DA',
    borderColor: '#0550AE',
  },
  FOUNDATION_LIBRARY: {
    bgColor: '#59636E',
    borderColor: '#454C54',
  },
};

const COMBOS_LIST = ['BUSINESS_COMPONENT', 'CONTAINER', 'MIDDLEWARE', 'FOUNDATION_LIBRARY'];

const COMBOS_CONFIG_MAP: Record<string, ComboData> = {
  BUSINESS_COMPONENT: {
    id: 'BUSINESS_COMPONENT',
    label: '业务组件',
    style: {
      fill: '#DAFBE1',
    },
    data: {
      bgColor: '#1A7F37',
      borderColor: '#116329',
    },
  },
  CONTAINER: {
    id: 'CONTAINER',
    label: '容器',
    style: {
      fill: '#FFF8C5',
    },
    data: {
      bgColor: '#9A6700',
      borderColor: '#7D4E00',
    },
  },
  MIDDLEWARE: {
    id: 'MIDDLEWARE',
    label: '中间件',
    style: {
      fill: '#DDF4FF',
    },
    data: {
      bgColor: '#0969DA',
      borderColor: '#0550AE',
    },
  },
  FOUNDATION_LIBRARY: {
    id: 'FOUNDATION_LIBRARY',
    label: '基础库',
    style: {
      fill: '#EFF2F5',
    },
    data: {
      bgColor: '#59636E',
      borderColor: '#454C54',
    },
  },
};

type EdgesMap = Record<DependencyEdgeType, {
  count: number;
  edges: EdgeData[];
}>;

export default function GridCard(props: GridCardProps) {
  const { spaceId, appId, appVersion, searchModuleName = '' } = props;
  const { token } = theme.useToken();
  const { loading: listNodesLoading, runAsync: requestNodes } = useRequest(listNodes);
  const { runAsync: requestPath } = useRequest(listPath);
  const [gridActiveKey, setGridActiveKey] = useState<DependencyEdgeType | null>(null);
  const [nodes, setNodes] = useState<NodeData[]>([]);
  const [combos, setCombos] = useState<ComboData[]>([]);
  const [edgesMap, setEdgesMap] = useState<EdgesMap>();
  const [listEdgesLoading, setListEdgesLoading] = useState<boolean>(false);

  const highlightKeyListBySearch = useMemo(() => {
    const result: string[] = [];
    if (!searchModuleName) return result;

    nodes?.forEach((node: any) => {
      const { label, id } = node;
      if (label?.includes(searchModuleName ?? '')) {
        result.push(id);
      }
    });
    return result;
  }, [searchModuleName, nodes]);

  useEffect(() => {
    const getAllEdges = async () => {
      if (!spaceId || !appId || !appVersion) return;
      setListEdgesLoading(true);
      const queryEdgeTypeList: DependencyEdgeType[] = ['CYCLIC', 'REVERSED'];
      const params: ListEdgesParams | ListPathParams = {
        type: 'SPACE',
        spaceId,
        appId,
        appVersion,
      };
      const data = await Promise.all(
        queryEdgeTypeList.map((edgeType) => listEdges({
          ...params,
          edgeType,
        })),
      );
      setListEdgesLoading(false);
      let newEdgesMap: any = {};
      for (let index = 0; index < data.length; index++) {
        const edgeOriginalData = data[index];
        const type = queryEdgeTypeList[index];
        let count = edgeOriginalData?.length ?? 0;
        if (type === 'CYCLIC') {
          const cyclicList = await requestPath({ ...params, pathType: 'CYCLIC' });
          count = cyclicList?.length;
        }
        newEdgesMap = {
          ...newEdgesMap,
          [type]: {
            count,
            edges: edgeOriginalData?.map((item) => ({
              id: `${item.srcDepKey}-${item.dstDepKey}`,
              source: item.srcDepKey ?? '',
              target: item.dstDepKey ?? '',
            })) ?? [],
          },
        };
      }
      setEdgesMap(newEdgesMap);
    };

    const getNodes = async () => {
      if (!spaceId || !appId || !appVersion) return;
      const timeBeforeRequest = Date.now();
      const originalNodes = await requestNodes({ type: 'SPACE', spaceId, appId, appVersion });
      reportTime({
        eventName: 'GridNodesRequestTime',
        timeBefore: timeBeforeRequest,
        timeAfter: Date.now(),
        extraData: originalNodes,
      });
      // 排序
      originalNodes.sort(
        (curr, next) => COMBOS_LIST.indexOf(curr?.hierarchy ?? '') - COMBOS_LIST.indexOf(next?.hierarchy ?? ''),
      );

      const newCombos: Array<any> = [];
      const newNodes = originalNodes?.map((item) => {
        const config = item.hierarchy && COMBOS_CONFIG_MAP[item.hierarchy];
        if (config && newCombos.findIndex((combo) => combo.id === item.hierarchy) === -1) {
          newCombos.push(config);
        }

        return {
          id: item.depKey,
          label: item.moduleName,
          combo: item.hierarchy,
          data: {
            ...item,
            ...(item.hierarchy ? NODE_CONFIG_MAP[item.hierarchy] : null),
          },
        };
      }) as NodeData[];
      setCombos(newCombos);
      setNodes(newNodes);
    };

    getAllEdges();
    getNodes();
  }, [requestNodes, requestPath, spaceId, appId, appVersion]);

  return (
    <CardWithSelectors
      loading={listNodesLoading || listEdgesLoading}
      items={moduleDependenciesItems.map((item) => ({
        ...item,
        label: listEdgesLoading ? item.label : `${item.label}(${edgesMap?.[item.key]?.count ?? 0})`,
      }))}
      selectedKey={gridActiveKey}
      onChange={(activeKey) => {
        setGridActiveKey(activeKey);
      }}
    >
      {listNodesLoading ? (
        <Skeleton style={{ padding: token.padding }} />
      ) : isEmpty(nodes) ? (
        <Empty style={{ margin: token.margin }} />
      ) : (
        <Grid
          nodes={nodes}
          combos={combos}
          edges={edgesMap?.[gridActiveKey as DependencyEdgeType]?.edges ?? []}
          highlightKeyList={highlightKeyListBySearch}
          gridData={{
            spaceId,
            appId,
            appVersion,
          }}
        />
      )}
    </CardWithSelectors>
  );
}
