.statisticCard {
  width: 396px;
  height: fit-content;

  .normalLink {
    &:not(:hover) {
      color: var(--mc-color-text-label);
    }
  }

  .cardHeader {
    font-size: var(--mc-font-size-lg);
    padding: var(--mc-padding);
  }

  .cardBody {
    padding: 0 var(--mc-padding) var(--mc-padding);
  }

  .cardDivider {
    height: 1px;
    background-color: var(--mc-color-border);
    margin: 0 calc(0px - var(--mc-margin));
  }

  .process {
    color: var(--mc-color-text-secondary);

    .title {
      font-weight: 600;
      font-size: var(--mc-font-size-lg);
      line-height: var(--mc-line-height-lg);
    }

    .pending {
      color: var(--mc-color-text);
      font-size: var(--mc-font-size-heading-3);
    }

    .subTitle {
      font-weight: 500;
      font-size: var(--mc-font-size-sm);
    }
  }

  .detailItem {
    font-size: var(--mc-font-size-sm);

    .numerical {
      margin-top: var(--mc-margin-xxs);
      font-size: var(--mc-font-size-heading-5);
      line-height: var(--mc-line-height-heading-5);
      white-space: nowrap;

      :not(.divider) {
        font-weight: 600;
      }
    }

    .trend {
      font-size: var(--mc-font-size-sm);
    }

    :global {
      .mc-statistic-content {
        font-size: var(--mc-font-size-sm);
      }
    }
  }

  ul.detailList {
    font-size: var(--mc-font-size-sm);
    margin: 0;
    padding-inline-start: 0;
    gap: var(--mc-padding);

    li {
      list-style: none;
      color: var(--mc-color-text-secondary);
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-block-end: var(--mc-margin-xs);
    }
  }
}

.itemTitleText {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.metricDataList {
  min-width: 222px;
  border-left: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
  padding-left: var(--mc-padding);
}

.drawerCard {
  .cardRight {
    border-left: 1px solid var(--mc-color-border);
    padding-left: var(--mc-padding);
  }

  .cardLeft,
  .cardRight {
    flex: 1;
    color: var(--mc-color-text-secondary);

    .label {
      font-weight: 600;
      color: var(--mc-color-text);
    }

    .list {
      color: var(--mc-color-text-secondary);
    }
  }

  :global {
    .mc-card .mc-card-body {
      padding: 10px var(--mc-padding);
    }

    .mc-alert-message {
      color: var(--mc-color-success);
    }
  }
}
