import React, { useEffect, useMemo, useState } from 'react';
import { Flex, Radio, theme, Tooltip } from 'antd';
import {
  GetRegressionDurationTrendParams,
  GetRegressionResponseDurationTrendsParams,
  getRegressionDurationTrend,
  getRegressionResponseDurationTrends,
  getRegressionPersonDayData,
  GetRegressionPersonDayDataParams,
  getReqRegressionMetrics,
  GetReqRegressionMetricsParams,
} from '@ali/mc-services/Efficiency';
import { useRequest } from '@ali/mc-request';
import { MetricComparativeData, ReqRegressionMetricVO, calculateInterval } from '@ali/mc-services';
import TrendCard from './components/TrendCard';
import { ChevronRightOutlined, IterationsOutlined, LinkExternalOutlined, PositioningOutlined, QuestionOutlined, SortDescOutlined } from '@ali/mc-icons';
import { keyBy } from 'lodash-es';
import styles from './index.module.less';
import RegressionDurationTrend from './components/RegressionDurationTrend';
import { CheckboxGroupProps } from 'antd/es/checkbox';
import { DataEntryList, ListItem, StatusTag, User } from '@ali/mc-uikit';
import dayjs from 'dayjs';
import { Link } from 'ice';
import { getStatusTagColor } from '../../common/metric';

const unitMap: Record<string, string> = {
  HOUR: '小时',
  MINUTE: '分钟',
  DAY: '天',
  PERSON_DAY: '人日',
};

const options: CheckboxGroupProps<string>['options'] = [
  { label: '回归耗时趋势', value: 'trend' },
  { label: '回归明细', value: 'detail' },
];

interface BizRegressionDurationProps {
  spaceId?: number;
  applicationId?: number;
  versionPlanId?: number;
  releaseId?: string;
  stage?: string;
  backUrl?: string;
}

const BizRegressionDuration = (props: BizRegressionDurationProps) => {
  const { token } = theme.useToken();
  const { spaceId, applicationId, versionPlanId, releaseId, stage } = props;
  const [tab, setTab] = useState<'trend' | 'detail'>('trend');
  const [sortType, setSortType] = useState<'time' | 'timeOut'>('timeOut');

  const {
    runAsync: requestRegressionDurationTrend,
    data: regressionDurationTrend = {},
  } = useRequest<
    MetricComparativeData, [GetRegressionDurationTrendParams]
  >(getRegressionDurationTrend);

  const {
    runAsync: requestRegressionResponseDurationTrends,
    data: durationTrends = [],
  } = useRequest<
    MetricComparativeData[], [GetRegressionResponseDurationTrendsParams]
  >(getRegressionResponseDurationTrends);

  const {
    runAsync: requestRegressionPersonDayData,
    data: personDayData = [],
  } = useRequest<
    MetricComparativeData[], [GetRegressionPersonDayDataParams]
  >(getRegressionPersonDayData);

  const {
    runAsync: requestReqRegressionMetrics,
    data: reqRegressionMetrics = [],
    loading: reqRegressionMetricsLoading,
  } = useRequest<
    ReqRegressionMetricVO[], [GetReqRegressionMetricsParams]
  >(getReqRegressionMetrics);

  useEffect(() => {
    if (spaceId && applicationId && versionPlanId && releaseId && stage) {
      const params = {
        spaceId,
        appId: applicationId,
        versionPlanId: versionPlanId,
        releaseId: Number(releaseId),
        stage: stage,
      };
      Promise.all([
        requestRegressionDurationTrend({
          ...params,
        }),
        requestRegressionResponseDurationTrends({
          ...params,
        }),
        requestRegressionPersonDayData({
          ...params,
        }),
        requestReqRegressionMetrics({
          ...params,
        }),
      ]);
    }
  }, [
    spaceId, applicationId, versionPlanId, releaseId, stage,
    requestRegressionDurationTrend,
    requestRegressionResponseDurationTrends,
    requestRegressionPersonDayData,
    requestReqRegressionMetrics,
  ]);

  const durationTrendsMap = keyBy(durationTrends, 'identifier');
  const personDayDataMap = keyBy(personDayData, 'identifier');
  const p50Duration = durationTrendsMap['regression_response_p50_duration']; // 响应耗时-P50
  const p90Duration = durationTrendsMap['regression_response_p90_duration']; // 响应耗时-P90
  const firstP50Duration = durationTrendsMap['first_regression_response_p50_duration']; // 首次响应耗时-P50
  const firstP90Duration = durationTrendsMap['first_regression_response_p90_duration']; // 首次响应耗时-P90

  const afterP50Duration = personDayDataMap['regression_after_response_p50_duration']; // 响应后回归耗时-P50
  const afterP90Duration = personDayDataMap['regression_after_response_p90_duration']; // 响应后回归耗时-P90
  const bizPersonDay = personDayDataMap['biz_regression_person_day']; // 业务回归人力

  const p50TrendDetail = p50Duration?.trendData?.trendDetail?.map((item) => { return { ...item, type: 'P50' }; }) || [];
  const p90TrendDetail = p90Duration?.trendData?.trendDetail?.map((item) => { return { ...item, type: 'P90' }; }) || [];
  const afterP50Detail = afterP50Duration?.trendData?.trendDetail?.map((item) => { return { ...item, type: 'P50' }; }) || [];
  const afterP90Detail = afterP90Duration?.trendData?.trendDetail?.map((item) => { return { ...item, type: 'P90' }; }) || [];

  const sortReqRegressionMetrics = useMemo(() => {
    if (sortType === 'time') {
      return reqRegressionMetrics?.sort((a, b) => {
        return (b.regressionEndTime - b.regressionStartTime) - (a.regressionEndTime - a.regressionStartTime);
      });
    } else if (sortType === 'timeOut') {
      return reqRegressionMetrics?.sort((a, b) => {
        return (Number(b.regressionDurationExceedHour)) - Number(a.regressionDurationExceedHour);
      });
    }
    return reqRegressionMetrics;
  }, [reqRegressionMetrics, sortType]);

  const renderItem = (item: ReqRegressionMetricVO, index: number) => {
    const {
      reqId,
      aoneRequest,
      alterSheetName,
      alterSheetId,
      endRegressionUser,
      regressionStartTime,
      regressionEndTime,
      regressionRound,
      regressionDurationExceedHour,
    } = item;
    const { reqName, detailUrl, status, statusStage } = aoneRequest || {};
    const isSlowReq = regressionDurationExceedHour && Number(regressionDurationExceedHour) > 0;
    return (
      <ListItem
        key={index}
        actions={isSlowReq ? [
          <Link
            key={reqId}
            target="_blank"
            to={`/efficiency/${spaceId}/requirements/${reqId}?applicationId=${applicationId}&versionPlanId=${versionPlanId}`}
          >
            <Flex style={{ color: token.colorTextSecondary }} gap={token.marginXXS}>
              <span>慢需求详情</span>
              <ChevronRightOutlined />
            </Flex>
          </Link>,
        ] : undefined}
        title={
          <Flex gap={token.marginXS} align="center">
            <Tooltip title={reqName}>
              <span className={styles.reqName}>{reqName}</span>
            </Tooltip>
            {detailUrl && (
              <Link key="slowReqDetail" target="_blank" to={detailUrl}>
                <LinkExternalOutlined
                  style={{ fontSize: token.fontSizeLG }}
                  onClick={() => {
                    window.open(detailUrl, '_blank', 'noopener noreferrer');
                  }}
                />
              </Link>
            )}
            {statusStage && status && (
              <StatusTag color={getStatusTagColor(statusStage)}>{status}</StatusTag>
            )}
            {isSlowReq && (
              <StatusTag bordered={false} color="warning">慢需求</StatusTag>
            )}
          </Flex>
        }
      >
        <Flex vertical gap={token.marginXXS}>
          <Flex gap={token.margin}>
            <Flex gap={token.marginXXS}>
              <span>最后反馈人</span>
              <User showAvatar empIds={endRegressionUser} />
            </Flex>
            <Flex gap={token.marginXXS}>
              <span>开始时间</span>
              <span>{dayjs(regressionStartTime).format('YYYY-MM-DD HH:mm:ss')}</span>
            </Flex>
            <Flex gap={token.marginXXS}>
              <span>结束时间</span>
              <span>{dayjs(regressionEndTime).format('YYYY-MM-DD HH:mm:ss')}</span>
            </Flex>
          </Flex>
          <Flex gap={token.margin}>
            <Flex gap={token.marginXXS}>
              <span>回归耗时</span>
              <span>
                {regressionEndTime &&
                  regressionStartTime &&
                  calculateInterval(regressionEndTime, regressionStartTime)}
              </span>
            </Flex>
            <Flex gap={token.marginXXS}>
              <span>回归超时</span>
              <span>{regressionDurationExceedHour}小时</span>
            </Flex>
          </Flex>
          <Flex gap={token.margin}>
            <Flex gap={token.marginXXS}>
              <IterationsOutlined />
              <Link
                target="_blank"
                to={`/iterations/alterSheet/detail?entityId=${alterSheetId}`}
              >
                <span>{alterSheetName}</span>
              </Link>
            </Flex>
            <Flex gap={token.marginXXS}>
              <PositioningOutlined />
              <span>最后回归结果反馈于第{regressionRound}轮回归</span>
            </Flex>
          </Flex>
        </Flex>
      </ListItem>
    );
  };

  return (
    <Flex gap={token.margin} className={styles.bizRegressionDuration}>
      <Flex className={styles.card}>
        <TrendCard data={regressionDurationTrend} />
      </Flex>
      <Flex
        vertical
        flex={1}
        gap={token.margin}
      >
        <Flex className={styles.title}>相关数据分析</Flex>
        <Radio.Group
          block
          options={options}
          value={tab}
          optionType="button"
          buttonStyle="solid"
          style={{ width: 240 }}
          onChange={(e) => setTab(e.target.value)}
        />
        {tab === 'trend' && (
          <>
            <Flex vertical style={{ lineHeight: token.lineHeight, fontSize: token.fontSizeSM }}>
              <span>1、业务回归耗时通常可拆解成2部分耗时：回归响应耗时 和 响应之后的回归耗时。 </span>
              <span>2、回归响应耗时通常可以反映测试资源是否紧张，如果测试资源紧张，可能会加长响应耗时。</span>
              <span>3、响应之后的回归耗时，可以继续拆解到测试用例个数、测试用例自动化率、平均回归人力3个指标。</span>
              <span>
                4、测试用例个数可以反映本次回归的工作量，测试用例多，可能回归时长就会变长；测试用例自动化率如果达
                到一定水位，可以减少回归时长；回归人力可以反映本次回归是否人力资源紧张，资源紧张也会加长回归时长。
              </span>
            </Flex>
            <Flex className={styles.content}>
              <Flex vertical className={styles.info}>
                <Flex vertical className={styles.item}>
                  <Flex
                    align="center"
                    gap={token.marginXXS}
                    className={styles.title}
                  >
                    <span>响应耗时-P50</span>
                    <Tooltip title="当前发布单下，关联到本团队空间的每个回归需求，从该需求被发起回归开始到回归人接手开始的时长的50分位值">
                      <QuestionOutlined style={{ color: token.colorTextSecondary }} />
                    </Tooltip>
                  </Flex>
                  <Flex className={styles.duration}>
                    {p50Duration && (
                      <span className={styles.value}>
                        {p50Duration?.value as any}{unitMap[p50Duration?.unit || 'HOUR']}
                      </span>
                    )}
                  </Flex>
                  <Flex justify="space-between" style={{ marginTop: token.marginXS }}>
                    <span className={styles.label}>首次响应耗时-P50</span>
                    {firstP50Duration && (
                      <span className={styles.value}>
                        {firstP50Duration?.value as any}{unitMap[firstP50Duration?.unit || 'HOUR']}
                      </span>
                    )}
                  </Flex>
                </Flex>
                <Flex vertical className={styles.item}>
                  <Flex
                    align="center"
                    gap={token.marginXXS}
                    className={styles.title}
                  >
                    <span>响应耗时-P90</span>
                    <Tooltip title="当前发布单下，关联到本团队空间的每个回归需求，从该需求被发起回归开始到回归人接手开始的时长的90分位值">
                      <QuestionOutlined style={{ color: token.colorTextSecondary }} />
                    </Tooltip>
                  </Flex>
                  <Flex className={styles.duration}>
                    {p90Duration && (
                      <span className={styles.value}>
                        {p90Duration?.value as any}{unitMap[p90Duration?.unit || 'HOUR']}
                      </span>
                    )}
                  </Flex>
                  <Flex justify="space-between" style={{ marginTop: token.marginXS }}>
                    <span className={styles.label}>首次响应耗时-P90</span>
                    {firstP90Duration && (
                      <span className={styles.value}>
                        {firstP90Duration?.value as any}{unitMap[firstP90Duration?.unit || 'HOUR']}
                      </span>
                    )}
                  </Flex>
                </Flex>
              </Flex>
              <Flex vertical gap={token.marginLG} className={styles.trendChats}>
                <span className={styles.title}>响应耗时-P50/P90趋势</span>
                <RegressionDurationTrend
                  unit={unitMap[p50Duration?.unit || 'HOUR']}
                  data={[...p90TrendDetail, ...p50TrendDetail]}
                />
              </Flex>
            </Flex>
            <Flex className={styles.content}>
              <Flex vertical className={styles.info}>
                <Flex vertical className={styles.item}>
                  <Flex
                    align="center"
                    gap={token.marginXXS}
                    className={styles.title}
                  >
                    <Tooltip title="响应后回归耗时-P50/P90">
                      <span className={styles.titleText}>响应后回归耗时-P50/P90</span>
                    </Tooltip>
                    <Tooltip title="当前发布单下，关联到本团队空间的每个回归需求，从该需求的回归人接手开始到回归反馈结束（通过或不通过）的时长的50分位值/90分位值">
                      <QuestionOutlined style={{ color: token.colorTextSecondary }} />
                    </Tooltip>
                  </Flex>
                  <Flex className={styles.duration}>
                    {(afterP50Duration && afterP90Duration) && (
                      <Flex className={styles.value} gap={token.marginXXS}>
                        {afterP50Duration?.value as any}{unitMap[afterP50Duration?.unit || 'HOUR']}
                        <span>/</span>
                        {afterP90Duration?.value as any}{unitMap[afterP90Duration?.unit || 'HOUR']}
                      </Flex>
                    )}
                  </Flex>
                </Flex>
                <Flex vertical className={styles.item}>
                  <Flex justify="space-between" style={{ marginTop: token.marginXS }}>
                    <span className={styles.label}>业务回归人力</span>
                    {bizPersonDay && (
                      <span className={styles.value}>
                        {bizPersonDay?.value as any}{unitMap[bizPersonDay?.unit || 'PERSON_DAY']}
                      </span>
                    )}
                  </Flex>
                </Flex>
              </Flex>
              <Flex vertical gap={token.marginLG} className={styles.trendChats}>
                <span className={styles.title}>响应后回归耗时-P50/P90趋势</span>
                <RegressionDurationTrend
                  unit={unitMap[p50Duration?.unit || 'HOUR']}
                  data={[...afterP90Detail, ...afterP50Detail]}
                />
              </Flex>
            </Flex>
          </>
        )}
        {tab === 'detail' && (
          <DataEntryList
            loading={reqRegressionMetricsLoading}
            title={
              <Flex className={styles.title}>
                本团队业务回归明细（{reqRegressionMetrics?.length}）
              </Flex>
            }
            extra={
              <Flex gap={token.margin}>
                <Flex
                  gap={token.marginXXS}
                  onClick={() => setSortType('time')}
                  className={sortType === 'time' ? styles.sort : styles.normalStyle}
                >
                  <SortDescOutlined />
                  <span>回归耗时从大到小排序</span>
                </Flex>
                <Flex
                  gap={token.marginXXS}
                  onClick={() => setSortType('timeOut')}
                  className={sortType === 'timeOut' ? styles.sort : styles.normalStyle}
                >
                  <SortDescOutlined />
                  <span>回归超时从大到小排序</span>
                  <Tooltip title="结束时间 减 第一轮回归通知发起时间过5个小时">
                    <QuestionOutlined style={{ color: token.colorTextSecondary }} />
                  </Tooltip>
                </Flex>
              </Flex>
            }
            actionTrigger="show"
            dataSource={sortReqRegressionMetrics}
            renderItem={renderItem}
          />
        )}
      </Flex>
    </Flex>
  );
};
export default BizRegressionDuration;
