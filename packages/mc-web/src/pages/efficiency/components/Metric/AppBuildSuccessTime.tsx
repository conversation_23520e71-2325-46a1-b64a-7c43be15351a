import React, { useEffect } from 'react';
import { Flex, Statistic, Table, theme } from 'antd';
import { GetAppBuildDurationTrendParams, GetAppStageBuildDurationsParams, getAppBuildDurationTrend, getAppStageBuildDurations } from '@ali/mc-services/Efficiency';
import { useRequest } from '@ali/mc-request';
import { MetricComparativeData } from '@ali/mc-services';
import TrendCard from './components/TrendCard';
import styles from './index.module.less';
import { ArrowDownOutlined, ArrowUpOutlined, SearchInsightOutlined } from '@ali/mc-icons';
import { UnitMap } from '@/util/utils';

interface AppBuildSuccessTimeProps {
  spaceId?: number;
  applicationId?: number;
  versionPlanId?: number;
}

const AppBuildSuccessTime = (props: AppBuildSuccessTimeProps) => {
  const { token } = theme.useToken();
  const { spaceId, applicationId, versionPlanId } = props;

  const {
    runAsync: requestAppBuildDurationTrend,
    data: appBuildDurationTrend = {},
  } = useRequest<
    MetricComparativeData, [GetAppBuildDurationTrendParams]
  >(getAppBuildDurationTrend);

  const {
    runAsync: requestAppStageBuildDurations,
    data: appStageBuildDurations = [],
    loading: appStageBuildDurationsLoading,
  } = useRequest<
    MetricComparativeData[], [GetAppStageBuildDurationsParams]
  >(getAppStageBuildDurations);

  useEffect(() => {
    if (spaceId && applicationId && versionPlanId) {
      Promise.all([
        requestAppBuildDurationTrend({
          spaceId,
          appId: applicationId,
          versionPlanId: versionPlanId,
        }),
        requestAppStageBuildDurations({
          spaceId,
          appId: applicationId,
          versionPlanId: versionPlanId,
        }),
      ]);
    }
  }, [spaceId, applicationId, versionPlanId, requestAppBuildDurationTrend, requestAppStageBuildDurations]);

  const columns = [
    {
      title: '整包构建下各流水线阶段（不含卡口）',
      dataIndex: 'label',
      key: 'label',
      render: (label: string) => label,
    },
    {
      title: '平均执行时长(分钟)',
      dataIndex: 'value',
      key: 'value',
      render: (value: number) => value,
    },
    {
      title: '对比',
      dataIndex: 'comparativeValue',
      render: (comparativeValue: number, data: MetricComparativeData) => {
        const unit = data?.unit
          ? (UnitMap[data?.unit] || data?.unit) : data?.unit;
        const hasDiffValue = data?.diffValue !== undefined;
        return (
          <Flex
            gap="small"
            className={styles.baseline}
            style={{ visibility: comparativeValue ? 'visible' : 'hidden' }}
          >
            <span>对比{data?.baseline}</span>
            <span>
              {comparativeValue as any} {unit}
            </span>
            <Statistic
              value={hasDiffValue ? (data?.diffValue as any) : '-'}
              precision={2}
              valueStyle={
                (hasDiffValue && data?.flag !== 'EQUAL')
                  ? { color: data?.flag === 'DOWN' ? token.colorSuccess : token.colorError }
                  : undefined
              }
              prefix={
                (data?.flag && hasDiffValue && data?.flag !== 'EQUAL')
                  ? data.flag === 'UP'
                    ? <ArrowUpOutlined />
                    : <ArrowDownOutlined />
                  : undefined
              }
            />
          </Flex>
        );
      },
    },
  ];

  return (
    <Flex gap={token.margin} className={styles.appBuildSuccessTime}>
      <Flex className={styles.card}>
        <TrendCard data={appBuildDurationTrend} />
      </Flex>
      <Flex
        vertical
        flex={1}
        gap={token.margin}
      >
        <Flex className={styles.title}>相关数据分析</Flex>
        <Flex className={styles.desc} gap={token.marginXXS}>
          <SearchInsightOutlined style={{ color: token.colorTextTertiary }} />
          <span>当前指标进一步拆解，可下钻看各流水线阶段的平均执行时长：</span>
        </Flex>
        <Table
          loading={appStageBuildDurationsLoading}
          columns={columns}
          dataSource={appStageBuildDurations}
        />
      </Flex>
    </Flex>
  );
};
export default AppBuildSuccessTime;
