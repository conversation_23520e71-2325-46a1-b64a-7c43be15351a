import { MetricComparativeData } from '@ali/mc-services';

export type MetricComparativeLevel = 'EXTREME' | 'HIGH' | 'LOW' | 'UNKNOWN';

export function detectMetricComparativeLevel(data: MetricComparativeData): MetricComparativeLevel {
  const { identifier, value, unit } = data;
  const processTimeDay = unit === 'HOUR' ? (value as any / 24) : value;

  if (identifier === 'process_time') {
    const time = parseInt(processTimeDay as any, 10);
    if (time < 7) {
      return 'EXTREME';
    } else {
      return 'HIGH';
    }
  }

  if (identifier === 'biz_arch_maturity' || identifier === 'global_arch_maturity') {
    switch (value as any) {
      case 'L3':
        return 'EXTREME';
      case 'L2':
        return 'HIGH';
      case 'L1':
        return 'LOW';
      default:
        return 'UNKNOWN'; // 添加返回值
    }
  }

  if (identifier === 'process_recovery_time') {
    const time = parseInt(value as any, 10);
    if (time < 12) {
      return 'EXTREME';
    } else if (time <= 24 * 2) {
      return 'HIGH';
    } else {
      return 'LOW';
    }
  }

  if (identifier === 'slow_req_percent') {
    const percent = parseInt(((value as any) || '').replace('%', ''), 10);
    if (percent < 30) {
      return 'EXTREME';
    } else if (percent < 70) {
      return 'HIGH';
    } else {
      return 'LOW';
    }
  }

  return 'UNKNOWN';
}



export const getArchMaturityWidth = (value: string) => {
  switch (value) {
    case 'L3':
      return '100%';
    case 'L2':
      return '66.6%';
    case 'L1':
      return '33.3%';
    default:
      return '0%';
  }
};

export const getProcessRecoveryTime = (value: string) => {
  if (Number(value) < 12) {
    return '16.6%';
  } else if (Number(value) === 12) {
    return '33.3%';
  } else if (Number(value) > 12 && Number(value) < 48) {
    return '50%';
  } else if (Number(value) === 48) {
    return '66.6%';
  } else if (Number(value) > 48 && Number(value) < 96) {
    return '83%';
  } else {
    return '100%';
  }
};

// 获取进度条宽度
export const processBarWidth = (value: string, identifier: string, unit?: string) => {
  const processTimeHour = unit === 'HOUR' ? (14 * 24) : 14;
  switch (identifier) {
    case 'process_time':
      return `${(Number(value) / processTimeHour) * 100}%`;
    case 'biz_arch_maturity':
      return getArchMaturityWidth(value);
    case 'global_arch_maturity':
      return getArchMaturityWidth(value);
    case 'slow_req_percent':
      return value;
    case 'process_recovery_time':
      return getProcessRecoveryTime(value);
    default:
      return '0%';
  }
};
