import React from 'react';
import { But<PERSON>, Flex, theme, Tooltip } from 'antd';

import { Copy, StatusTag, User } from '@ali/mc-uikit';
import {
  CalendarOutlined,
  ArrowLeftOutlined,
  DeviceOutlined,
  IntegrateOutlined,
  IterationsOutlined,
  LinkExternalOutlined,
  PaperAirplaneOutlined,
  ProjectOutlined,
} from '@ali/mc-icons';

import styles from './index.module.less';
import { AoneRequest, ApplicationBO, getTimeStr, MetricData, VersionPlanBO } from '@ali/mc-services';
import { Link, useNavigate } from 'ice';
import { keyBy } from 'lodash-es';

type SlowRequirementDetailHeaderProps = {
  spaceId?: number;
  aoneRequest?: AoneRequest;
  applicationBO?: ApplicationBO;
  versionPlanBO?: VersionPlanBO;
  metricData?: MetricData[];
};
export default function SlowRequirementDetailHeader(props: SlowRequirementDetailHeaderProps) {
  const { token } = theme.useToken();
  // const screens = Grid.useBreakpoint();
  const navigate = useNavigate();

  const { spaceId, aoneRequest, applicationBO, versionPlanBO, metricData } = props;

  const metricMap = keyBy(metricData, 'identifier');

  let backTo: string | undefined;
  if (spaceId) {
    backTo = `/efficiency/${spaceId}/insights?tabKey=slow&applicationId=${applicationBO?.id}&versionPlanId=${versionPlanBO?.id}`;
  } else {
    backTo = `/app-efficiency/${applicationBO?.id}/insights?tabKey=slow&versionPlanId=${versionPlanBO?.id}`;
  }

  return (
    <Flex gap="small" align="baseline">
      <Button
        type="text"
        onClick={() => {
          navigate(backTo);
        }}
        icon={<ArrowLeftOutlined />}
      />
      <Flex vertical gap="small" className={styles.slowReqDetailHeader}>
        <Flex gap="small" align="center" className={styles.title} flex={1}>
          <Link to={aoneRequest?.detailUrl || ''} target="_blank">
            <Flex align="center" gap="small" flex={1}>
              <Tooltip title={aoneRequest?.reqName} overlayStyle={{ maxWidth: '90%' }}>
                <span style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  {aoneRequest?.reqName}
                </span>
              </Tooltip>
              <LinkExternalOutlined />
            </Flex>
          </Link>
          <StatusTag color="success">{aoneRequest?.status}</StatusTag>
        </Flex>
        <Flex gap="middle" className={styles.desc}>
          <Copy text={String(aoneRequest?.aoneReqId)}>#{aoneRequest?.aoneReqId}</Copy>
          <Flex align="center" gap={token.paddingXXS}>
            { aoneRequest?.userStaffId ? <User empIds={[aoneRequest?.userStaffId]} size="xsmall" showAvatar showName /> : '-' }
            <span>创建于</span>
            { aoneRequest?.createdAt ? <span>{getTimeStr(new Date(aoneRequest?.createdAt))}</span> : '-' }
          </Flex>
          <Flex align="center" gap={token.paddingXXS}>
            <span>指派给</span>
            { aoneRequest?.assignedToStaffId ? <User empIds={[aoneRequest?.assignedToStaffId]} size="xsmall" showAvatar showName /> : '-' }
          </Flex>
          <Link to={`https://aone.alibaba-inc.com/v2/projects/${aoneRequest?.akProjectId}`} target="_blank">
            <Flex align="center" gap={token.paddingXXS}>
              <ProjectOutlined />
              <span>{aoneRequest?.akProjectName}</span>
            </Flex>
          </Link>
          <Link to={`/apps/${applicationBO?.id}/config`} target="_blank">
            <Flex gap={token.paddingXXS} align="center">
              <DeviceOutlined />
              <span>{applicationBO?.name}</span>
            </Flex>
          </Link>
          <Link to={`/releases/${versionPlanBO?.id}`} target="_blank">
            <Flex gap={token.paddingXXS} align="center">
              <CalendarOutlined />
              <span>{versionPlanBO?.releaseVersion || versionPlanBO?.name}</span>
            </Flex>
          </Link>
          <Flex gap={token.margin}>
            <Flex gap={token.marginXS}>
              <IterationsOutlined />
              <span>参与{metricMap['slow_req_alter_sheet_count']?.value}个迭代</span>
            </Flex>
            <Flex gap={token.marginXS}>
              <IntegrateOutlined />
              <span>参与{metricMap['slow_req_integrate_sheet_count']?.value}个集成</span>
            </Flex>
            <Flex gap={token.marginXS}>
              <PaperAirplaneOutlined />
              <span>参与{metricMap['slow_req_release_count']?.value}个发布</span>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}
