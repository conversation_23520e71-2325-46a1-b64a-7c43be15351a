.gridNode {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 calc(var(--mc-padding-xxs) / 2);
  width: 100%;
  height: 100%;
  background-color: #eff4ff;
  border: 1px solid #5f95fd;
  cursor: pointer;
  &:hover {
    background-color: #e9ebed;
  }
  .gridNodeLabelWrapper {
    overflow: hidden;
  }
  .gridNodeLabel {
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .gridNodeTips {
    font-weight: var(--mc-font-weight-bold);
  }
}

.gridNodeHighlight {
  border-color: #f6cd51;
  box-shadow: 0px calc(var(--mc-padding-xxs) / 2) calc(calc(var(--mc-padding-xxs) / 2) + var(--mc-padding-xs)) #FFE28D;
  filter: saturate(0.3);
}

.lineageNode {
  display: flex;
  padding: var(--mc-padding-sm);
  width: 100%;
  height: 100%;
  background-color: var(--mc-color-fill);
  border: calc(var(--mc-padding-xxs) / 4) solid var(--mc-color-border);
  border-radius: calc(var(--mc-padding-sm) / 2);
  cursor: pointer;
  &:hover {
    border: calc(var(--mc-padding-xxs) / 2) solid var(--mc-color-primary);
    padding: calc(var(--mc-padding-sm) - var(--mc-padding-xxs) / 4);
  }
  .lineageHeader {
    width: 100%;
  }
  .lineageLabel {
    // width: 100%;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    color: var(--mc-color-text);
    font-size: var(--mc-font-size);
    font-weight: var(--mc-font-weight-strong);
  }
  .nodeText {
    font-size: var(--mc-font-size-sm);
    color: var(--mc-color-text-description);
  }
  .nodeLink {
    font-size: var(--mc-font-size-sm);
    color: var(--mc-color-text-description);
    cursor: pointer;
    &:hover {
      color: var(--mc-color-primary);
    }
  }
}

.canvas {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .canvasToolbar {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 50;
    .canvasToolbarIcon {
      font-size: var(--mc-font-size-xl);
      color: var(--mc-color-text-description);
      cursor: pointer;
    }
  }
}
