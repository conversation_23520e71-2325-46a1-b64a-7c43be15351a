.monacoEditorWrapper {
  width: 100%;
  border: var(--mc-line-width) solid var(--mc-color-border);
  border-radius: var(--mc-border-radius);
  overflow: hidden;

  :global {
    .mc-card-body {
      padding: 0;
    }
    .mc-tabs-nav {
      margin-bottom: 0;
    }
    .mc-tabs-nav-wrap {
      padding-left: var(--mc-padding);
    }
    .mc-tabs-tab {
      padding: var(--mc-padding-sm) var(--mc-padding);
    }
    .mc-tabs .mc-tabs-tab + .mc-tabs-tab {
      margin: 0;
    }
  }

  .editorHeader {
    background-color: var(--mc-gray-1);
    padding: var(--mc-padding-xs) var(--mc-padding);
    min-height: calc(var(--mc-font-height) + var(--mc-padding-xs) * 2);
    font-size: var(--mc-font-size-sm);
    border-bottom: 1px solid var(--mc-color-border);
    .divider {
      color: var(--mc-gray-3);
    }
    .editorHeaderText {
      font-size: var(--mc-font-size-sm);
    }
  }
  .editorHeaderDiff {
    padding: 0px;
    .editorHeaderDiffItem {
      width: 50%;
      &:last-child {
        margin-right: var(--mc-margin-xl);
        border-left: var(--mc-line-width) solid var(--mc-color-border);
      }
    }
    .editorHeaderDiffItemString {
      padding: var(--mc-padding-xs) var(--mc-padding);
      font-size: var(--mc-font-size);
      font-weight: var(--mc-font-weight-strong);
    }
  }
}

// dark 主题适配
[data-color-mode='dark'] {
  .monacoEditorWrapper {
    .editorHeader {
      background-color: var(--mc-color-bg-container);
    }
  }
}
