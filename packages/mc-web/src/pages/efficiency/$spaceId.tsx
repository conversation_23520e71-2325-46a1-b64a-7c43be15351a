import { useEffect } from 'react';
import { useNavigate, useParams } from 'ice';
import { getFullPathWithContext } from './common/searchParams';
import { useGateRecordContext } from './components/GateRecordContext';

export default function HomePage() {
  const { recordData } = useGateRecordContext();
  const navigate = useNavigate();
  const { spaceId } = useParams();

  useEffect(() => {
    navigate(getFullPathWithContext({ path: `/efficiency/${spaceId}/insights`, context: recordData ?? {} }));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
}
