

.CardWrap {
  .title {
    color: var(--mc-color-text);
    &:hover {
      color: var(--mc-color-primary);
    }
  }

  .listWrap {
    :global {
      .mc-list-item {
        padding: var(--mc-list-item-padding);
        padding-inline: var(--mc-padding-lg);
      }

      .mc-list-item-meta-avatar {
        font-size: var(--mc-font-size-lg);
      }
    }

    .moduleName, .aoneName {
      font-size: var(--mc-font-size-lg);
      height: var(--mc-font-size-xl);
      font-weight: var(--mc-font-weight-strong);
    }
    .moduleName:not(:hover), .aoneName:not(:hover) {
      color: var(--mc-color-text);
    }

    .moduleContent, .aoneContent {
      font-size: var(--mc-font-size-sm);
      color: var(--mc-color-text-secondary);
    }

    .right {
      font-size: var(--mc-font-size-sm);
      :global {
        .anticon {
          font-size: var(--mc-font-size);
        }
      }
    }
  }

  .footer {
    padding: var(--mc-margin);
    color: var(--mc-color-info-text);
    cursor: pointer;
    font-size: var(--mc-font-size-sm);

    .text {
      color: var(--mc-color-info-text);
    }

    .rightIcon {
      color: var(--mc-color-info-text);
    }
  }

  :global {
    .mc-list {
      border-bottom: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
    }

    .mc-tabs-tab {
      color: var(--mc-color-text-secondary) !important;
    }
    .mc-tabs-tab-active {
      color: var(--mc-color-text) !important;
    }
  }
}

.reload {
  cursor: pointer;
  width: 32px;
  height: 32px;
  color: var(--mc-color-text-secondary);
  background: var(--mc-gray-1);
  border-radius: var(--mc-border-radius);
  border: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
}

.more {
  padding: 16px;
  border: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
  border-radius: var(--mc-border-radius);
  cursor: pointer;

  .link {
    color: var(--mc-color-primary);
    font-size: var(--mc-font-size-sm);
  }
}

