import React, { useMemo } from 'react';
import { Flex } from 'antd';
import { getTimeStr } from '@ali/mc-services';
import { Copy, ListItem, User } from '@ali/mc-uikit';
import { Link } from 'ice';
import { OrganizationOutlined, StopOutlined } from '@ali/mc-icons';
import TagIcon from './TagIcon';
import { formatCountdownTimeStr } from '@/util/utils';

import styles from '@/pages/dashboard/index.module.less';

export type ToDoListItemProps = {
  item: any;
  index: number;
  currentTag: string;
};

export const ToDoListItem: React.FC<ToDoListItemProps> = (props) => {
  const { item, index, currentTag } = props;

  const timeouts = formatCountdownTimeStr(Date.now() - item?.gmtCreate, 'D 天 H 小时');
  let { handleUrl } = item || {};

  // 针对新版本迭代页面，进行链接替换
  if (handleUrl.includes('#/iteration/')) {
    const url = new URL(handleUrl.replace('#/', ''));
    const { pathname, searchParams } = url;
    const alterSheetId = searchParams.get('alterSheetId');

    let step: string | undefined;
    if (pathname.includes('/sdkAgileCiIntegration/')) {
      if (currentTag === 'CR') {
        step = 'DEV_SPACE_INTEGRATE_SDK_CODE_MERGE';
      } else if (currentTag === 'INTEGRATE') {
        step = 'DEV_SPACE_SDK_CONTINUOUS_INTEGRATION';
      } else if (currentTag === 'TEST') {
        step = 'DEV_SPACE_TEST';
      }
    } else if (pathname.includes('/bizDynamicDevelopment/') || pathname.includes('/sdkVerifyIntegration/')) {
      if (currentTag === 'CR') {
        step = 'DEV_SPACE_NON_INTEGRATE_SDK_CODE_MERGE';
      } else if (currentTag === 'INTEGRATE') {
        step = 'DEV_SPACE_BIZ_DYNAMIC_PUBLISH_LIST';
      }
    }

    if (step) {
      handleUrl = `/iterations/alterSheet/detail?${new URLSearchParams({
        entityId: alterSheetId!,
        step,
      })}`;
    }
  }

  const actions = useMemo(() => {
    if (item?.status === 'UNPROCESSED') {
      return [
        <Link key="process" target="_blank" to={handleUrl}>
          去处理
        </Link>,
      ];
    }
    return [];
  }, [handleUrl, item?.status]);

  return (
    <ListItem
      className={styles.listItem}
      icon={currentTag && <TagIcon itemKey={currentTag} />}
      title={
        <Link target={`iteration_${index}`} to={handleUrl}>
          {item?.tag}: {item?.title}
        </Link>
      }
      actions={actions}
    >
      <Flex vertical>
        <Flex align="center">
          {item?.workflowId && <Copy text={item?.workflowId}>#{item?.workflowId}</Copy>}
          <Flex align="center">
            <User showAvatar empIds={item?.creator} size="xsmall" />
            <span>创建于{getTimeStr(item?.gmtCreate, 'YYYY-MM-DD HH:mm:ss')}</span>
          </Flex>
        </Flex>
        <Flex>
          {item?.space?.name && (
            <Link target={`space_${item.space.id}`} to={`/teamspaces/spaceId=${item.space.id}`}>
              <Flex className={styles.action} align="center">
                <OrganizationOutlined className={styles.icon} />
                <span>{item?.space?.name}</span>
              </Flex>
            </Link>
          )}
        </Flex>
        {item?.status === 'UNPROCESSED' && (
          <Flex align="center" className={styles.timeouts}>
            <Flex>
              <StopOutlined className={styles.icon} />
              <span>已等待超过 {timeouts}，请尽快处理</span>
            </Flex>
          </Flex>
        )}
      </Flex>
    </ListItem>
  );
};
