import React from 'react';
import { defineDataLoader, Outlet } from 'ice';
import GlobalLayout from '@/components/GlobalLayout';
import DashboardSider from './components/Sider';
import { queryCollaborationSpacePage, queryPersonalTeamSpace } from '@ali/mc-services/CollaborationSpace';
import { isTopRouterChange } from '@/util/router';
import { CollaborationSpaceBO } from '@ali/mc-services';
import { useThemeContext } from '@ali/mc-uikit';
import DashboardLayoutContext from '@/context/DashboardLayoutContext';
import { ShouldRevalidateFunctionArgs } from 'react-router-dom';
import { useDeferData } from '@/hooks/useDeferData';

export default function DashboardWrapper() {
  const { isDark } = useThemeContext();
  const { data, loading } = useDeferData<{
    teamSpace: CollaborationSpaceBO;
    personalSpaceList: CollaborationSpaceBO[];
  }>();

  return (
    <DashboardLayoutContext.Provider
      value={{
        teamSpaceInfo: data?.teamSpace,
        loading: loading,
      }}
    >
      <GlobalLayout
        leftSider={
          <DashboardSider
            loading={loading}
            teamSpace={data?.teamSpace}
            personalSpaceList={data?.personalSpaceList || []}
          />
        }
        leftSiderProps={{
          theme: isDark ? 'dark' : 'light',
          width: 320,
        }}
      >
        <Outlet />
      </GlobalLayout>
    </DashboardLayoutContext.Provider>
  );
}

// 先找团队空间，如果没有显示个人空间
export const dataLoader = defineDataLoader(
  async () => {
    let teamSpace = null;
    let personalSpaceList: CollaborationSpaceBO[] = [];
    let res = await queryPersonalTeamSpace();
    if (res && res.belong === 'TEAM') {
      teamSpace = res;
    } else {
      const data = await queryCollaborationSpacePage({
        onlyShowMine: true,
        belong: 'PERSONAL',
        type: 'DEVELOPMENT',
        onlyShowFav: false,
      });
      personalSpaceList = data.items;
    }

    // if (!teamSpace && !personalSpaceList.length) {
    //   window.open('/#/spaces/development/list', '_self', 'noopener');
    // }

    return { teamSpace, personalSpaceList };
  },
  { defer: true },
);

export const shouldRevalidate = ({ currentUrl }: ShouldRevalidateFunctionArgs) => {
  return isTopRouterChange('/dashboard', currentUrl.pathname);
};
