import React from 'react';

import { StatusTag } from '@ali/mc-uikit';
import { ReleaseBO } from '@ali/mc-services';

import { PUBLISH_TYPE } from '@/constants/release';

export type ReleasePublishTypeTagProps = {
  type: NonNullable<ReleaseBO['publishType']>;
};
export default function ReleasePublishTypeTag(props: ReleasePublishTypeTagProps) {
  const { type } = props;
  const item = PUBLISH_TYPE[type];
  return <StatusTag color={item.color ?? 'gray'} >{item.label ?? type}</StatusTag>
}
