export const DELIVERY_EFFICIENCY_TAG_MAP = {
  EXTREME: '极速交付',
  HIGH: '快速交付',
  LOW: '慢速交付',
  UNKNOWN: '交付效率待产出',
};

export const DELIVERY_EFFICIENCY_ICON_MAP: {[key: string]: string} = {
  EXTREME: 'https://picasso-work.alibaba-inc.com/i2/O1CN01ZB8RYQ1jqqstRgizK_!!6000000004600-2-tps_intranet-164-164.png',
  HIGH: 'https://picasso-work.alibaba-inc.com/i2/O1CN013lJdZh1GJJ14YgYBv_!!6000000000601-2-tps_intranet-124-111.png',
  LOW: 'https://tpsservice-files-inner.cn-hangzhou.oss-cdn.aliyun-inc.com/images/resources/b5f2aaab21c0bf15d43b93b1c9c14d05-152-71.png',
};

export const DELIVERY_EFFICIENCY_COLOR_MAP = {
  EXTREME: '#1A7F37',
  HIGH: '#9A6700',
  LOW: '#D1242F',
  UNKNOWN: 'gray',
};

export const DELIVERY_EFFICIENCY_DESCRIPTION_MAP = {
  EXTREME: '您的模块已具备动态化能力，可使用动态化迭代随时发布不跟版，处于极速交付状态',
  HIGH: '您的模块虽不具备动态化能力，但项目包大小余额充足，包大小卡口可免批通过，处于快速交付状态',
  LOW: '模块尚未具备动态化能力、项目包大小余额不足，只能跟版发布且包大小卡口不通过需审批',
  UNKNOWN: '',
};
