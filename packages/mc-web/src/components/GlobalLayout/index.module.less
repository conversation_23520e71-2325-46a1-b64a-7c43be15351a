.leftSiderPanel{
  :global {
    .mc-menu-light.mc-menu-root.mc-menu-vertical {
      border-inline-end: none;
    }
    .mc-menu-vertical, .mc-menu-inline {
      padding-left: 0;
    }
    .mc-menu-vertical .mc-menu-item {
      // padding-inline: 0;
      margin-inline: 0;
    }

    .mc-menu-vertical .mc-menu-item .mc-menu-title-content {
      display: inline;
    }

    .mc-menu-vertical .mc-menu-item {
      width: 100%;
    }
  }
}