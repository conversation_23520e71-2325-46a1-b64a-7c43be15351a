
.pointerStyle, .md5IconStyle, .downloadIconStyle{
    cursor: pointer;
    font-size: var(--mc-font-size);
    color: var(--mc-color-text-secondary);
}

.md5IconStyle{
  font-size: var(--mc-font-size-lg);
}

.downloadIconStyle{
    font-size: var(--mc-font-size);
    position: relative;
    top: 1px;
}

.bundleItemWrap{
    position: relative;
    font-size: var(--mc-font-size-sm);
    color:var(--mc-color-text-secondary);
    line-height: var(--mc-line-height-sm);
}

.entryTextStyle {
  color: var(--mc-color-text-secondary) !important;
  &:disabled {
    color: var(--mc-color-text-disabled) !important;
  }
  &:not(:disabled){
    &:hover{
      color: var(--mc-color-link) !important;
      text-decoration-color: var(--mc-color-link) !important;
    }
  }
}

.moreArtifactList{
  :global{
      .mc-list{
          border: none;
      }
  }
}
.artifactItemWrap{
  border: 1px solid var(--mc-color-border);
  border-radius: var(--mc-border-radius-lg);
  margin-bottom: var(--mc-margin);
  padding: var(--mc-padding);
}

.labelStyle{
  margin-right: var(--mc-margin-xs);
  white-space: nowrap;
}

.titleStyle{
  font-weight: var(--mc-font-weight-strong);
  margin-bottom: var(--mc-margin-xs);
  font-size: var(--mc-font-size);
}
