.versionPlanPage {
  margin-top: var(--mc-margin);
  border-radius: var(--mc-border-radius);
  border: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border-secondary);
  background: linear-gradient(180deg, #c9e7ff 0%, #ffffff 20%, #ffffff 42%);
  position: relative;

  .loading_box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 20;
  }
}

.versionPlan {
  // .tags {
  //   display: flex;
  //   justify-content: space-between;

  //   .tag {
  //     width: 149px;
  //     height: 32px;
  //     padding: 0 12px;
  //     display: flex;
  //     line-height: 32px;
  //     border-radius: 269px;
  //     background: #FFFFFF;
  //     border: 1px solid #D9D9D9;
  //     font-family: PingFang SC;
  //     font-size: 12px;
  //     color: #333333;
  //     cursor: pointer;
  //     word-wrap: nowrap;
  //     white-space: nowrap;
  //   }

  //   .selectTag {
  //     width: 149px;
  //     height: 32px;
  //     padding: 0 12px;
  //     display: flex;
  //     line-height: 32px;
  //     border-radius: 269px;
  //     background: #FFFFFF;
  //     border: 1px solid #1677FF;
  //     font-family: PingFang SC;
  //     font-weight: 500;
  //     font-size: 12px;
  //     color: #1677FF;
  //     cursor: pointer;
  //     word-wrap: nowrap;
  //     white-space: nowrap;
  //   }

  //   .img {
  //     width: 16px;
  //     height: 16px;
  //     border-radius: 4px;
  //     margin-right: 4px;
  //     margin-top: 8px;
  //   }
  // }

  .versionPlanHeader {
    margin-top: var(--mc-margin);

    .title {
      font-size: var(--mc-font-size);
      font-weight: 500;
      line-height: var(--mc-line-height);
      color: var(--mc-color-text);

      .limited,
      .formal {
        padding: 0px var(--mc-padding-xs);
        border-radius: var(--mc-border-radius-sm);
        font-size: var(--mc-font-size-sm);
        line-height: var(--mc-line-height-sm);
        margin-left: var(--mc-margin);
      }
    }

    .link {
      font-size: var(--mc-font-size);
      line-height: var(--mc-line-height);
      color: var(--mc-color-link-active);
      cursor: pointer;
    }
  }

  .dataRange {
    margin-top: var(--mc-margin);
    font-size: var(--mc-font-size-sm);
    line-height: var(--mc-line-height-sm);
    color: var(--mc-color-text);
  }

  .versionPlanSteps {
    margin: var(--mc-margin) calc(0px - var(--mc-margin)) auto;

    .progressItem_box {
      flex-grow: 1;

      .type,
      .version,
      .time {
        font-size: var(--mc-font-size-sm);
        line-height: var(--mc-line-height-sm);
      }

      .time {
        color: var(--mc-color-primary);
      }

      .progressLine {
        position: relative;
        width: 100%;
        margin: var(--mc-margin-xs) 0 var(--mc-margin-xs);

        .gradientline {
          left: 0;
          background-image: linear-gradient(90deg, var(--mc-color-bg-base) 0%, transparent 100%);
        }

        .gradientline_contrary {
          right: 0;
          background-image: linear-gradient(270deg, var(--mc-color-bg-base) 0%, transparent 100%);
        }

        .gradientline,
        .gradientline_contrary {
          position: absolute;
          top: 0;
          width: 24px;
          height: 8px;
        }

        .leftLine {
          border-radius: calc(var(--mc-border-radius-sm) / 2) 0 0 calc(var(--mc-border-radius-sm) / 2);
        }

        .rightLine {
          border-radius: 0 calc(var(--mc-border-radius-sm) / 2) calc(var(--mc-border-radius-sm) / 2) 0;
        }

        .leftLine,
        .rightLine {
          height: 3px;
          flex-grow: 1;
        }

        .centerDot {
          margin: 0 var(--mc-margin-xxs);
          width: 8px;
          height: 8px;
          border-radius: var(--mc-border-radius-sm);
        }
      }
    }
  }
}

.detail {
  height: 54px;
  border-top: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border-secondary);
  cursor: pointer;
  font-size: var(--mc-font-size);
  line-height: var(--mc-line-height);
  color: var(--mc-color-link-active);
}
