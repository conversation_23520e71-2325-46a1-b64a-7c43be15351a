.dynamicIterationTip {
  flex: 1 1 0;
  .title {
    opacity: 0.85;
    font-weight: 500;
    color: var(--mc-color-text);
    line-height: var(--mc-lineHeight);
    margin-bottom: var(--mc-margin-sm);
  }
  .descriptions {
    font-size: var(--mc-font-size-sm);
  }
  .listWrap {
    flex: 1 1 0;
    overflow: scroll;
  }
  .info {
    line-height: var(--mc-lineHeightSM);
    color: var(--mc-color-text-secondary);
    font-size: var(--mc-font-size-sm);
    .linkText {
      cursor: pointer;
      &:hover {
        color: var(--mc-color-primary);
      }
    }
  }

  :global {
    .mc-list-item {
      padding: var(--mc-padding-sm) 0 !important;
    }
    .mc-list-split .mc-list-item:last-child {
      border-block-end: var(--mc-line-width) var(--mc-line-type) var(--mc-color-split);
    }
    .mc-list-item-meta-title {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  a {
    color: var(--mc-color-link-hover);
  }
}
