.addonSelect {
  cursor: pointer;
  border: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
  border-radius: var(--mc-border-radius);
  > div, input {
    margin-inline-end: 0px !important;
    margin-inline-start: 0px !important;
  }

  :global {
    .mc-select {
      &:not([class*="mc-addon-select"]){
        border-radius: var(--mc-border-radius);
      }
      &:is([class*="mc-addon-select-after"]){
        border-top-right-radius: var(--mc-border-radius);
        border-bottom-right-radius: var(--mc-border-radius);
      }
    }
    .mc-select-arrow {
      color: #59636e;
    }
    .mc-select-selector {
      border: none !important;
      box-shadow: none !important;
    }
    .mc-input-disabled{
      cursor: default !important;
    }
    .mc-addon-select{
      font-weight: var(--mc-font-weight-strong);
      color: #59636e;
      white-space: nowrap;
      width: fit-content;
      background-color: #eff2f5;

      &-addon-wrapper{
        padding-inline: var(--mc-padding-xs);
      }
      &-small {
        height: var(--mc-control-height-sm);
        line-height: var(--mc-line-height-sm);
        font-size: var(--mc-font-size-sm);
      }
      &-middle {
        height: var(--mc-control-height);
        line-height: var(--mc-line-height);
        font-size: var(--mc-font-size);
      }
      &-large {
        height: var(--mc-control-height-lg);
        line-height: var(--mc-line-height-lg);
        font-size: var(--mc-font-size-sm-lg);
      }
    }
    .mc-addon-select-before{
      border-top: none !important;
      border-bottom: none !important;
      border-left: none !important;
      border-right: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border) !important;
      border-top-left-radius: var(--mc-border-radius);
      border-bottom-left-radius: var(--mc-border-radius);
    }
    .mc-addon-select-after{
      border-top: none !important;
      border-bottom: none !important;
      border-right: none !important;
      border-left: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border) !important;
      border-top-right-radius: var(--mc-border-radius);
      border-bottom-right-radius: var(--mc-border-radius);
    }

  }
  &:hover {
    border-color: var(--mc-color-primary);
  }
}

