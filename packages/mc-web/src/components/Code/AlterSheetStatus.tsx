import React from 'react';
import { StatusTag } from '@ali/mc-uikit';

const STATUS_MAP: Record<string, string> = {
  ONGOING: '进行中',
  OPENED: '开启',
  TEST_SUBMITTED: '已提测',
  INTEGRATED: '已集成',
  BETA: '灰度中',
  GRAY: '灰度中',
  PUBLISHING: '正式发布中',
  PUBLISHED: '已发布',
  REGRESSION_ONGOING: '回归中',
  REGRESSION_PASS: '回归通过',
  REGRESSION_FAIL: '回归失败',
  CLOSED: '关闭',
};


export default (props: { status: string }) => {
  const { status } = props;
  let statusColor = 'default';
  switch (status) {
    case 'TEST_SUBMITTED':
    case 'INTEGRATED':
    case 'PUBLISHED':
      statusColor = 'success';
      break;
    case 'OPEN':
    case 'OPENED': // 开启
    case 'BETA': // 灰度中
    case 'GRAY':
    case 'PUBLISHING': // 正式发布中
      statusColor = 'processing';
      break;
    default: // 其他情况类似关闭了。
      statusColor = 'default';
  }

  if (status) {
    return (
      <StatusTag color={statusColor}>{STATUS_MAP[status] ?? status}</StatusTag>
    );
  }
  return null;
};
