import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, Input, Modal, Select, Upload, message, Alert, FormProps } from 'antd';
import { showDrawboard, closeDrawboard } from '@ali/xfeedback-drawboard-api';

import { createConversation } from './fetch';
import { request } from '@ali/mc-services';

import styles from './index.module.less';
import { ArrowRightOutlined, ScissorOutlined, UploadOutlined } from '@ant-design/icons';
import { sendEvent } from './util';

interface ConversationFormProps extends FormProps {
  pathname: string;
  keyword?: string;
  isShow?: boolean;
  onSubmit?: (result: any) => void;
  onCapture?: (capturing: boolean) => void;
}

function getBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
}

function dataURLtoBlob(dataUrl: string): Blob {
  let arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png';
  const bstr = atob(arr[1]);
  let len = bstr.length;
  const u8arr = new Uint8Array(len);

  while (len--) {
    u8arr[len] = bstr.charCodeAt(len);
  }
  return new Blob([u8arr], { type: mime });
}

function isMacOS() {
  return window.navigator.userAgent.includes('(Macintosh;');
}

const keys = {
  scene: '问题场景',
  link: '问题链接',
  description: '问题描述',
};

const PATHNAME_MAP: Record<string, string> = {
  sdkAgileCiIntegration: '持续集成',
  bizDynamicDevelopment: '业务动态变更',
  crossPlatformDevelopment: '跨端页面研发',
  sdkVerifyIntegration: '自助验证',
};

function prepareFormData(values: any, fileList: any[]) {
  const formData: any = {
    data: [
      {
        key: 'fromTitle',
        label: '来源页面标题',
        showText: document.title,
        value: document.title,
      },
      {
        key: 'fromUrl',
        label: '来源页面 URL',
        showText: location.href,
        value: location.href,
      },
    ],
  };

  for (const key in values) {
    if (!values[key]) continue;

    if (key === 'link' && !values.link) {
      values.link = location.href; // eslint-disable-line
    }

    formData.data.push({
      key,
      label: keys[key] || key,
      showText: values[key],
      value: values[key],
    });
  }

  if (fileList?.length) {
    formData.attachment = {
      name: '附件',
      fileList: fileList.map((file) => {
        return {
          name: file.name,
          url: file.url || file.response.data,
        };
      }),
    };
  }

  return formData;
}

function CaptureHelper() {
  return (
    <>
      <Alert
        style={{
          display: 'block',
          width: '100%',
        }}
        message={
          <div className={styles.captureHelperMessage}>
            <div>
              <p>即将调用浏览器 webRTC 接口进行截图</p>
              <ul>
                <li>截图只有在用户授权之后才会进行</li>
                <li>仅会获取已授权窗口的可见区域</li>
              </ul>
            </div>
            <ArrowRightOutlined
              className={styles.arrow}
              style={{
                fontSize: '48px',
                color: 'red',
              }}
            />
          </div>
        }
        type="warning"
      />
      <img
        width="400"
        style={{
          padding: '5px',
          marginTop: '8px',
          boxShadow: '0 0 1px 0 rgba(9, 30, 66, 0.31), 0 18px 28px 0 rgba(9, 30, 66, 0.15)',
        }}
        src="https://tpsservice-files-inner.cn-hangzhou.oss-cdn.aliyun-inc.com/images/resources/1ad43247d730090044b48e5f5b2b99c7-1202-1066.png"
      />
    </>
  );
}

async function capture(onCapture: (capturing: boolean) => void): Promise<string> {
  onCapture(true);
  const modal = Modal.info({
    className: styles.captureHelpModal,
    content: <CaptureHelper />,
    transitionName: '',
    maskTransitionName: '',
    width: 468,
  });

  try {
    const stream = await navigator.mediaDevices.getDisplayMedia({
      video: {
        displaySurface: 'window',
        width: { ideal: window.outerWidth },
        height: { ideal: window.outerHeight },
        frameRate: { ideal: 60 },
      },
      audio: false,
    });
    modal.destroy();

    const video = document.createElement('video');
    video.width = window.outerWidth;
    video.srcObject = stream;

    return new Promise((resolve) => {
      video.onloadedmetadata = () => {
        video.play().then(() => {
          setTimeout(() => {
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx?.drawImage(video, 0, 0);
            const imgDataUrl = canvas.toDataURL('image/png', 1);
            stream.getTracks().forEach((track) => track.stop());
            // 这个 imgDataUrl 就是屏幕截图
            onCapture(false);
            resolve(imgDataUrl);
          }, 1000);
        });
      };
    });
  } catch (err) {
    console.log(err);
    onCapture(false);
    return Promise.reject();
  } finally {
    modal && modal.destroy();
  }
}

type FileListItem = {
  uid: number;
  name: string;
  status: string;
  preview: string;
};
export default function ConversationForm(props: ConversationFormProps) {
  const { onSubmit, onCapture, pathname, keyword, isShow } = props;
  const domRef = useRef(null);
  const [fileList, setFileList] = useState<FileListItem[]>([]);
  const [previewImage, setPreviewImage] = useState();
  const [previewVisible, setPreviewVisible] = useState(false);

  const [form] = Form.useForm();

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    form.validateFields(async (err, data) => {
      if (err) return;
      const formData = prepareFormData(data, fileList);
      const res: any = await createConversation(formData);
      if (res.msg === 'success') {
        onSubmit && onSubmit(res.result);
      }
    });
  };

  useEffect(() => {
    const scene = findSceneByPathname(pathname);
    form.setFieldsValue({
      scene: scene,
      description: keyword,
    });
  }, [pathname, keyword]);

  function findSceneByPathname(_pathname: string): string | undefined {
    for (const key in PATHNAME_MAP) {
      if (_pathname?.includes(key)) {
        return PATHNAME_MAP[key];
      }
    }
    return undefined;
  }

  const uploadCapture = async (imgDataUrl: string) => {
    // 塞个 loading 状态
    const uid = Date.now();
    const name = `capture-${Date.now()}.png`;

    setFileList((prevState) => [
      ...prevState,
      {
        uid,
        name,
        status: 'uploading',
        preview: imgDataUrl,
      },
    ]);

    // console.log(imgDataUrl);
    const blob = dataURLtoBlob(imgDataUrl);
    const formData = new FormData();
    formData.append('prefix', 'links');
    formData.append('file', blob, name);

    const url = await request('/dev/api/v1/custom/upload', {
      // 你的上传API
      method: 'POST',
      body: formData,
    });

    if (sendEvent) {
      sendEvent('asstUploadWithCapture', {
        et: 'CLK',
      });
    }

    setFileList((prevState = []) => {
      const newState = prevState.map((_item) => {
        const item = { ..._item };
        if (item.uid === uid) {
          item.status = 'done';
          item.url = url;
        }

        return item;
      });
      return newState;
    });
  };

  const doCapture = async (e: React.FormEvent<HTMLFormElement>) => {
    e.stopPropagation();

    const data = await capture(onCapture);
    Modal.confirm({
      title: '编辑截图',
      content: '是否需要针对截图进行编辑标注？',
      okText: '对截图进行标注',
      cancelText: '直接上传',
      onOk: () => {
        const img = new Image();
        img.src = data;
        img.onload = () => {
          const drawboard = showDrawboard(img, {
            primaryColor: '#ff6a00',
            async onSubmit(canvas) {
              const imgDataUrl = canvas.toDataURL('image/png');
              await uploadCapture(imgDataUrl);
              closeDrawboard(drawboard);
            },
            async onCancel() {
              await closeDrawboard(drawboard);
            },
          });
        };
      },
      onCancel() {
        uploadCapture(data);
      },
    });
  };

  const onPaste = async (event) => {
    if (!isShow) return;
    let { items } = (event.clipboardData || event.originalEvent.clipboardData);

    for (let i = 0; i < items.length; i++) {
      // 检查粘贴的项目是否为种类型图片，简单处理，只取当识别到一个图片之后，就退出不再处理后续的内容
      if (items[i].type.indexOf('image') !== -1) {
        // 防止粘贴的项目内容转换为Blob
        let blob = items[i].getAsFile();

        const dataUrl = await getBase64(blob);
        uploadCapture(dataUrl);
        if (sendEvent) {
          sendEvent('asstUploadWithPaste', {
            et: 'INPUT',
          });
        }
        break;
      }
    }
  };

  useEffect(() => {
    document.addEventListener('paste', onPaste, false);

    return () => {
      document.removeEventListener('paste', onPaste, false);
    };
  }, [isShow]);

  const uploadButton = (
    <div className={styles.uploadButtons}>
      <Button>
        <UploadOutlined />
        上传图片
      </Button>
      <Button onClick={doCapture} style={{ marginLeft: '8px' }}>
        <ScissorOutlined />
        当前页面截图
      </Button>
    </div>
  );

  const handleUploadPreview = async (_file) => {
    const file = { ..._file };
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }

    setPreviewImage(file.preview || file.url);
    setPreviewVisible(true);
  };

  const handleUploadChange = ({ _fileList }) => {
    setFileList([..._fileList]);
  };

  const beforeUpload = (file: File | Blob) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('仅支持上传 JPEG 和 PNG 图片!');
    }
    return isJpgOrPng;
  };

  const transformFile = (file) => {
    return new File([file], file.uid, { type: file.type });
  };

  return (
    <div ref={domRef} className={styles.conversationFormWrapper}>
      <Form className={styles.conversationForm} layout="vertical" onSubmit={handleSubmit}>
        <Form.Item name="scene" label="问题场景" rules={[{ required: true, message: '请选择问题归属的场景' }]} required>
          <Select
            getPopupContainer={() => domRef.current || document.body}
            placeholder="请选择问题分类场景"
            options={Object.values(PATHNAME_MAP)
              .map((value) => {
                return {
                  label: value,
                  value: value,
                  key: value,
                };
              })
              .concat([
                {
                  label: '产品',
                  key: 'product',
                  value: '产品',
                },
                {
                  label: '构建',
                  key: 'build',
                  value: '构建',
                },
              ])}
          />
        </Form.Item>
        <Form.Item
          name="description"
          label="问题描述"
          rules={[{ required: true, message: '必须填写问题相关的描述信息' }]}
          required
        >
          <Input.TextArea rows={12} />
        </Form.Item>
        <Form.Item
          label={`问题截图（点击按钮上传或复制图片后使用快捷键粘贴 ${isMacOS() ? 'Command + v' : 'Control + v'} 上传）`}
        >
          <Upload
            action="/dev/api/v1/custom/upload"
            name="file"
            data={{ prefix: 'links' }}
            fileList={fileList}
            listType="picture"
            withCredentials
            transformFile={transformFile}
            beforeUpload={beforeUpload}
            onPreview={handleUploadPreview}
            onChange={handleUploadChange}
            className="upload-list-inline"
          >
            {fileList.length >= 6 ? null : uploadButton}
          </Upload>
        </Form.Item>
        <Button htmlType="submit" type="primary" style={{ marginTop: '8px' }}>
          创建工单
        </Button>
      </Form>
      <Modal title="图片预览" visible={previewVisible} footer={null} onCancel={() => setPreviewVisible(false)}>
        <img alt="截图预览" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
}
