[{"label": "团队空间", "snippet": "64c881f20d44f0060b1ea7db", "matched": ["/iteration/spaceHome", "/space/development/list", "/iteration/sdkAgileCiIntegration/list", "/iteration/sdkAgileCiIntegration/detail", "/iteration/bizDynamicDevelopment/list", "/iteration/crossPlatformDevelopment/list", "/iteration/crossPlatformDevelopment/detail", "/iteration/bizDynamicDevelopment/detail", "/iteration/module"]}, {"label": "持续集成", "snippet": "64c886c33cb06406eb10198a", "matched": ["/iteration/sdkAgileCiIntegration/list", "/iteration/sdkAgileCiIntegration/detail"]}, {"label": "发布空间", "snippet": "64c882dd3676ad0613c3208f", "matched": ["/space/release/list", "/iteration/appVersionIntegration/list", "/iteration/appVersionIntegration/detail", "/iteration/bizDynamicPublish/list", "/iteration/bizDynamicPublish/detail"]}, {"label": "业务动态发布", "snippet": "", "matched": ["/iteration/bizDynamicPublish/list", "/iteration/bizDynamicPublish/detail"]}, {"label": "变更模块", "snippet": "64c883d1f5b697060ab03efa"}, {"label": "构建节点", "snippet": "64c8840951a53d06275de6b1"}, {"label": "预构建", "snippet": "64e85c7376b7b206e2df5928"}, {"label": "跟版集成", "snippet": "64e892f1b53697062a7e1249"}, {"label": "跨端页面研发", "snippet": "64ed6966733089062f8f22bf", "matched": ["/iteration/crossPlatformDevelopment/list", "/iteration/crossPlatformDevelopment/detail"]}, {"label": "业务动态变更", "snippet": "64ed84d62c04e706ddc72a2d", "matched": ["/iteration/bizDynamicDevelopment/list", "/iteration/bizDynamicDevelopment/detail"]}]