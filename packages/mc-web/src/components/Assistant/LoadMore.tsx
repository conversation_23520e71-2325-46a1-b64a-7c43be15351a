import React, { useRef, useState } from 'react';
import { Button, Popover } from 'antd';

import Empty from './Empty';

import styles from './index.module.less';

export type LoadMoreProps = {
  hasMore?: boolean;
  loading?: boolean;
  isEmpty?: boolean;
  emptyComponent?: React.ReactNode;
  showPopover?: boolean;
  onLoadMore?: () => void;
  onCreate?: () => void;
};

function LoadMore(props: LoadMoreProps) {
  const [count, setCount] = useState(0);
  const ref = useRef(null);

  if (shouldRenderEmpty(props)) {
    return renderEmpty(props);
  }

  return props.hasMore ? (
    <div ref={ref} className={styles.loadMore} onClick={handleLoadMore}>
      {props.showPopover === false ? (
        <span style={{ width: '100%', textAlign: 'center' }}>加载更多</span>
      ) : (
        <Popover content={renderPopoverContent(props)} getPopupContainer={getPopupContainer} trigger="hover">
          <span style={{ width: '100%', textAlign: 'center' }}>加载更多</span>
        </Popover>
      )}
    </div>
  ) : null;

  function shouldRenderEmpty({ loading, isEmpty }: LoadMoreProps) {
    return !loading && isEmpty;
  }

  function renderEmpty({ emptyComponent, onCreate }: LoadMoreProps) {
    if (emptyComponent) {
      return emptyComponent;
    }
    return <Empty onClick={onCreate} />;
  }

  function handleLoadMore() {
    if (props.loading) return;
    props.onLoadMore && props.onLoadMore();
    setCount(count + 1);
  }

  function renderPopoverContent({ onCreate }: LoadMoreProps) {
    return (
      <div>
        没有搜索到匹配的结果？
        <Button type="link" style={{ padding: 0 }} onClick={onCreate}>
          提交人工答疑
        </Button>
      </div>
    );
  }

  function getPopupContainer() {
    return ref?.current || document.body;
  }
}

export default LoadMore;
