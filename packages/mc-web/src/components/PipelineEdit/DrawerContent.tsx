import { But<PERSON>, Drawer, Flex, theme } from 'antd';
import React, { useCallback } from 'react';
// import styles from './index.module.less';
import { DrawerContentProps } from './type';


const DrawerContent = ({ open, onClose }: DrawerContentProps) => {
  const { token } = theme.useToken();

  const handleSubmit = useCallback(() => {

  }, []);


  return (<Drawer
    open={open}
    onClose={onClose}
    destroyOnClose
    title="编辑流水线"
    styles={{
      wrapper: {
        width: '100%',
      },
      body: {
        padding: '0px auto',
      },

    }}
    footer={<Flex style={{ width: '100%', justifyContent: 'center', alignItems: 'center' }} gap={token.margin}>
      <Button onClick={onClose}>
        取消
      </Button>
      <Button type="primary" onClick={handleSubmit}>
        运行构建
      </Button>
    </Flex>}
  >
    {/* <PipelineGraph /> */}
  </Drawer>);
};

export default DrawerContent;
