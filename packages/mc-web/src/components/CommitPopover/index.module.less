
.commitPopover{
  :global{
    .mc-popover-inner{
        padding: 0px;
    }
  }
  .popoverTitle{
    font-size: var(--mc-font-size);
    font-weight: normal;
    color: var(--mc-color-text);
    padding-left: var(--mc-padding);
    height: 30px;
    // border-bottom: 1px solid var(--mc-color-border);
  }
  .popoverContentWrap{
    min-width: 340px;
    max-width: 580px;
    color: var(--mc-color-text-secondary);
    padding-inline: var(--mc-padding);
    padding-bottom: var(--mc-padding-xs);
    span{
        word-wrap: break-word;
        word-break: break-all;
    }
    .labelStyle{
        white-space: no-wrap;
        min-width: 40px;
        &::after {
            content: ':';
            margin-right: var(--mc-padding-xs);
        }
    }
    .linkStyle{
        color: var(--mc-color-primary);
    }

}
}

.childrenStyle{
    color: var(--mc-color-text-secondary);
}
