:global {
    .submodulesDrawer {
      .ant-drawer-body {
        padding: 0;
      }
    }
  }

  .submodulesContent {
    margin-top: 0;
    width: 360px;
    height: 100vh;
    background-color: #f9fafb;
    position: relative;

    .bgPng {
      height: 99px;
      background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01HaqQU11csUrctz6gh_!!6000000003656-0-tps-360-99.jpg);
      font-weight: var(--mc-font-weight-strong);
      font-size: 20px;
      color: var(--mc-color-white);
      text-align: center;
      padding-top: var(--mc-padding-lg);
    }

    .structure {
      // width: 328px;
      margin: auto;
      margin-top: -31px;
      padding: 0 var(--mc-padding);
      height: calc(100vh - 99px);
      overflow-y: auto;

      >div {
        height: 100%;
      }

      &::-webkit-scrollbar {
        height: 0;
        width: 0;
        color: transparent;
      }
    }
  }
