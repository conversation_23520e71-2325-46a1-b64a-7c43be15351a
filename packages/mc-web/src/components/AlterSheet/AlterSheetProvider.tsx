import React, { createContext, useContext, useEffect } from 'react';
import { AlterSheetBO } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import { findAlterSheet, FindAlterSheetParams } from '@ali/mc-services/AlterSheet';

const AlterSheetContext = createContext<{
  alterSheet?: AlterSheetBO;
}>({});

export interface AlterSheetProviderProps {
  alterSheetId: number;
  children: React.ReactNode;
}
export default function ALterSheetProvider(props: AlterSheetProviderProps) {
  const { alterSheetId, children } = props;

  const { data: alterSheet } = useRequest<AlterSheetBO, [FindAlterSheetParams]>(
    findAlterSheet,
  );

  useEffect(() => {}, [alterSheetId]);

  return (
    <AlterSheetContext.Provider
      value={{
        alterSheet,
      }}
    >
      {children}
    </AlterSheetContext.Provider>
  );
}

export const useAlterSheetContext = () => useContext(AlterSheetContext);
