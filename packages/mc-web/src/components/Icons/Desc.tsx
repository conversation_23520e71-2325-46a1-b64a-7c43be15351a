import * as React from 'react';
import type { SVGProps } from 'react';
const SvgDesc = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    viewBox="0 0 16 16"
    className="anticon"
    focusable="false"
    width="1em"
    height="1em"
    aria-hidden="true"
    {...props}
  >
    <defs>
      <clipPath id="master_svg0_163_18352">
        <path d="M0 0H16V16H0z" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_163_18352)">
      <path
        d="M2.75,2.5C2.6837,2.5,2.62011,2.5263400000000003,2.57322,2.57322C2.5263400000000003,2.62011,2.5,2.6837,2.5,2.75C2.5,2.75,2.5,10.25,2.5,10.25C2.5,10.388,2.612,10.5,2.75,10.5C2.75,10.5,4.75,10.5,4.75,10.5C4.94891,10.5,5.13968,10.579,5.28033,10.7197C5.42098,10.8603,5.5,11.0511,5.5,11.25C5.5,11.25,5.5,13.44,5.5,13.44C5.5,13.44,8.219999999999999,10.72,8.219999999999999,10.72C8.360520000000001,10.5793,8.55115,10.5002,8.75,10.5C8.75,10.5,13.25,10.5,13.25,10.5C13.3163,10.5,13.3799,10.4737,13.4268,10.4268C13.4737,10.3799,13.5,10.3163,13.5,10.25C13.5,10.25,13.5,2.75,13.5,2.75C13.5,2.6837,13.4737,2.62011,13.4268,2.57322C13.3799,2.5263400000000003,13.3163,2.5,13.25,2.5C13.25,2.5,2.75,2.5,2.75,2.5C2.75,2.5,2.75,2.5,2.75,2.5ZM1,2.75C1,1.784,1.784,1,2.75,1C2.75,1,13.25,1,13.25,1C14.216,1,15,1.784,15,2.75C15,2.75,15,10.25,15,10.25C15,10.7141,14.8156,11.1592,14.4874,11.4874C14.1592,11.8156,13.7141,12,13.25,12C13.25,12,9.06,12,9.06,12C9.06,12,6.487,14.573,6.487,14.573C6.28324,14.7767,6.02367,14.9153,5.74111,14.9715C5.45854,15.0277,5.16567,14.9988,4.8995,14.8886C4.63333,14.7784,4.40581,14.5917,4.24571,14.3522C4.08561,14.1127,4.0001,13.8311,4,13.543C4,13.543,4,12,4,12C4,12,2.75,12,2.75,12C2.28587,12,1.8407499999999999,11.8156,1.5125600000000001,11.4874C1.18437,11.1592,1,10.7141,1,10.25C1,10.25,1,2.75,1,2.75C1,2.75,1,2.75,1,2.75Z"
        fillRule="evenodd"
        fill="currentColor"
        fillOpacity={1}
      />
    </g>
  </svg>
);
export default SvgDesc;
