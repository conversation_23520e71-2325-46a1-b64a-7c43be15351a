import * as React from 'react';
import type { SVGProps } from 'react';
const SvgIntegrateRightArrow = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    viewBox="0 0 10.749975204467773 9.999975204467773"
    className="anticon"
    focusable="false"
    width="1em"
    height="1em"
    aria-hidden="true"
    {...props}
  >
    <path
      d="M5.21967,0.219668C5.51256,-0.0732226,5.98744,-0.0732226,6.28033,0.219668C6.28033,0.219668,10.5303,4.46967,10.5303,4.46967C10.8232,4.76256,10.8232,5.23744,10.5303,5.53033C10.5303,5.53033,6.28033,9.7803,6.28033,9.7803C5.98744,10.0732,5.51256,10.0732,5.21967,9.7803C4.92678,9.4874,4.92678,9.0126,5.21967,8.7197C5.21967,8.7197,8.1893,5.75,8.1893,5.75Q8.1893,5.75,0.75,5.75Q0,5.41421,0,5C0,4.58579,0.33579,4.25,0.75,4.25C0.75,4.25,8.1893,4.25,8.1893,4.25C8.1893,4.25,5.21967,1.28033,5.21967,1.28033C4.92678,0.987438,4.92678,0.512558,5.21967,0.219668C5.21967,0.219668,5.21967,0.219668,5.21967,0.219668Z"
      fillRule="evenodd"
      fill="currentColor"
      fillOpacity={1}
    />
  </svg>
);
export default SvgIntegrateRightArrow;
