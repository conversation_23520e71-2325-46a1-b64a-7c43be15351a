import * as React from 'react';
import type { SVGProps } from 'react';
const SvgCrashTag = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    viewBox="0 0 16 16"
    className="anticon"
    focusable="false"
    width="1em"
    height="1em"
    aria-hidden="true"
    {...props}
  >
    <defs>
      <clipPath id="master_svg0_3125_27467">
        <path d="M0 0H16V16H0z" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_3125_27467)">
      <path
        d="M2.5,7.775L2.5,2.75C2.5,2.6837,2.5263400000000003,2.62011,2.57322,2.57322C2.62011,2.5263400000000003,2.6837,2.5,2.75,2.5L7.775,2.5C7.80785,2.49994,7.84039,2.50636,7.87077,2.5188800000000002C7.90114,2.53141,7.92874,2.5498000000000003,7.952,2.573L14.202,8.823C14.2253,8.846219999999999,14.2438,8.873809999999999,14.2564,8.90418C14.269,8.934560000000001,14.2754,8.967120000000001,14.2754,9C14.2754,9.03288,14.269,9.06544,14.2564,9.09582C14.2438,9.12619,14.2253,9.15378,14.202,9.177L9.177,14.202C9.15378,14.2253,9.12619,14.2438,9.09582,14.2564C9.06544,14.269,9.03288,14.2754,9,14.2754C8.967120000000001,14.2754,8.934560000000001,14.269,8.90418,14.2564C8.873809999999999,14.2438,8.846219999999999,14.2253,8.823,14.202L2.573,7.952C2.5498000000000003,7.92874,2.53141,7.90114,2.5188800000000002,7.87077C2.50636,7.84039,2.49994,7.80785,2.5,7.775ZM1,7.775L1,2.75C1,1.784,1.784,1,2.75,1L7.775,1C8.239,1,8.684999999999999,1.184,9.013,1.513L15.263,7.763C15.5909,8.091149999999999,15.7751,8.53609,15.7751,9C15.7751,9.46391,15.5909,9.90885,15.263,10.237L10.237,15.263C9.90885,15.5909,9.46391,15.7751,9,15.7751C8.53609,15.7751,8.091149999999999,15.5909,7.763,15.263L1.513,9.013C1.35035,8.85047,1.22133,8.65747,1.1333,8.44505C1.04528,8.23263,0.9999830127,8.004940000000001,1,7.775ZM6,5C5.73478,5,5.48043,5.10536,5.29289,5.29289C5.10536,5.48043,5,5.73478,5,6C5,6.26522,5.10536,6.51957,5.29289,6.70711C5.48043,6.89464,5.73478,7,6,7C6.26522,7,6.51957,6.89464,6.70711,6.70711C6.89464,6.51957,7,6.26522,7,6C7,5.73478,6.89464,5.48043,6.70711,5.29289C6.51957,5.10536,6.26522,5,6,5Z"
        fillRule="evenodd"
        fill="currentColor"
        fillOpacity={1}
      />
    </g>
  </svg>
);
export default SvgCrashTag;
