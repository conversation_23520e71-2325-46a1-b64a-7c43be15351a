import * as React from 'react';
import type { SVGProps } from 'react';
const SvgItemIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    fill="none"
    viewBox="0 0 16 16"
    className="anticon"
    focusable="false"
    aria-hidden="true"
    width="1em"
    height="1em"
    {...props}
  >
    <path
      d="M0,1.75C0,0.783502,0.783502,0,1.75,0C1.75,0,5.25,0,5.25,0C6.2165,0,7,0.783502,7,1.75C7,1.75,7,5.25,7,5.25C7,6.2165,6.2165,7,5.25,7C5.25,7,4,7,4,7C4,7,4,11,4,11C4,11.5523,4.44772,12,5,12C5,12,9,12,9,12C9,12,9,10.75,9,10.75C9,9.7835,9.7835,9,10.75,9C10.75,9,14.25,9,14.25,9C15.2165,9,16,9.7835,16,10.75C16,10.75,16,14.25,16,14.25C16,15.2165,15.2165,16,14.25,16C14.25,16,10.75,16,10.75,16C9.7835,16,9,15.2165,9,14.25C9,14.25,9,13.5,9,13.5C9,13.5,5,13.5,5,13.5C3.61929,13.5,2.5,12.3807,2.5,11C2.5,11,2.5,7,2.5,7C2.5,7,1.75,7,1.75,7C0.783502,7,0,6.2165,0,5.25C0,5.25,0,1.75,0,1.75C0,1.75,0,1.75,0,1.75ZM1.75,1.5C1.61193,1.5,1.5,1.61193,1.5,1.75C1.5,1.75,1.5,5.25,1.5,5.25C1.5,5.38807,1.61193,5.5,1.75,5.5C1.75,5.5,5.25,5.5,5.25,5.5C5.38807,5.5,5.5,5.38807,5.5,5.25C5.5,5.25,5.5,1.75,5.5,1.75C5.5,1.61193,5.38807,1.5,5.25,1.5C5.25,1.5,1.75,1.5,1.75,1.5C1.75,1.5,1.75,1.5,1.75,1.5ZM10.75,10.5C10.6119,10.5,10.5,10.6119,10.5,10.75C10.5,10.75,10.5,14.25,10.5,14.25C10.5,14.3881,10.6119,14.5,10.75,14.5C10.75,14.5,14.25,14.5,14.25,14.5C14.3881,14.5,14.5,14.3881,14.5,14.25C14.5,14.25,14.5,10.75,14.5,10.75C14.5,10.6119,14.3881,10.5,14.25,10.5C14.25,10.5,10.75,10.5,10.75,10.5C10.75,10.5,10.75,10.5,10.75,10.5Z"
      fillRule="evenodd"
      fill="currentColor"
      fillOpacity={1}
    />
  </svg>
);
export default SvgItemIcon;
