.build-status-RUNNING{
  // color: var(--mc-color-success);
  color: var(--mc-green);
}

.build-status-SKIP_SUCCEEDED{
  // color: var(--mc-color-success);
  color: var(--mc-green);
}

.build-status-SUCCEEDED{
  // color: var(--mc-color-success);
  color: var(--mc-green);
}

.build-status-TIMEOUT{
  color: var(--mc-color-warning);
}

.build-status-FAILED{
  color: var(--mc-color-error);
}

.build-status-SKIPPED{
  color: var(--mc-color-warning);
}

.build-status-CANCELLED{
  color: var(--mc-color-text-secondary);
}

.build-status-WAITING{
  color: var(--mc-color-text-secondary);
}

.build-status-WARNING{
  color: var(--mc-color-warning);
}

.build-status-SKIP{
  // color: var(--mc-color-success);
  color: var(--mc-green);
}

.build-status-PASS{
  // color: var(--mc-color-success);
  color: var(--mc-green);
}

.build-status-NOT_PASS{
  color: var(--mc-color-error);
}

.buildStatusIconWrap{
  gap:var(--mc-margin-xs);
  font-weight: 500;
}