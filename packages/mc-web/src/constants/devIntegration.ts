export const STATUS_TYPE_ICON: Record<string, { color: string; label: string; textColor: string }> = {
  FIRST_INTG_WAIT_APPROVAL: {
    label: '首次集成等待审批',
    textColor: 'colorWarningText',
    color: 'gold',
  },
  FIRST_INTG_APPROVAL_REJECT: {
    label: '首次审批被驳回',
    textColor: 'colorErrorText',
    color: 'warning',
  },
  FIRST_INTG_APPROVAL_CANCELED: {
    label: '首次集成审批取消',
    textColor: 'colorTextSecondary',
    color: 'default',
  },
  INIT: {
    label: '待提交集成',
    textColor: 'colorInfoText',
    color: 'processing',
  },
  EMERGENT_WAIT_APPROVAL: {
    label: '等待审批',
    textColor: 'colorInfoText',
    color: 'processing',
  },
  EMERGENT_APPROVAL_REJECT: {
    label: '审批驳回',
    textColor: 'colorErrorText',
    color: 'error',
  },
  EMERGENT_APPROVAL_CANCELED: {
    label: '审批撤销',
    textColor: 'colorTextSecondary',
    color: 'default',
  },
  INTEGRATE_TO_AREA_SUCCESS: {
    label: '集成成功',
    textColor: 'colorSuccessText',
    color: 'success',
  },
  INTEGRATE_TO_AREA_FAIL: {
    label: '集成失败',
    textColor: 'colorErrorText',
    color: 'error',
  },
  CLOSED: {
    label: '已关闭',
    textColor: 'colorTextSecondary',
    color: 'default',
  },
  CANCELED: {
    label: '取消集成',
    textColor: 'colorTextSecondary',
    color: 'default',
  },
};

export const ALTER_STATUS: Record<string, { label: string; color: string }> = {
  OPENED: {
    label: '开启',
    color: 'processing',
  },
  TEST_SUBMITTED: {
    label: '已提测',
    color: 'success',
  },
  INTEGRATED: {
    label: '已集成',
    color: 'success',
  },
  BETA: {
    label: '灰度中',
    color: 'processing',
  },
  PUBLISHING: {
    label: '正式发布中',
    color: 'processing',
  },
  PUBLISHED: {
    label: '已发布',
    color: 'success',
  },
  CLOSED: {
    label: '关闭',
    color: 'default',
  },
  ONGOING: {
    label: '进行中',
    color: 'processing',
  },
};

export const ALTER_TYPE: {
  [key: string]: string;
} = {
  SOURCE: '源码依赖',
  BINARY: '外部依赖',
};

export const ALTER_MODE: {
  [key: string]: string;
} = {
  ADD: '新增依赖',
  ADD_COMPILE: '新增主bundle的依赖或者awb',
  ADD_FORCE: '新增版本管理，适合awb中的依赖',
  ADD_EXCLUDE: '新增依赖排除，适合依赖下线',
  UPDATE: '修改版本',
  DELETE: '删除依赖',
  CHANGE2COMPILE: '修改为主bundle依赖',
  CHANGE2FORCE: '修改为版本管理，适合从主bundle移出依赖',
  UPDATE_AS_ADD_COMPILE: '修改版本',
  UPDATE_AS_ADD_FORCE: '修改版本',
};

export const VERSION_COMPARE_RESULT: Record<string, string> = {
  HIGHER: '高于',
  EQUAL: '等于',
  LOWER: '低于',
  UNABLE_TO_COMPARE: '无法比较',
};

export const BRANCH_MODEL_TYPE: Record<string, string> = {
  INTEGRATION: '集成分支模式',
  FREE: '自由分支模式',
};
