# @ali/mc-web

## 1.10.21

### Patch Changes

- Updated dependencies
  - @ali/mc-services@1.5.6
  - @ali/mc-uikit@0.5.6

## 1.10.20

### Patch Changes

- update
- Updated dependencies
  - @ali/mc-services@1.5.5
  - @ali/mc-uikit@0.5.5

## 1.10.19

### Patch Changes

- fix: 修复新增模块弹窗未重置状态导致alterMode接口返回慢的情况下变更模式字段异常问题

## 1.10.18

### Patch Changes

- Updated dependencies
  - @ali/mc-services@1.5.4
  - @ali/mc-uikit@0.5.4

## 1.10.17

### Patch Changes

- fix: 修复ai tag不展示的问题

## 1.10.16

### Patch Changes

- feat: 4月产品文档更新

## 1.10.15

### Patch Changes

- feat: 4月产品文档更新

## 1.10.14

### Patch Changes

- feat: aicr强卡模块未处理提醒

## 1.10.13

### Patch Changes

- chore: 修改强弱标签位子&&命中方法上增加强弱标签展示
  feat: AI CR 618规则强制上报

## 1.10.12

### Patch Changes

- fix: 修复部分没有团队空间的引导

## 1.10.11

### Patch Changes

- feat: 问题列表【操作历史记录】优化需求，更新至【问题进展】

## 1.10.10

### Patch Changes

- feat: aicr卡口审批修改
  feat: 命中的规则强弱标签显示在方法卡片上

## 1.10.9

### Patch Changes

- fix: 修复删除模块归属项目报错的问题

## 1.10.8

### Patch Changes

- fix: 外部依赖不显示源码构建

## 1.10.7

### Patch Changes

- fix: 贡献余额值为deltaSize取反

## 1.10.6

### Patch Changes

- feat: 3 月产品更新

## 1.10.5

### Patch Changes

- fix: 动态化迭代详情链接问题修正

## 1.10.4

### Patch Changes

- fix: 提测验证人默认使用迭代测试负责人

## 1.10.3

### Patch Changes

- c9726bd: fix: 修复发布工作台测试回归包合包标展示不对的问题

## 1.10.2

### Patch Changes

- Updated dependencies
  - @ali/mc-services@1.5.3
  - @ali/mc-uikit@0.5.3

## 1.10.1

### Patch Changes

- fix: 优化AI CR强卡口

## 1.10.0

### Minor Changes

- feat: 支持AI CR强卡口流程

## 1.9.8

### Patch Changes

- fix: 复制迭代报错

## 1.9.7

### Patch Changes

- fix: 修复集成区取值错误问题

## 1.9.6

### Patch Changes

- fix: 修复关联模块源分支无法修改问题；增加构建失败风险标签

## 1.9.5

### Patch Changes

- fix: 样式优化

## 1.9.4

### Patch Changes

- fix: 修复一些问题

## 1.9.3

### Patch Changes

- fix: 修复需求绑定模块集成单编辑问题&&部分样式问题

## 1.9.2

### Patch Changes

- fix：修正 useDeferData 引入问题

## 1.9.1

### Patch Changes

- fix: 修正 useDeferData 导入问题

## 1.9.0

### Minor Changes

- feat: 测试报告迁移
  feat: 质量性能防劣化入口

## 1.8.11

### Patch Changes

- fix: 修复模块创建名称规则校验
  refactor: 优化全局抽屉样式
- Updated dependencies
  - @ali/mc-uikit@0.5.2

## 1.8.10

### Patch Changes

- fix: 修复 integrateAreaStatusMap 组件迁移问题

## 1.8.9

### Patch Changes

- feat: 应用发布列表页面迁移
  fix: 治理报告问题修复

## 1.8.8

### Patch Changes

- fix: 产物展示优化

## 1.8.7

### Patch Changes

- 6f25af1: fix: 跟版集成列表状态异常修复；提测表单多次更新问题修复

## 1.8.6

### Patch Changes

- Updated dependencies
  - @ali/mc-services@1.5.2
  - @ali/mc-uikit@0.5.1

## 1.8.5

### Patch Changes

- fix: 运行中的任务定时请求日志;
  fix: 修复发布正式版构建成功后自动提交集成，集成节点数据未刷新的问题;
  fix: 自由构建-构建弹窗内集成区无法搜索问题;
  fix: 自由构建-版本计划排序;
  fix: 迭代左侧增加集成区状态标签;
  fix: 产物展示优化&产物展示组件调整;
  fix: 运行日志弹窗内展示模块名;
  fix: 提测表单默认值逻辑补充;
  fix: 删除bugfix分支标签的展示;
  fix: md5支持复制;
  fix: 集成节点增加无需构建提示;
  fix: 修复代码漏带风险标签请求未发起问题;
  fix: 卡口检测执行状态和检测状态展示；卡口检测时间为空时相关展示采用-占位;
  fix: 增加【迭代ID】标签；发布版本依赖链接修改;
  fix: 添加模块弹窗滚动到错误字段;

## 1.8.4

### Patch Changes

- feat: 修复提交编辑集成单报错问题

## 1.8.3

### Patch Changes

- fix: 替换AddonSelect组件以及样式优化'

## 1.8.2

### Patch Changes

- fix: 修复编辑器Header不展示的问题

## 1.8.1

### Patch Changes

- feat: 效能洞察-治理报告P1P2部分
  fix: 修复需求关联模块线上问题

## 1.7.2

### Patch Changes

- fix: 依赖详情搜索&提测表单大小写转换问题'

## 1.7.1

### Patch Changes

- feat: 2月产品更新链接

## 1.7.0

### Minor Changes

- 417c52c: feat: 迭代需求关联到模块

## 1.6.4

### Patch Changes

- fix: 自主验证集成阶段创建跟版迭代报错问题修;
  fix: 修复工作台迭代列表切换tab集成标签缓存问题;
  fix: 默认展开有构建记录的流水线;
  fix: 非动态发布不展示代码回流设置;
  fix: 修复迭代设置内关闭按钮样式异常问题;
  fix: 提测模块弹窗样式修复;
  fix: 修复复制scmtag丢失问题;
  fix: 已关联的模块增加已关联提示，仍在下拉列表中展示；
  fix: 关联模块失败不关闭弹窗;
  fix: 依赖详情版本号支持复制;
  fix: 自主验证迭代卡口没通过允许提测（和跟版迭代对齐);
  fix: 动态化迭代集成分支接口;

## 1.6.3

### Patch Changes

- fix: 基础依赖tab-集成区无法搜索的问题/下拉框展开时标签太显眼;
  fix: 构建任务详情固定打开整包tab;
  fix: 工作台-构建记录和迭代列表处理mtl4迭代和uniapp迭代，跳转到老迭代页;
  fix: 修复代码评审抽屉表单数据不更新问题;
  fix: 构建记录根据url参数定位;
  fix: 构建任务执行中文案颜色;

## 1.6.2

### Patch Changes

- feat(效能洞察): 优化一些问题

## 1.6.1

### Patch Changes

- Updated dependencies [08e09fc]
  - @ali/mc-uikit@0.4.2

## 1.4.9

### Patch Changes

- Updated dependencies [1645a2e]
  - @ali/mc-services@1.5.1
  - @ali/mc-uikit@0.4.1

## 1.4.8

### Patch Changes

- fix: 修复跳转链接异常的问题

## 1.4.7

### Patch Changes

- fix: 增加字段非空展示逻辑
- fix: 修复集成参数错误问题
- fix: 修复CommitPopover组件展示异常
- fix: 模块搜索增加depKey匹配

## 1.4.6

### Patch Changes

- fix: 回滚迭代详情切回旧版本入口

## 1.4.5

### Patch Changes

- 04ddbf7: feat: 抽象迭代工作流页面的基础组件：全局可折叠侧边栏、迭代阶段、详情侧边栏
  fix: 修复复制迭代丢失mainModuleId导致模块不一致的问题
- Updated dependencies [04ddbf7]
  - @ali/mc-uikit@0.4.0

## 1.4.4

### Patch Changes

- feat: AI CR功能优化

## 1.4.3

### Patch Changes

- fix: 链接替换为Link组件;
  fix: 修复更多产物弹窗入口文字的样式;
  fix: 运行日志抽屉-默认定位到展开的记录;
  fix: 构建弹窗-未选择模块或整包构建时禁止构建;
  fix: 提取并替换公共的依赖类型枚举;
  fix: 构建弹窗-未选择构建模版时抛出错误提示并滚动到对应位置;

## 1.4.2

### Patch Changes

- fix:修复动态化变更单构建任务展示异常

## 1.4.1

### Patch Changes

- dd08797: feat: 源码依赖新增提交CR按钮

## 1.4.0

### Minor Changes

- 91d23c5: feat: 源码构建引导
  fix: 修复 AI 部分接口 Swagger Schema 的兼容性问题
  feat: 1月文档更新 && 图片更新

## 1.3.3

### Patch Changes

- feat: 仅初始化且不存在数据时，收起拓扑图

## 1.3.2

### Patch Changes

- hotfix: 1.迭代详情只有源码依赖展示新增feature能力 2.优化topo图因方法太多而不展示的提示文案

## 1.3.1

### Patch Changes

- - fix: 非严格模式下useEffect只执行一次，导致useEffect里面设置的pageTitle被ice的definePageConfig覆盖；
    feat: 新增自动编排流水线的状态标签;
    feat: 新增外网链接的入口；
    fix: 修复流水线任务明细不跟随节点刷新的问题；
    fix: 修复关联aone组件创建人字段取值错误导致的筛选异常问题；
    fix: pipeline路由改为pipelines以跟老版作区分；

## 1.3.0

### Minor Changes

- df5f0b4: feat: AI CR

### Patch Changes

- Updated dependencies [df5f0b4]
  - @ali/mc-services@1.5.0
  - @ali/mc-icons@1.4.0
  - @ali/mc-uikit@0.3.4

## 1.2.2

### Patch Changes

- 8f9c46b: feat: 模块包构建错误日志展示&&ai助手分析功能同步老mc
  feat: 需求审核增加组织架构分组功能
  fix: AI 效率卡片展示逻辑修正
- fdec964: feat: 模块包构建错误日志展示&&ai助手分析功能同步老mc&&修复bug
  feat: 需求审核增加组织架构分组功能

## 1.2.1

### Patch Changes

- fix: 修复自由构建场景新建迭代版本计划取值错误问题&构建时基础依赖变化同步更新整包构建分支'

## 1.0.28

### Patch Changes

- fix: 修正新增或编辑时模块的名称校验规则
  fix: 团队空间模块资产切换数据不一致问题修正
  fix: 版本发布钉钉群创建条件调整

## 1.0.27

### Patch Changes

- fix: bugfix

## 1.0.26

### Patch Changes

- 29ccdb8: feat: 效能洞察二期
  feat: 12 月产品更新文档
  bugfix: VersionPlanSelect 数据初始化刷新问题修正，解决应用无版本计划时重复刷新的问题

## 1.0.25

### Patch Changes

- Updated dependencies [109bf3e]
  - @ali/mc-services@1.4.0
  - @ali/mc-uikit@0.3.3

## 1.0.24

### Patch Changes

- a52ce25: fix: 创建迭代修复并行迭代展示问题
  fix: 发布工作台需求审核导出excel为全部数据

## 1.0.23

### Patch Changes

- fix: 修复新建动态发布迭代的目标客户端版本列表接口错误的问题

## 1.0.22

### Patch Changes

- e1c7d37: feat: 发布工作台大促需求管控

## 1.0.21

### Patch Changes

- 744502d: feat: 【动态化】迭代信息提醒
  fix: 测试节点-结果反馈按钮位置修改及结果反馈弹窗内样式调整

## 1.0.20

### Patch Changes

- fix: 新建迭代无法取到团队空间信息导致流程阻塞
  fix: 模块发布正式版构建参数错误修复

## 1.0.19

### Patch Changes

- fix: 修复构建弹窗内无需构建提示方法的依赖项过多导致非必要更新的问题

## 1.0.18

### Patch Changes

- fix:提交测试stepId取值错误修复

## 1.0.17

### Patch Changes

- e3462d3: fix: 迭代详情添加模块报错问题修正

## 1.0.16

### Patch Changes

- fix(团队包大小): 申请额度审批失败时展示历史审批记录+提交按钮

## 1.0.15

### Patch Changes

- cfe5b65: fix: 修正满需求轨迹页面数组渲染的key取值问题

## 1.0.14

### Patch Changes

- 77159b3: feat: 发布工作台增加支持舆情平台跳转
  fix: 模块创建选择团队空间判断变更
  fix: 构建日志弹窗样式异常修正
  fix：迭代详情关联模块按钮变更文案修正

## 1.0.13

### Patch Changes

- Updated dependencies [c2299e0]
  - @ali/mc-icons@1.3.0
  - @ali/mc-uikit@0.3.2

## 1.0.12

### Patch Changes

- 85032b4: feat: 单模块多人持续集成判断产物Commit ID避免老代码覆盖新代码
  fix: 提交测试迭代选择id提交错误修复
  fix: 团队空间跳转问题修正

## 1.0.11

### Patch Changes

- feat: 新增模块无需构建提示

## 1.0.10

### Patch Changes

- fix: 代码合并接口错误修复

## 1.0.9

### Patch Changes

- 707e7c6: feat: 发布工作台废弃发布单列表内隐藏
  feat: 腹部工作台集成明细页面

## 1.0.8

### Patch Changes

- d3973d6: fix: 更多产物弹窗内复制下载链接带上域名

## 1.0.7

### Patch Changes

- 87727a6: fix: 工作台迭代列表数据加载问题修正

## 1.0.6

### Patch Changes

- 【hotfix】包含子模块的模块编辑弹窗数据异常问题修复&模块列表操作相关代码优化&

## 1.0.5

### Patch Changes

- fix: 构建模版空数据校验

## 1.0.4

### Patch Changes

- 2259800: fix: 修复构建后没有定时刷新和编辑流水线页面返回按钮链接失效问题

## 1.0.3

### Patch Changes

- 0af18ee: fix: 修正发版工作台提交问题参数缺失的问题

## 1.0.2

### Patch Changes

- 9ad17f4: fix: 修改代码漏带风险标签的展示条件

## 1.0.1

### Patch Changes

- 801a6fe: fix: 迭代复制报错
- Updated dependencies [d383162]
  - @ali/mc-services@1.3.1
  - @ali/mc-uikit@0.3.1

## 0.9.4

### Patch Changes

- feat: 11 月产品更新文档

## 0.9.2

### Patch Changes

- fix: 研发效能 bugfix
  feat：研发效能视觉稿还原

## 0.9.1

### Patch Changes

- fix: 修正工作台切换App时,未刷新版本计划数据导致跳转发布工作台对对应版本计划错误的问题

## 0.9.0

### Minor Changes

- feat: [发布工作台] 问题跟进列表 与我相关筛选
  feat: [发布工作台] 问题反馈在问题列表里支持二次编辑
  feat: [发布工作台] 问题处理状态增加选项

## 0.8.3

### Patch Changes

- ac249eb: feat: 十月产品更新

## 0.8.2

### Patch Changes

- fix: 修复一些问题

## 0.8.1

### Patch Changes

- fix: 修复App发布洞察团队空间下钻链接被隐藏的问题'

## 0.8.0

### Minor Changes

- 5759a5a: feat: 研发效能度量

## 0.7.1

### Patch Changes

- fix: 删除不用的代码

## 0.7.0

### Minor Changes

- feat(效能优化): 1.优化历史 2.优化建议反馈

### Patch Changes

- Updated dependencies
  - @ali/mc-services@1.3.0
  - @ali/mc-icons@1.2.0
  - @ali/mc-uikit@0.3.0

## 0.6.19

### Patch Changes

- fix: ak/sk复制文案修正
  feat：问卷弹窗临时需求

## 0.6.18

### Patch Changes

- fix: 修复包大小余额申请的问题

## 0.6.17

### Patch Changes

- 7a3b02a: fix: 修复申请额度弹窗的取值错误问题

## 0.6.16

### Patch Changes

- fix: 临时修复发布工作台问题列表的标题换行问题,目前扩展及操作区域是占用横向空间的,标题无法直接通栏平铺
- Updated dependencies
- Updated dependencies [a1846cf]
  - @ali/mc-uikit@0.2.7
  - @ali/mc-services@1.2.6

## 0.6.15

### Patch Changes

- fix: 修正 LocalCache.get 获取数据时，key生成错误导致读取空数据的问题'
- Updated dependencies
  - @ali/mc-services@1.2.5
  - @ali/mc-uikit@0.2.6

## 0.6.14

### Patch Changes

- hotfix: 简化 LocalCache 实现，修复当缓存的条目数量超过设置限制时死循环的问题
- Updated dependencies
  - @ali/mc-services@1.2.4
  - @ali/mc-uikit@0.2.5

## 0.6.13

### Patch Changes

- feat: 优化效能优化页的体验问题
- Updated dependencies
  - @ali/mc-services@1.2.3
  - @ali/mc-uikit@0.2.4

## 0.6.12

### Patch Changes

- fix: 问题反馈成功展示了全部的问题
  fix: 舆情查看图片图标调整
  fix: 发布工作台包下载的二维码放大
  refactor: 首页工作台优化,统一样式实现，简化页面结构

## 0.6.11

### Patch Changes

- fix: 修复 toLocaleLowerCase 没有空值判断的问题

## 0.6.10

### Patch Changes

- a3ef262: - 修复首页跳转工作台时接口重复请求的问题
  - 优化工作台集成记录列表加载更多按钮的展示逻辑

## 0.6.9

### Patch Changes

- fix: 修正没有正常导入 definePageConfig 的问题

## 0.6.8

### Patch Changes

- 5c4a78e: fix: 修正首页工作台版本计划选择器条目问题'

## 0.6.6

### Patch Changes

- 0e77b30: feat(AI二期): 1. 项目包大小增加对比版本 2.卡口执行详情增量变化增加对比版本 3.优化一些问题
- Updated dependencies [0e77b30]

  - @ali/mc-services@1.2.2
  - @ali/mc-icons@1.1.1
  - @ali/mc-uikit@0.2.3

- 5de8298: - feat: 发布工作台创建问题支持绑定 Aone
  - feat：发布工作台问题创建增加新的问题类型
  - refactor: 优化发布工作台数据初始化和更新加载逻辑

## 0.6.5

### Patch Changes

- fix: 修复全局搜索快捷键问题'

## 0.6.4

### Patch Changes

- 7e75dbe: fix: 版本问题反馈兼容问题处理

## 0.6.2

### Patch Changes

- 1. 更新前端监控配置 2.优化一些问题
- Updated dependencies
  - @ali/mc-uikit@0.2.2

## 0.6.1

### Patch Changes

- feat: 8月产品更新

## 0.5.9

### Patch Changes

- a4e41b0: fix: 修正在老版本兼容页面跳转工作台首页失败的问题

## 0.5.8

### Patch Changes

- 495c6de: - fix: 新版兼容问题修正
- ab7045a6: - fix: 回归包标题过长布局问题
- 29f60c3f: - fix: 待办url跳转

## 0.5.6

### Patch Changes

- cbb01a2: fix: 修正复制的 aksk 数据错误的问题

## 0.5.5

### Patch Changes

- 86ff6e4: feat: AI二期优化

## 0.5.4

### Patch Changes

- 1b103e5: - fix: 修正 copy-to-clipboard 的API是常用错误倒是复制失败的问题

## 0.5.3

### Patch Changes

- Updated dependencies [48eb049]
  - @ali/mc-services@1.2.1
  - @ali/mc-uikit@0.2.1

## 0.5.2

### Patch Changes

- fix: 修正发布工作台问题反馈空数据过滤

## 0.5.1

### Patch Changes

- ddb6e0c: fix: 修正发布工作台其他类型任务不通过不生效的问题'

## 0.5.0

### Minor Changes

- a0d2521: AI 二期：依赖图谱

## 0.4.3

### Patch Changes

- 24cc06b: hotfix: 发布工作台线上问题修正

## 0.4.2

### Patch Changes

- 9df91e9: hotfix: 新版本工作台新版本问题修正

## 0.4.1

### Patch Changes

- 902f933: fix: 修正老板资源加载的环境判断问题

## 0.4.0

### Minor Changes

- c92282f: 首页工作台 & 发布工作台

### Patch Changes

- Updated dependencies [228ccf6]
- Updated dependencies [c92282f]
  - @ali/mc-icons@1.1.0
  - @ali/mc-services@1.2.0
  - @ali/mc-uikit@0.2.0

## 0.3.5

### Patch Changes

- f66d632: 1. 优化建议详情页Empty状态新增可优化空间说明 2. 团队包大小表格位置调整 3. 修复代码优化建议页选择器的一些问题

## 0.3.4

### Patch Changes

- bdf621b: 1. 优化建议详情页Empty状态文案修改 2. 修复线上空间选择器权限校验未生效的问题

## 0.3.3

### Patch Changes

- 3cecaa3: 包大小 AI 建议一期优化

## 0.3.2

### Patch Changes

- 98ac6da: hotfix

## 0.3.1

### Patch Changes

- 2cf44c0: Tab切换参数丢失问题修正

## 0.3.0

### Minor Changes

- 6ad3a8a: 包大小 AI 建议
