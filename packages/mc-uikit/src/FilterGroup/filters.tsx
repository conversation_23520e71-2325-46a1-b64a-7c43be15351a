import React from 'react';
import { Flex, Space, Typography } from 'antd';
import { trim, isNil } from 'lodash-es';

import { User as IUser } from '@ali/mc-services';
import { fetchUserByKeyword } from '@ali/mc-services/User';

import User from '../User';
import { FilterOptionType } from './OptionRender';

export async function fetchUsers(keyword: string): Promise<FilterOptionType[]> {
  if (isNil(keyword) || trim(keyword).length === 0) {
    return Promise.resolve([] as FilterOptionType[]);
  }

  return fetchUserByKeyword({
    keyword,
    count: 5,
  }).then((users: IUser[]) => {
    const items: FilterOptionType[] = [];

    if (users?.length > 0) {
      users.forEach((item) => {
        items.push({
          title: item.nickName,
          label: (
            <Flex vertical align="flex-start" gap={4} key={item.empId}>
              <Space size="small" align="center" wrap>
                <User showAvatar empIds={item.empId as string} />
                <Typography.Text type="secondary">({item.empId})</Typography.Text>
              </Space>
              <Typography.Text
                style={{ width: 298 }}
                type="secondary"
                ellipsis={{
                  tooltip: true,
                }}
              >
                {item.bu}
              </Typography.Text>
            </Flex>
          ),
          value: item.empId as string,
          data: item,
        } as FilterOptionType);
      });
    }

    return items;
  });
}