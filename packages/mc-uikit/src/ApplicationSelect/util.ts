import { ApiResponse, ApplicationBO, AuthorityRes } from '@ali/mc-services';

export function parseOption(app: ApplicationBO, permission: boolean | ApiResponse<AuthorityRes | boolean>) {
  return {
    key: app.id,
    label: app.name,
    value: app.id,
    disabled:
      app.isDeleted ||
      (app.type !== 'UNI_APP_CLIENT' &&
        (app.privacyRecordStatus === 'UNCOMPLETED' || !app.currentAppkey || !app.appkey)),
    application: app,
    permission,
  };
}