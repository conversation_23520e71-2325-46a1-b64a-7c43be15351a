import dayjs from 'dayjs';
import durationPlugin from 'dayjs/plugin/duration';
import { padStart } from 'lodash-es';

// 初始化dayjs插件
dayjs.extend(durationPlugin);
// import relativeTime from 'dayjs/plugin/relativeTime' // ES 2015

export const getTimeStr = (origin: Date | string, fmt = 'YYYY-MM-DD HH:mm:ss') => {
  let timeStr = '';
  if (!origin) {
    return '-';
  }

  try {
    timeStr = dayjs(origin)
      .format(fmt);
    if (timeStr === 'Invalid date') {
      timeStr = '';
    }
  } catch (error) {
    console.error(error);
  }
  return timeStr;
};

export function getTimeInterval(endTime: string, startTime: string, format?: string): string {
  // 解析时间
  const endDate = dayjs(endTime);
  const startDate = dayjs(startTime);

  // 获取间隔duration
  const duration = dayjs.duration(endDate.diff(startDate));

  // 如果提供了自定义格式，则使用该格式
  const years = String(duration.years());
  const months = String(duration.months());
  const days = String(duration.days());
  const hours = String(duration.hours());
  const minutes = String(duration.minutes());
  const seconds = String(duration.seconds());
  const milliseconds = String(duration.milliseconds());

  if (format) {
    const REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g
    const matches: Record<string, string>= {
      Y: years,
      YY: padStart(years, 2, '0'),
      YYYY: padStart(years, 4, '0'),
      M: months,
      MM: padStart(months, 2, '0'),
      D: days,
      DD: padStart(days, 2, '0'),
      H: hours,
      HH: padStart(hours, 2, '0'),
      m: minutes,
      mm: padStart(minutes, 2, '0'),
      s: seconds,
      ss: padStart(seconds, 2, '0'),
      SSS: padStart(milliseconds, 3, '0')
    }

    let lastOffset = 0;
    while (REGEX_FORMAT.test(format)) {
      lastOffset = REGEX_FORMAT.lastIndex;
    }
    let prevIsZero = false;
    return format.replace(REGEX_FORMAT, (match, $1, offset) => {
      const matched = matches[match];

      // 为 0 时不展示的单位
      const isLast = offset + match.length === lastOffset;
      if (prevIsZero) {
        prevIsZero  = /^0+$/.test(matched ?? '');
        // console.log(`match: ${match}, $1: ${$1}, isLast: ${isLast}, str: ${str}, matched: ${matched}`);
        if (match && isLast) {
          return $1;
        }
        return '';
      }

      prevIsZero  = /^0+$/.test(matched ?? '');

      // console.log(`...match: ${match}, $1: ${$1}, prevIsZero: ${prevIsZero}, isLast: ${isLast}, str: ${str}, matched: ${matched}`);
      return $1 || ((!prevIsZero) ? matched : '');
    });
  }

  // 格式化结果
  if (duration.days() > 0) {
    return `${duration.days()} 天 ${duration.hours()} 小时 ${duration.minutes()} 分 ${duration.seconds()}秒`;
  } else if (duration.hours() > 0) {
    return `${duration.hours()} 小时 ${duration.minutes()} 分 ${duration.seconds()} 秒`;
  } else if (duration.minutes() > 0) {
    return `${duration.minutes()} 分 ${duration.seconds()} 秒`;
  } else {
    return `${duration.seconds()} 秒`;
  }
}
