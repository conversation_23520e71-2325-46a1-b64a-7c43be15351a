import * as React from 'react';
import type { SVGProps } from 'react';
const SvgLogoBlack = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={27.999} height={28} fill="none" {...props}>
    <defs>
      <linearGradient id="logo_black_svg__a" x1={0.742} x2={0.023} y1={0.17} y2={0.615}>
        <stop offset="0%" stopColor="#1F2328" stopOpacity={0.95} />
        <stop offset="40.025%" stopColor="#1F2328" />
      </linearGradient>
      <linearGradient id="logo_black_svg__b" x1={0.308} x2={0.974} y1={0.15} y2={0.616}>
        <stop offset="0%" stopColor="#1F2328" stopOpacity={0.95} />
        <stop offset="39.757%" stopColor="#1F2328" />
      </linearGradient>
    </defs>
    <path fill="#1F2328" d="M4.027 4.093A14.2 14.2 0 0 1 14 .013c3.74 0 7.328 1.468 9.972 4.08L14 13.94z" />
    <path
      fill="#FFF"
      fillRule="evenodd"
      d="M13.99 13.948 4.008 4.093l.01-.01q1.985-1.96 4.58-3.022Q11.19 0 14 0q2.807 0 5.402 1.061 2.594 1.062 4.58 3.023l.009.01L14 13.956zM4.046 4.093 14 13.92l9.953-9.827q-1.978-1.95-4.561-3.007Q16.802.026 14 .026t-5.393 1.06Q6.024 2.143 4.046 4.093"
    />
    <path
      fill="url(#logo_black_svg__a)"
      fillRule="evenodd"
      d="m4.028 4.103-.376.394v-.002a13.7 13.7 0 0 0 .557 19.193l5.817-5.743-.153-.147a5.64 5.64 0 0 1-.007-7.941z"
    />
    <path
      fill="url(#logo_black_svg__b)"
      fillRule="evenodd"
      d="M23.824 23.658a13.717 13.717 0 0 0 .147-19.565l-5.84 5.763a5.65 5.65 0 0 1 1.629 4.124 5.66 5.66 0 0 1-1.782 3.96l5.818 5.744z"
    />
    <path fill="#1F2328" d="M14 18.622 4.5 28h18.998z" />
  </svg>
);
export default SvgLogoBlack;

