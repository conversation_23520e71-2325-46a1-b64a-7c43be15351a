.statusBadge {
  color: var(--mc-color-white);
  background-color: var(--mc-gray-7);
  font-size: var(--mc-font-size-sm);
  border-radius: var(--mc-border-radius-xs);
  align-items: stretch;
  > div {
    padding: 0 var(--mc-padding-xs);
    align-content: center;
    &:is(:last-child) {
      border-radius: 0 var(--mc-border-radius-xs) var(--mc-border-radius-xs) 0;
    }
  }

  &-magenta {
    > div:is(:last-child) {
      background-color: var(--mc-magenta-6, #ff00ff);
    }
  }

  &-red {
    > div:is(:last-child) {
      background-color: var(--mc-red-6, #f5222d);
    }
  }

  &-volcano {
    > div:is(:last-child) {
      background-color: var(--mc-volcano-6, #fa541c);
    }
  }

  &-orange {
    > div:is(:last-child) {
      background-color: var(--mc-orange-6, #bc4c00);
    }
  }

  &-gold {
    > div:is(:last-child) {
      background-color: var(--mc-gold-6, #faad14);
    }
  }

  &-lime {
    > div:is(:last-child) {
      background-color: var(--mc-lime-6, #7cb305);
    }
  }

  &-green {
    > div:is(:last-child) {
      background-color: var(--mc-green-6, #1a7f37);
    }
  }

  &-cyan {
    > div:is(:last-child) {
      background-color: var(--mc-cyan-6, #13c2c2);
    }
  }

  &-blue {
    > div:is(:last-child) {
      background-color: var(--mc-blue-6, #0969da);
    }
  }
  &-geekblue {
    > div:is(:last-child) {
      background-color: var(--mc-geekblue-6, #2f54eb);
    }
  }

  &-purple {
    > div:is(:last-child) {
      background-color: var(--mc-purple-6, #6639ba);
    }
  }
}