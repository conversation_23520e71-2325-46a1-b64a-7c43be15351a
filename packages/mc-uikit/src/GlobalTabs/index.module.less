@import '../styles/vars.less';

.pageHeader {
  > [class*='-tabs-nav'] {
    :global {
      // .mc-tabs-nav-list {
      //   height: 40px;
      // }

      .mc-tabs-tab-btn {
        .mc-tabs-tab-icon:not(:last-child) {
          margin-inline-end: var(--mc-margin-xs) !important;
        }
      }

      .mc-tabs-tab {
        line-height: 28px;
        padding: 0 8px;
        border-radius: var(--mc-border-radius-sm);
        margin: 6px 4px 10px;
        // height: 30px;

        &:hover {
          background-color: @light-control-transparent-bgColor-hover;
        }
      }

      .mc-tabs-tab-btn {
        display: flex;
        align-items: center;
        .mc-tabs-tab-icon {
          display: flex;
          &:not(:last-child) {
            margin-inline-end: var(--mc-margin-xs);
          }
          &>svg {
            display: block;
          }
        }
      }
    }
  }
}

// dark 主题适配
[data-color-mode='dark'] {
  .pageHeader {
    > [class*='-tabs-nav'] {
      :global {
        .mc-tabs-tab:hover {
          background-color: @dark-control-transparent-bgColor-hover;
        }
      }
    }
  }
}
