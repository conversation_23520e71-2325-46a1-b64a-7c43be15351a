import React, { useCallback, useEffect } from 'react';

import { useRequest } from '@ali/mc-request';
import { GitGroup } from '@ali/mc-services';
import { GetGitGroupsParams, getGitGroups } from '@ali/mc-services/Gitlab';

import PrefixSelect, { PrefixSelectProps } from '../PrefixSelect';

export type GitGroupSelectProps = GetGitGroupsParams & PrefixSelectProps;
export default function GitGroupSelect(props: GitGroupSelectProps) {
  const { value, style, ...rest } = props;

  const {
    data: groups = [],
    run: requestGroups,
    loading,
  } = useRequest<GitGroup[], [GetGitGroupsParams]>(getGitGroups, {
    throttleWait: 300,
  });

  const onSearch = useCallback(
    (keyword?: string) => {
      requestGroups({
        search: keyword,
        includePersonal: true,
        gitUrlPrefix: rest.gitUrlPrefix ? `${rest.gitUrlPrefix}:` : undefined,
      });
    },
    [requestGroups, rest.gitUrlPrefix],
  );

  useEffect(() => {
    console.log('init', groups, value);
    // 当前搜索词在options中时，不触发搜索
    if (value && groups.find((group) => group.pathWithNameSpace === value)) {
      return;
    }

    onSearch(value);
  }, [value, rest.gitUrlPrefix]);

  return (
    <PrefixSelect
      placeholder="输入关键词搜索"
      {...props}
      loading={loading}
      showSearch
      allowClear
      onSearch={onSearch}
      style={{
        minWidth: '150px',
        ...style,
      }}
      filterOption={false}
      options={(groups || []).map((group: GitGroup) => {
        return {
          key: group.pathWithNameSpace || group.path,
          label: group.pathWithNameSpace || group.name,
          value: group.pathWithNameSpace || group.path,
          data: group,
        };
      })}
    />
  );
}
