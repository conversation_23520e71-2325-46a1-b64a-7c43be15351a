/**
 * Default dark mode
 * @description Also used as a base for all other dark mode themes
 */
{
  base: {
    color: {
      black: {
        $value: '#010409',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            "collection": "base/color/dark",
          },
        },
      },
      "white": {
        $value: '#ffffff',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            "collection": "base/color/dark",
          },
        },
      },
      transparent: {
        $value: '#000000',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            "collection": "base/color/dark",
          },
        },
        alpha: 0,
      },
      neutral: {
        '0': {
          $value: '#f0f6fc',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#c9d1d9',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#b1bac4',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#8b949e',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#6e7681',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#484f58',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#30363d',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#21262d',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#161b22',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#0d1117',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
      blue: {
        '0': {
          $value: '#cae8ff',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#a5d6ff',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#79c0ff',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#58a6ff',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#388bfd',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#1f6feb',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#1158c7',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#0d419d',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#0c2d6b',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#051d4d',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
      green: {
        '0': {
          $value: '#aff5b4',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#7ee787',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#56d364',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#3fb950',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#2ea043',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#238636',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#196c2e',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#0f5323',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#033a16',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#04260f',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
      yellow: {
        '0': {
          $value: '#f8e3a1',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#f2cc60',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#e3b341',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#d29922',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#bb8009',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#9e6a03',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#845306',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#693e00',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#4b2900',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#341a00',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
      orange: {
        '0': {
          $value: '#ffdfb6',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#ffc680',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#ffa657',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#f0883e',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#db6d28',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#bd561d',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#9b4215',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#762d0a',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#5a1e02',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#3d1300',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
      red: {
        '0': {
          $value: '#ffdcd7',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#ffc1ba',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#ffa198',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#ff7b72',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#f85149',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#da3633',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#b62324',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#8e1519',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#67060c',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#490202',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
      purple: {
        '0': {
          $value: '#eddeff',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#e2c5ff',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#d2a8ff',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#BE8FFF',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#AB7DF8',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#8957e5',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#6e40c9',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#553098',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#3c1e70',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#271052',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
      pink: {
        '0': {
          $value: '#ffdaec',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#ffbedd',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#ff9bce',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#f778ba',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#db61a2',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#bf4b8a',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#9e3670',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#7d2457',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#5e103e',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#42062a',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
      coral: {
        '0': {
          $value: '#ffddd2',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '1': {
          $value: '#ffc2b2',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '2': {
          $value: '#ffa28b',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '3': {
          $value: '#f78166',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '4': {
          $value: '#ea6045',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '5': {
          $value: '#cf462d',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '6': {
          $value: '#ac3220',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '7': {
          $value: '#872012',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '8': {
          $value: '#640d04',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
        '9': {
          $value: '#460701',
          $type: 'color',
          $extensions: {
            'org.primer.figma': {
              "collection": "base/color/dark",
            },
          },
        },
      },
    },
  },
}
