import { ComponentsConfig } from '../../../types';
import { generateColorPalettes } from '../colors';
import { black } from '../../../vars/light';

const coralColors = generateColorPalettes('coral');
export default function (/* token: FullToken<'Tabs'> */): ComponentsConfig['Tabs'] {
  return {
    cardBg: 'transparent',
    itemHoverColor: black,
    itemActiveColor: black,
    itemSelectedColor: black,
    inkBarColor: coralColors[4],
  };
}
