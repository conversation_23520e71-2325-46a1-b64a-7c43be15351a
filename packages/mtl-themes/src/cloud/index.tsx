import { ThemeConfig, theme } from 'antd';

import { lightTheme, darkTheme } from './theme';
import { MTLThemeConfig } from '../types';

import './styles/index.module.less';

const defaultThemeConfig: ThemeConfig = {
  cssVar: true,
  hashed: false,
  token: {
    colorError: 'red',
    colorInfo: 'blue',
    colorPrimary: 'blue',
    colorSuccess: 'green',
    colorWarning: 'yellow',
    borderRadiusXS: 3,
    borderRadiusLG: 6,
    motionUnit: 0.05,
    motionEaseOutCirc: 'none',
    boxShadow:
      '0px 0px 0px 1px #d0d7de, 0px 8px 16px -4px #424a5314, 0px 4px 32px -4px #424a5314, 0px 24px 48px -12px #424a5314, 0px 48px 96px -24px #424a5314',
    boxShadowSecondary: '0px 0px 0px 1px #d0d7de80, 0px 6px 12px -3px #424a530a, 0px 6px 18px 0px #424a531f',
    boxShadowTertiary: '0px 0px 0px 1px #d0d7de80, 0px 6px 12px -3px #424a530a, 0px 6px 18px 0px #424a531f',
  },
};

const themeConfig: MTLThemeConfig = {
  theme: {
    ...defaultThemeConfig,
    algorithm: [lightTheme.algorithm],
    components: lightTheme.components,
  },
  darkAlgorithm: darkTheme.algorithm,
  compactAlgorithm: theme.compactAlgorithm,
};


export default themeConfig;
