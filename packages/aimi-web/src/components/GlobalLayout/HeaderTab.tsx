import { AppsOutlined, HomeOutlined, KnowledgeBaseOutlined, McpOutlined } from '@ali/mc-icons';
import { Flex, Segmented } from 'antd';
import React, { useMemo } from 'react';
import styles from './index.module.less';
import { useLocation, useNavigate } from 'ice';

const HeaderOptions = [{
  value: 'dashboard',
  label: '首页',
  icon: <HomeOutlined />,
}, {
  value: 'agent',
  label: 'Agent应用',
  icon: <AppsOutlined />,
}, {
  value: 'knowledge',
  label: '知识库',
  icon: <KnowledgeBaseOutlined />,
}, {
  value: 'mcp',
  label: 'MCP',
  icon: <McpOutlined />,
}];

const HeaderTab = () => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const activeKey = useMemo(() => {
    return (pathname ?? '').split('/')?.[1];
  }, [pathname]);
  return (<Flex align="center" gap="middle">
    <Segmented
      className={styles.headerSegment}
      options={HeaderOptions}
      size="large"
      shape="round"
      value={activeKey}
      onChange={(value) => {
        navigate(`/${value}`);
      }}
    />
  </Flex>);
};

export default React.memo(HeaderTab);
