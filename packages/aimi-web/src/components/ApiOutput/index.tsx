import React, { CSSProperties } from 'react';
import { Flex, Tooltip, theme } from 'antd';
import { ApiOutlined, CopyOutlined } from '@ali/mc-icons';
import { Copy } from '@ali/mc-uikit';
import GlobalIcon from '../GlobalIcon';
import styles from './index.module.less';

type ApiOutputProps = {
  api?: string;
  showKnowledge?: boolean;
  style?: CSSProperties;
};

function ApiOutput(props: ApiOutputProps) {
  const { api = '', showKnowledge = true, style } = props;
  const { token } = theme.useToken();

  return (
    !!api.length && (
      <Flex align="center" className={styles.apiOutput} style={style}>
        <Tooltip title="API">
          <ApiOutlined className={styles.apiIcon} />
        </Tooltip>
        <Flex gap={token.marginSM} align="center" className={styles.apiContainer}>
          <Tooltip title={api}>
            <Flex className={styles.api}>{api}</Flex>
          </Tooltip>
          <Copy text={api} className={styles.copyIcon}>
            <CopyOutlined />
          </Copy>
          {showKnowledge && (
            <GlobalIcon
              type="knowledge"
              className={styles.apiContainerIcon}
              onClick={() =>
                window.open('https://alidocs.dingtalk.com/i/nodes/R1zknDm0WR6XzZ4LtRaeE3pGWBQEx5rG', '_blank')
              }
            />
          )}
        </Flex>
      </Flex>
    )
  );
}

export default React.memo(ApiOutput);
