import React from 'react';
import Question from './Question';
import Answer from './Answer';
import { Conversation as TConversation } from '@/types/dialog';

interface ConversationProps {
  data: TConversation;
}

// 一次问答定义为一个会话
const Conversation = (props: ConversationProps) => {
  const { data } = props;
  return (
    <div>
      {data?.question && <Question data={data?.question} />}
      {data?.answer && <Answer data={data?.answer} />}
    </div>
  );
};

export default Conversation;
