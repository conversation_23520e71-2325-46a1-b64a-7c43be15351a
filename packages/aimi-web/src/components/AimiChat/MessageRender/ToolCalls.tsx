import React from 'react';
import { Flex, theme } from 'antd';
import { CheckOutlined } from '@ali/mc-icons';
import styles from './index.module.less';

export default function ToolCalls({ children, ...props }) {
  // node: AST 节点信息 (高级用法)
  // children: <ToolCalls> 标签内部渲染后的内容
  // props: 标签上的其他属性 (例如 <ToolCalls id="123"> 中的 id)
  const { token } = theme.useToken();

  let data: any;
  try {
    data = JSON.parse(children ?? '{}');
  } catch (error) {
    console.log(error);
  }
  let { reasoning } = data ?? {};

  return (
    <Flex className={styles.toolCallsContainer} vertical gap={16}>
      {reasoning && <span>{reasoning}</span>}
      <Flex className={styles.toolCallsItem} justify="space-between">
        <span>AIMI 调用了MCP Tool：{props?.name}</span>
        <CheckOutlined style={{ color: token.colorSuccess, fontSize: token.fontSizeLG }} />
      </Flex>
    </Flex>
  );
}
