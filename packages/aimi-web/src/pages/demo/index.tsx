import React from 'react';
import { Divider, Flex } from 'antd';

import AlertDemo from './components/Alert'
import ButtonDemo from './components/button';
import CardDemo from './components/Card';
import ModalDemo from './components/Modal';
import InputDemo from './components/Input';
import SegmentedDemo from './components/Segmented';
import SelectDemo from './components/Select';
import StatusTagDemo from './components/StatusTag';
import TableDemo from './components/Table';

export default function DemoPage() {
  return (
    <Flex vertical gap="middle">
      <Flex vertical>
        <Divider orientation="left">Alert 警告提示</Divider>
        <AlertDemo />
      </Flex>
      <Flex vertical>
        <Divider orientation="left">Button 按钮</Divider>
        <ButtonDemo />
      </Flex>
      <Flex vertical>
        <Divider orientation="left">Card 卡片</Divider>
        <CardDemo />
      </Flex>
      <Flex vertical>
        <Divider orientation="left">Input 输入框</Divider>
        <InputDemo />
      </Flex>
      <Flex vertical>
        <Divider orientation="left">Modal 对话框</Divider>
        <ModalDemo/>
      </Flex>
      <Flex vertical>
        <Divider orientation="left">Segmented 分段控制器</Divider>
        <SegmentedDemo />
      </Flex>
      <Flex vertical>
        <Divider orientation="left">Select 选择器</Divider>
        <SelectDemo />
      </Flex>
      <Flex vertical>
        <Divider orientation="left">StatusTag 状态标签</Divider>
        <StatusTagDemo/>
      </Flex>
      <Flex vertical>
        <Divider orientation="left">Table 表格</Divider>
        <TableDemo />
      </Flex>
    </Flex>
  );
}
