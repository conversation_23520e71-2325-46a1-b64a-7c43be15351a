import React, { useEffect } from 'react';
import { Flex } from 'antd';
import { ArrowLeftOutlined } from '@ali/mc-icons';
import { useSearchParams, Link } from 'ice';
import AimiChat from '@/components/AimiChat';
import { useLayoutContext } from '@ali/mc-uikit';
import AgentHeader from '../agent/components/AgentHeader';

export default () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const agentId = Number(searchParams.get('agentId'));
  const agentIdentifier = searchParams.get('agentIdentifier') ?? 'aimi-main';
  const sessionId = Number(searchParams.get('sessionId'));
  const from = searchParams.get('from') ?? '';

  const { setBreadcrumbItems } = useLayoutContext();

  useEffect(() => {
    if (from === 'dashboard') {
      setBreadcrumbItems([
        {
          type: 'separator',
          separator: (
            <Link to="/dashboard">
              <ArrowLeftOutlined />
            </Link>
          ),
        },
        {
          title: '首页',
          path: '/dashboard',
        },
        {
          title: '历史会话',
        },
      ]);
    }
  }, [from, setBreadcrumbItems]);

  const updateSessionId = (value?: number) => {
    if (value) {
      setSearchParams((prevParams) => {
        prevParams.set('sessionId', String(value));
        return prevParams;
      });
    } else {
      setSearchParams((prevParams) => {
        prevParams.delete('sessionId');
        return prevParams;
      });
    }
  };

  return (
    <Flex
      vertical
      style={{
        height: `calc(100vh - 104px - ${from === 'dashboard' ? '46px' : '0px'} - 24px)`,
      }}
    >
      {!!agentId && agentIdentifier && <AgentHeader agentId={agentId} showBreadcrumbItems={from !== 'dashboard'} />}
      <AimiChat
        allowShowHistory
        agentId={agentId}
        agentIdentifier={agentIdentifier}
        sessionId={sessionId}
        updateSessionId={updateSessionId}
      />
    </Flex>
  );
};
