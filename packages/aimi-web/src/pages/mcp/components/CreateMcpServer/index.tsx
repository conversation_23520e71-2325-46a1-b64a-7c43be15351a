import { PlusOutlined } from '@ali/mc-icons';
import { Button, Form } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import CreateMcpServerProvider from './CreateMcpServerProvider';
import { useSearchParams } from 'ice';
import CreateMcpServerModalContent from './CreateMcpServerModalContent';


const CreateMcpServer = ({ onRefresh, open: propsOpen = false }: { onRefresh: () => void; open?: boolean}) => {
  const [open, setOpen] = useState<boolean>(propsOpen);
  const [searchParams, setSearchParams] = useSearchParams();
  const [form] = Form.useForm();

  const handleOpen = useCallback(() => {
    setOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setOpen(false);
    form.resetFields();
  }, [form]);

  useEffect(() => {
    if (open && searchParams.get('createServer')) {
      setSearchParams(prev => {
        prev.delete('createServer');
        return prev;
      });
    }
  }, [open, searchParams, setSearchParams]);

  return (<CreateMcpServerProvider>
    <Button
      type="primary"
      icon={<PlusOutlined />}
      onClick={handleOpen}
    >
      注册MCP Server
    </Button>
    <CreateMcpServerModalContent
      open={open}
      onClose={handleClose}
      onRefresh={onRefresh}
    />
  </CreateMcpServerProvider>);
};

export default CreateMcpServer;
