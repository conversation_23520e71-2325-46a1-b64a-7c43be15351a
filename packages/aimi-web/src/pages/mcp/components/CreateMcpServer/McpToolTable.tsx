import { Flex, Input, Table, theme } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import { StatusTag } from '@ali/mc-uikit';
import AimiUser from '@/components/AimiUser';
import { useCreateMcpServerContext } from './CreateMcpServerProvider';
import BizGroupSelector from '@/components/BizGroupSelector';
import { McpToolMeta, PageMcpToolMetaVO } from '@ali/mc-services/AiMiMcpDataContracts';
import { useRequest } from '@ali/mc-request';
import { getMcpToolMeta1, GetMcpToolMeta1Params } from '@ali/mc-services/AiMiMcpToolMeta';
import QueueAnim from 'rc-queue-anim';
import { SearchOutlined } from '@ali/mc-icons';
import { debounce } from 'lodash-es';

const McpToolTable = React.memo(({ dataSource, selectable }:
  { dataSource?: McpToolMeta[]; selectable: boolean }) => {
  const {
    setSelectedToolIds,
    selectedToolIds,
    setSelectedTools,
    selectedTools,
  } = useCreateMcpServerContext();
  const { token } = theme.useToken();

  const [searchParams, setSearchParams] = useState<GetMcpToolMeta1Params>({
    pageNo: 1,
    pageSize: 10,
  });

  const [showSelected, setShowSelected] = useState<boolean>(false);

  const {
    runAsync: getMcpToolList,
    data: mcpToolList,
    loading: mcpToolListLoading,
  } = useRequest<PageMcpToolMetaVO, [GetMcpToolMeta1Params | undefined]>(getMcpToolMeta1);

  useEffect(() => {
    if (!dataSource?.length) {
      getMcpToolList({
        ...searchParams,
        pageNo: (searchParams.pageNo ?? 1) - 1,
      });
    }
  }, [getMcpToolList, searchParams, dataSource]);

  const columns = [{
    title: '接口名',
    dataIndex: 'name',
    render: (_: any, record: McpToolMeta) => {
      return (<Flex flex={1} gap={token.marginXS} justify="space-between" align="center">
        <Flex vertical gap={token.marginXXS}>
          <Flex gap={token.marginXS} align="center">
            <StatusTag
              color={record?.accessLevel === 'PRIVATE' ? 'warning' : 'green'}
              bordered={false}
              style={{
                height: token.controlHeightSM - token.sizeUnit,
              }}
            >
              {record?.accessLevel === 'PRIVATE' ? '私有' : '公开'}
            </StatusTag>
            <div>
              {`${record?.name}`}
            </div>
            <StatusTag
              style={{
                height: token.controlHeightSM - token.sizeUnit,
              }}
            >
              {record?.bizGroupName}
            </StatusTag>
            {record?.needBuc && <StatusTag
              style={{
                height: token.controlHeightSM - token.sizeUnit,
                borderRadius: 10,
              }}
              color="processing"
              muted
            >
              BUC
            </StatusTag>}
          </Flex>
          <Flex
            style={{
              fontSize: token.fontSizeSM,
              color: token.colorTextSecondary,
            }}
          >
            {record?.description}
          </Flex>
        </Flex>
        <Flex style={{ flexShrink: 0 }}>
          <AimiUser empIds={record?.creator} showAvatar size="small" />
        </Flex>
      </Flex>);
    },
  }];

  return (<>
    {selectable && <Flex
      vertical
      gap={token.marginSM}
      style={{
        marginBottom: token.marginSM,
      }}
    >
      <Flex
        justify="space-between"
        align="center"
        gap={token.margin}
      >
        <Flex
          flex={1}
          gap={token.margin}
          align="center"
        >
          <BizGroupSelector
            allowClear
            style={{
              width: 400,
            }}
            disabled={showSelected}
            value={searchParams?.bizGroupId}
            onChange={(v: number) => {
              setSearchParams(prev => ({
                ...prev,
                bizGroupId: v,
              }));
            }}
          />
          <Input
            style={{
              width: 400,
            }}
            allowClear
            disabled={showSelected}
            width="400"
            placeholder="Tool名称"
            prefix={<SearchOutlined
              style={{
                color: token.colorTextSecondary,
              }}
            />}
            onChange={debounce((e: any) => {
               setSearchParams(prev => ({
                ...prev,
                name: e?.target?.value,
              }));
            }, 300)}
          />
        </Flex>
      </Flex>
      <Flex
        justify="space-between"
        align="center"
        style={{
          border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,
          borderRadius: 8,
          paddingBlock: token.paddingXS,
          paddingInline: token.paddingSM,
          backgroundColor: token.colorBorder,
        }}
      >
        <div>
          已选择
          <span
            style={{
              marginInline: token.paddingXXS,
            }}
          >
            {selectedToolIds?.length ?? 0}
          </span>
          项
        </div>
        <a
          onClick={() => {
            setShowSelected(s => !s);
          }}
        >
          {showSelected ? '取消查看' : '查看已选'}
        </a>
      </Flex>
    </Flex>}
    {
      showSelected ? <QueueAnim
        type={['right', 'left']}
        ease={['easeOutQuart', 'easeInOutQuart']}
      >
        <div key="selected_tool_table">
          <Table
            key="selected_tool_table"
            size="small"
            className={styles.apiTable}
            rowKey={r => r?.id ?? -1}
            dataSource={selectedTools ?? []}
            columns={columns}
            showHeader={false}
            rowSelection={{
              preserveSelectedRowKeys: true,
              selectedRowKeys: selectedToolIds,
              onSelect: (record, selected) => {
                if (record?.id) {
                  if (selected && !selectedToolIds?.includes(record?.id)) {
                    setSelectedToolIds?.(prev => [...(prev ?? []), record?.id ?? -1]);
                    setSelectedTools?.(prev => ([...(prev ?? []), record]));
                  } else {
                    setSelectedToolIds?.(prev => (prev ?? []).filter(r => r !== record?.id));
                    setSelectedTools?.(prev => (prev ?? []).filter(r => r.id !== record?.id));
                  }
                }
              },
              onSelectAll: (selected, _selectedRows, changeRows) => {
                if (selected) {
                  (changeRows ?? []).forEach(newRow => {
                    if (newRow?.id && !selectedToolIds?.includes(newRow?.id)) {
                      setSelectedToolIds?.(prev => [...(prev ?? []), newRow?.id ?? -1]);
                      setSelectedTools?.(prev => ([...(prev ?? []), newRow]));
                    }
                  });
                } else {
                  (changeRows ?? []).forEach(deleteRow => {
                    setSelectedToolIds?.(prev => (prev ?? []).filter(r => r !== deleteRow?.id));
                    setSelectedTools?.(prev => (prev ?? []).filter(r => r.id !== deleteRow?.id));
                  });
                }
              },
            }}
            pagination={{
              size: 'small',
              pageSize: 10,
            }}
          />
        </div>
      </QueueAnim> : null
    }
    {
      showSelected ? null : <QueueAnim
        type={['right', 'left']}
        ease={['easeOutQuart', 'easeInOutQuart']}
      >
        <div key="unselected_tool_table">
          <Table
            loading={mcpToolListLoading}
            size="small"
            className={styles.apiTable}
            rowKey={r => r?.id ?? -1}
            dataSource={(dataSource || mcpToolList?.items)}
            columns={columns}
            showHeader={false}
            rowSelection={{
              preserveSelectedRowKeys: true,
              selectedRowKeys: selectedToolIds,
              onSelect: (record, selected) => {
                if (record?.id) {
                  if (selected && !selectedToolIds?.includes(record?.id)) {
                    setSelectedToolIds?.(prev => [...(prev ?? []), record?.id ?? -1]);
                    setSelectedTools?.(prev => ([...(prev ?? []), record]));
                  } else {
                    setSelectedToolIds?.(prev => (prev ?? []).filter(r => r !== record?.id));
                    setSelectedTools?.(prev => (prev ?? []).filter(r => r.id !== record?.id));
                  }
                }
              },
              onSelectAll: (selected, _selectedRows, changeRows) => {
                if (selected) {
                  (changeRows ?? []).forEach(newRow => {
                    if (newRow?.id && !selectedToolIds?.includes(newRow?.id)) {
                      setSelectedToolIds?.(prev => [...(prev ?? []), newRow?.id ?? -1]);
                      setSelectedTools?.(prev => ([...(prev ?? []), newRow]));
                    }
                  });
                } else {
                  (changeRows ?? []).forEach(deleteRow => {
                    setSelectedToolIds?.(prev => (prev ?? []).filter(r => r !== deleteRow?.id));
                    setSelectedTools?.(prev => (prev ?? []).filter(r => r.id !== deleteRow?.id));
                  });
                }
              },
            }}
            pagination={{
              size: 'small',
              pageSize: searchParams?.pageSize,
              total: showSelected ? selectedTools?.length : (mcpToolList?.totalCount || 0),
              current: showSelected ? undefined : searchParams?.pageNo,
              showSizeChanger: false,
              onChange: showSelected ? undefined : (page, size) => {
                setSearchParams(prev => ({
                  ...prev,
                  pageNo: page,
                  pageSize: size,
                }));
              },
            }}
          />
        </div>

      </QueueAnim>
    }

  </>);
});

export default McpToolTable;
