import { Button, Form, message, Modal, theme } from 'antd';
import React, { useState } from 'react';
import { useCreateMcpServerContext } from './CreateMcpServerProvider';
import McpServerForm from './McpServerForm';
import { useRequest } from '@ali/mc-request';
import { getMcpServerMeta, GetMcpServerMetaParams, saveMcpServerMeta } from '@ali/mc-services/AiMiMcpServerMeta';
import { McpServerMeta } from '@ali/mc-services/AiMiMcpDataContracts';
import { removeFalsy } from '@/utils/utils';
import CreateServerResultCard from './CreateServerResultCard';
import { updateAgentConfig } from '@ali/mc-services/AiMiAgent';
import { AgentMcpServerVO } from '@ali/mc-services/AiMiDataContracts';
import { isEmpty } from 'lodash-es';

const CreateMcpServerModalContent = ({ open, onClose, onRefresh }: {
  open?: boolean;
  onClose: () => void;
  onRefresh: () => void;
}) => {
  const { token } = theme.useToken();
  const [form] = Form.useForm();
  const [batchUpdateAgentLoading, setBatchUpdateAgentLoading] = useState<boolean>(false);
  const [needRefresh, setNeedRefresh] = useState<boolean>(false);

  const {
    selectedToolIds,
    selectedTools,
    setCreatedServer,
    setStep,
    step,
    showAgentList,
    selectedAgents,
    createdServer,
    agentUpdated,
    setAgentUpdated,
    resetStates,
  } = useCreateMcpServerContext();

  const {
    runAsync: saveMcpServer,
  } = useRequest<number, [McpServerMeta]>(saveMcpServerMeta);

  const {
    runAsync: getMcpServerDetail,
  } = useRequest<McpServerMeta, [GetMcpServerMetaParams]>(getMcpServerMeta);

  const handleOk = () => {
    form.validateFields().then(values => {
      const {
        name,
        bizGroupId,
        path,
        description,
        iconUrl,
        accessLevel,
      } = values;

      const toolMetaIds = selectedToolIds ?? [];
      const tools = selectedTools ?? [];
      const params = {
        name,
        bizGroupId,
        path: `/mcp-server/${path}/sse`,
        description,
        iconUrl,
        accessLevel: accessLevel ? 'PUBLIC' : 'PRIVATE',
        toolMetaIds,
        tools,
      };
      console.log(params, 'params===>');
      saveMcpServer(removeFalsy({
       ...params,
      })).then(res => {
        if (res) {
          message.success('MCP Server注册成功');
          setNeedRefresh(true);
          getMcpServerDetail({
            id: res,
          }).then(detail => {
            setCreatedServer(detail);
            setStep?.(1);
          });
        }
      });
    }).catch((err) => {
      const { errorFields } = err || {};
      if (errorFields?.length) {
        const filedName = errorFields[errorFields?.length - 1]?.name;
        if (filedName) {
          form.scrollToField(filedName, {
            block: 'center',
            behavior: 'smooth',
          });
        }
      }
    });
  };

  const handleBatchUpdateAgent = () => {
    // updateAgentConfig
    if (selectedAgents?.length) {
      const params = (selectedAgents ?? []).map(agent => ({
        ...agent,
        mcpServerMetas: [...(agent?.mcpServerMetas ?? []), createdServer] as AgentMcpServerVO[],
      }));
      setBatchUpdateAgentLoading(true);
       Promise.all(params.map(param => updateAgentConfig(param))).then(() => {
        setBatchUpdateAgentLoading(false);
        message.success('加入Agent成功');
        setAgentUpdated(true);
      });
    }
  };

  return (<Modal
    open={open}
    onCancel={onClose}
    title="注册MCP Server"
    centered
    destroyOnClose
    footer={(_, { CancelBtn }) => {
      if (step === 0) {
        return (<>
          <CancelBtn />
          <Button
            type="primary"
            onClick={handleOk}
          >
            注册
          </Button>
        </>);
      } else if (step === 1) {
        if (showAgentList) {
          if (agentUpdated) {
            return (<>
              <Button
                onClick={() => {
                  onClose?.();
                }}
              >
                关闭
              </Button>
            </>);
          } else {
            return (<>
              <Button
                type="primary"
                onClick={handleBatchUpdateAgent}
                loading={batchUpdateAgentLoading}
                disabled={isEmpty(selectedAgents)}
              >
                加入
              </Button>
            </>);
          }
        } else {
          return (<Button
            onClick={() => {
              onClose?.();
            }}
          >
            关闭
          </Button>);
        }
      } else {
        return null;
      }
    }}
    afterClose={() => {
      needRefresh && onRefresh?.();
      form.resetFields?.();
      resetStates?.();
    }}
    width={958}
    styles={{
      body: {
        height: 'calc(100vh - 250px)',
        overflowY: 'auto',
        paddingBlock: token.padding,
        minHeight: 498,
      },
      footer: {
        marginTop: 0,
      },
      header: {
        marginBottom: 0,
      },
    }}
  >
    {step === 0 && <McpServerForm
      form={form}
    />}
    {step === 1 && <CreateServerResultCard />}
  </Modal>);
};

export default CreateMcpServerModalContent;
