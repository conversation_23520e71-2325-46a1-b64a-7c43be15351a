import { Checkbox, Collapse, Flex, Input, Popover, Select, Table, theme, Typography } from 'antd';
import React, { useMemo, useState } from 'react';
import styles from './index.module.less';
import { ISwaggerHttpApiVO } from './type';
import { useCreateMcpToolContext } from './CreateMcpToolProvider';
import { StatusTag } from '@ali/mc-uikit';
import { transformData } from './utils';
import { debounce, intersection } from 'lodash-es';
import QueueAnim from 'rc-queue-anim';
import HttpMethodTag from '@/components/HttpMethodTag';
import { InfoOutlined, SearchOutlined } from '@ali/mc-icons';

const ApiTable = React.memo(({ dataSource, selectable }: { dataSource: ISwaggerHttpApiVO[]; selectable: boolean }) => {
  const { selectedApiKeys, setSelectedApiKeys } = useCreateMcpToolContext();
  const { token } = theme.useToken();
  const columns = [{
    title: '接口名',
    dataIndex: 'name',
    render: (_: any, record: ISwaggerHttpApiVO) => {
      return (<Flex flex={1} gap={token.marginXXS} vertical justify="center">
        <Flex align="center" gap={token.marginXS}>
          <HttpMethodTag httpMethod={record?.api?.httpMethod} />
          {
            record?.existed && <StatusTag
              color="green"
              style={{
                height: token.controlHeightSM - token.sizeUnit,
              }}
            >
              已注册
            </StatusTag>
          }
          <div>
            {`${record?.api?.path}`}
          </div>
        </Flex>
        {record?.api?.description && <Flex>
          <Typography.Paragraph
            ellipsis={{
              rows: 2,
              tooltip: record?.api?.description,
            }}
            style={{
              marginBottom: 0,
              fontSize: token.fontSizeSM,
              color: token.colorTextSecondary,
            }}
          >
            {record?.api?.description}
          </Typography.Paragraph>
        </Flex>}
      </Flex>);
    },
  }];
  const sortedDataSource = useMemo(() => {
    return (dataSource ?? []).sort((a) => { return a.existed === true ? 1 : -1; });
  }, [dataSource]);

  return (<Table
    size="small"
    className={styles.apiTable}
    rowKey={r => r?.id}
    // dataSource={(dataSource ?? []).sort((a) => { return a.existed === true ? 1 : -1; })}
    dataSource={sortedDataSource}
    columns={columns}
    showHeader={false}
    rowSelection={selectable ? {
      preserveSelectedRowKeys: true,
      selectedRowKeys: selectedApiKeys,
      getCheckboxProps: (record) => ({
        disabled: record.existed,
      }),
      onSelect: (record, selected) => {
        if (record?.id) {
          if (selected && !selectedApiKeys?.includes(record?.id)) {
            setSelectedApiKeys?.(prev => [...(prev ?? []), record?.id]);
          } else {
            setSelectedApiKeys?.(prev => (prev ?? []).filter(r => r !== record?.id));
          }
        }
      },
      // onSelectAll: (selected, _selectedRows, changeRows) => {
      //   if (selected) {
      //     (changeRows ?? []).forEach(newRow => {
      //       if (newRow?.id && !selectedApiKeys?.includes(newRow?.id)) {
      //         setSelectedApiKeys?.(prev => [...(prev ?? []), newRow?.id ?? -1]);
      //       }
      //     });
      //   } else {
      //     (changeRows ?? []).forEach(deleteRow => {
      //       setSelectedApiKeys?.(prev => (prev ?? []).filter(r => r !== deleteRow?.id));
      //     });
      //   }
      // },
    } : undefined}
    pagination={selectable ? false : {
      pageSize: 10,
      showSizeChanger: false,
    }}
  />);
});


const HttpApiCollapsibleList = ({
  dataSource,
  selectable = false,
}: {
  dataSource?: ISwaggerHttpApiVO[];
  selectable?: boolean;
}) => {
  const { token } = theme.useToken();
  const { selectedApiKeys, setSelectedApiKeys, selectedApis } = useCreateMcpToolContext();
  const [filters, setFilters] = useState<{
    tags?: string[];
    httpMethods?: string[];
    name?: string;
    showNotExisted?: boolean;
  }>({
    showNotExisted: true,
  });
  const [showSelected, setShowSelected] = useState<boolean>(false);

  const finalData = useMemo(() => {
    const { tags = [], httpMethods = [], name = '', showNotExisted = false } = filters || {};
    let dataSource_ = dataSource ?? [];
    if (tags?.length) {
      dataSource_ = (dataSource_ ?? []).map(item => ({
        ...item,
        api: {
          ...item?.api,
          displayTags: intersection(item?.api?.displayTags, tags),
        },
      }));
    }

    const filteredData = (dataSource_ ?? []).filter(item => {
      const hasMatchingHttpMethod = httpMethods?.length > 0 ? httpMethods.includes(item?.api?.httpMethod ?? '') : true;
      const matchesName = (item?.api?.path?.toLowerCase()?.includes(name) ||
      item?.api?.serverUrl?.toLowerCase()?.includes(name));
      const meetsShowCondition = showNotExisted ? !item?.existed : true;
      return hasMatchingHttpMethod && matchesName && meetsShowCondition;
    });

    return transformData(filteredData ?? []);
  }, [dataSource, filters]);

  const { tagOptions, httpMethodOptions } = useMemo(() => {
    return {
      tagOptions: ([...new Set((dataSource ?? []).flatMap(item => item?.api?.displayTags))]).map(tag => ({
        label: tag,
        value: tag,
      })),
      httpMethodOptions: ([...new Set((dataSource ?? []).map(item => item?.api?.httpMethod))]).map(httpMethod => ({
        label: httpMethod,
        value: httpMethod,
      })),
    };
  }, [dataSource]);

  return (<Flex
    vertical
    flex={1}
    gap={token.margin}
  >
    {selectable && <>
      <Flex
        justify="space-between"
        align="center"
      >
        <Flex
          flex={1}
          gap={token.margin}
          align="center"
        >
          <Select
            placeholder="选择tag"
            options={tagOptions}
            mode="multiple"
            disabled={showSelected}
            onChange={(v) => {
              setFilters(prev => ({
                ...prev,
                tags: v,
              }));
            }}
          />
          <Select
            disabled={showSelected}
            placeholder="请求类型"
            mode="multiple"
            options={httpMethodOptions}
            onChange={(v) => {
              setFilters(prev => ({
                ...prev,
                httpMethods: v,
              }));
            }}
          />
          <Input
            disabled={showSelected}
            placeholder="接口名/url"
            allowClear
            prefix={<SearchOutlined
              style={{
                color: token.colorTextSecondary,
              }}
            />}
            onPressEnter={debounce((e: any) => {
              setFilters(prev => ({
                ...prev,
                name: e?.target?.value,
              }));
            }, 300)}
          />
          <Checkbox
            disabled={showSelected}
            onChange={(e) => {
              setFilters(prev => ({
                ...prev,
                showNotExisted: e.target.checked,
              }));
            }}
            checked={filters?.showNotExisted}
          >
            <Flex className="textNoWrap" align="center" gap={token.marginXXS}>
              过滤已注册
              <Popover
                content="接口不可重复注册Tool"
              >
                <InfoOutlined style={{ cursor: 'pointer' }} />
              </Popover>
            </Flex>
          </Checkbox>
        </Flex>
      </Flex>
      <Flex
        justify="space-between"
        align="center"
        style={{
          border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,
          borderRadius: 8,
          paddingBlock: token.paddingXS,
          paddingInline: token.paddingSM,
          backgroundColor: token.colorBorder,
        }}
      >
        <div>
          已选择
          <span
            style={{
              marginInline: token.paddingXXS,
            }}
          >
            {selectedApiKeys?.length ?? 0}
          </span>
          项
        </div>
        <a
          onClick={() => {
            setShowSelected(s => !s);
          }}
        >
          {showSelected ? '取消查看' : '查看已选'}
        </a>
      </Flex>
    </>}
    <Flex
      flex={1}
      vertical
    >
      {
        showSelected ? <QueueAnim
          type={['right', 'left']}
          ease={['easeOutQuart', 'easeInOutQuart']}
        >
          <div key="selected_tagCollapse">
            {(transformData(selectedApis ?? [])).map(item => {
              const apiIds = (item?.apis ?? []).map(api => api?.id);
              const filteredApis = (item?.apis ?? []).filter(i => !i.existed);
              return (<Collapse
                key={`selected_tagCollapse_${item?.tag}`}
                expandIconPosition="end"
                className={styles.apiCollapseStyle}
                style={{
                  width: '100%',
                  marginBottom: token.margin,
                }}
                collapsible="icon"
                defaultActiveKey={['1']}
                items={[{
                  label: <Flex>
                    <Checkbox
                      checked={filteredApis.every(api => selectedApiKeys?.includes(api?.id))}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedApiKeys?.(prev => (Array.from(new Set([
                            ...(prev ?? []),
                            ...(filteredApis?.map(api => api?.id) ?? []),
                          ]))));
                        } else {
                          setSelectedApiKeys?.(prev => (prev ?? []).filter(i => !apiIds.includes(i)));
                        }
                      }}
                    >
                      {item?.tag}
                    </Checkbox>
                  </Flex>,
                  // label: item?.tag,
                  key: '1',
                  children: <ApiTable dataSource={item?.apis} selectable={selectable} />,
                }]}
              />);
            })}
          </div>
        </QueueAnim> : null
      }
      {
        showSelected ? null : <QueueAnim
          type={['right', 'left']}
          ease={['easeOutQuart', 'easeInOutQuart']}
        >
          <div key="unselected_tagCollapse">
            {(finalData ?? []).map((item, index) => {
              const apiIds = (item?.apis ?? []).map(api => api?.id);
              const filteredApis = (item?.apis ?? []).filter(i => !i.existed);
              return (<Collapse
                key={`tagCollapse_${item?.tag}`}
                expandIconPosition="end"
                className={styles.apiCollapseStyle}
                style={{
                  width: '100%',
                  marginBottom: token.margin,
                }}
                defaultActiveKey={index === 0 ? [item?.tag] : []}
                collapsible="icon"
                items={[{
                  label: <Flex>
                    <Checkbox
                      checked={
                        filteredApis?.length ? filteredApis.every(api => selectedApiKeys?.includes(api?.id)) : false
                      }
                      disabled={filteredApis?.length === 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedApiKeys?.(prev => (Array.from(new Set([
                            ...(prev ?? []),
                            ...(filteredApis?.map(api => api?.id) ?? []),
                          ]))));
                        } else {
                          setSelectedApiKeys?.(prev => (prev ?? []).filter(i => !apiIds.includes(i)));
                        }
                      }}
                    >
                      <span
                        style={{
                          fontWeight: token.fontWeightStrong,
                        }}
                      >
                        {item?.tag}
                      </span>
                    </Checkbox>
                  </Flex>,
                  // label: item?.tag,
                  key: item?.tag,
                  children: <ApiTable dataSource={item?.apis} selectable={selectable} />,
                }]}
              />);
            })}
          </div>
        </QueueAnim>
      }
    </Flex>

  </Flex>);
};

export default HttpApiCollapsibleList;
