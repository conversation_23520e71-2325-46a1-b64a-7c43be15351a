.previewCard {
  .previewContent {
    height: 100%;
    overflow: auto;
  }
  .cardTabs {
    .cardTabsItem {
      // font-size: 14px;
      // font-weight: 600;
      // padding: 4px 16px;
      // border: 1px solid rgba(149, 149, 149, 0.3);
      // border-radius: 12px;
      // cursor: pointer;
    }
  }
  :global {
    .aimi-card-head {
      min-height: auto;
      padding: 8px 16px;
    }
    .aimi-card-body {
      height: calc(100% - 48px);
    }
    .aimi-tabs .aimi-tabs-tab {
      font-size: 14px;
      padding: 4px 16px;
      border: 1px solid transparent;
    }
    .aimi-tabs .aimi-tabs-tab-active {
      border-radius: 12px;
      border: 1px solid rgba(149, 149, 149, 0.3);
    }
  }
}
