import React from 'react';
import { Input } from 'antd';
// import { InlineButton } from '@ali/mc-uikit';
import SettingCard from '@/pages/agent/components/SettingCard';
// import PromptOptimizeModal from '../SettingModal/PromptOptimizeModal';

type PromptSettingProps = {
  value?: string;
  onChange?: (value: string) => void;
};

function PromptSetting(props: PromptSettingProps) {
  const { value, onChange } = props;
  // const [showPromptOptimizeModal, setShowPromptOptimizeModal] = useState(false);
  const { TextArea } = Input;

  const onTextAreaChange = (e: any) => {
    const inputStr = e.target.value;
    onChange?.(inputStr);
  };

  return (
    <>
      <SettingCard
        titleConfig={{
          title: '提示词',
          // actions: [
          //   <InlineButton type="text" key="optimize" onClick={() => setShowPromptOptimizeModal(true)}>
          //     优化
          //   </InlineButton>,
          // ],
        }}
      >
        <div style={{ padding: '12px 16px' }}>
          <TextArea
            autoFocus
            style={{ border: 'none', resize: 'none', boxShadow: 'none' }}
            autoSize={{ minRows: 12, maxRows: 12 }}
            placeholder="提示词：通过设定角色身份、能力范围和规则限制（如变量user），精准引导模型按需工作。变量需严格使用${xxx}格式。"
            value={value}
            onChange={onTextAreaChange}
          />
        </div>
      </SettingCard>
      {/* <PromptOptimizeModal open={showPromptOptimizeModal} onClose={() => setShowPromptOptimizeModal(false)} /> */}
    </>
  );
}

export default React.memo(PromptSetting);
