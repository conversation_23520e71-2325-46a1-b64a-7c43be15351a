import React from 'react';
import { useAgentContext } from '../AgentProvider';
import PromptSetting from './PromptSetting';
import KnowledgeSetting from './KnowledgeSetting';
import ModelSetting from './ModelSetting';
import McpSetting from './McpSetting';
import VariableSetting, { getVariablesFromText } from './VariableSetting';

function SettingCards() {
  const { agent, setAgent } = useAgentContext();

  const setVariablesToAgent = (prompt: string) => {
    setAgent((prevAgent) => {
      return { ...prevAgent, variables: getVariablesFromText(prompt, prevAgent) };
    });
  };

  return (
    <>
      <PromptSetting
        value={agent?.promptTemplate || ''}
        onChange={(value) => {
          setAgent({
            ...agent,
            promptTemplate: value,
          });
          setVariablesToAgent(value);
        }}
      />
      <ModelSetting />
      <KnowledgeSetting />
      <McpSetting />
      <VariableSetting onRefresh={() => setVariablesToAgent(agent?.promptTemplate || '')} />
    </>
  );
}
export default React.memo(SettingCards);
