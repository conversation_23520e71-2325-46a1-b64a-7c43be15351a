import { Divider, Flex, message, Skeleton, Switch, Table, theme, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import KnowledgeDetailHeader from './components/KnowledgeDetailHeader';
import { Link, useParams } from 'ice';
import { getKnowledgeBase, GetKnowledgeBaseParams } from '@ali/mc-services/AiMiKnowledgeBase';
import { useRequest } from '@ali/mc-request';
import { DocumentPageRequest, DocumentVO, KnowledgeBaseVO, PageDocumentVO } from '@ali/mc-services/AiMiDataContracts';
import { modifyDocumentStatus, ModifyDocumentStatusParams, pageDocument } from '@ali/mc-services/AiMiDocument';
import { ArrowLeftOutlined, FileOutlined, LinkOutlined, TrashOutlined } from '@ali/mc-icons';
import { DOC_SOURCE_MAP } from './components/constants';
import { getTimeStr } from '@ali/mc-services';
import { InlineButton, useLayoutContext } from '@ali/mc-uikit';
import styles from './index.module.less';
import UpdateKnowledgeBase from './components/UpdateKnowledgeBase';

const StatusTextMap: { [key: string]: string } = {
  ACTIVE: '启用',
  DISABLED: '禁用',
  REMOVED: '移除',
};

export default () => {
  const { token } = theme.useToken();
  const { setBreadcrumbItems } = useLayoutContext();
  const { knowledgeBaseId: id } = useParams();
  const knowledgeBaseId = id ? parseInt(id, 10) : undefined;
  const [docSearchParams, setDocSearchParams] = useState<DocumentPageRequest>({
    pageNo: 1,
    pageSize: 10,
  });
  const [tableLoading, setTableLoading] = useState<boolean>(true);

  const [currDocId, setCurrDocId] = useState<number>();

  const {
    runAsync: getKnowledgeBaseDetail,
    loading,
    data: knowledgeBaseDetail,
    refreshAsync: refreshKnowledgeBaseDetail,
  } = useRequest<KnowledgeBaseVO, [GetKnowledgeBaseParams]>(getKnowledgeBase);

  const {
    runAsync: getDocumentRes,
    data: documentRes,
    // mutate: mutateDocumentRes,
    refreshAsync: refreshDocumentRes,
  } = useRequest<PageDocumentVO, [any]>(pageDocument);

  const {
    runAsync: doModifyDocumentStatus,
    loading: modifyDocumentStatusLoading,
  } = useRequest<number, [ModifyDocumentStatusParams]>(modifyDocumentStatus);

  useEffect(() => {
    if (knowledgeBaseId) {
      getKnowledgeBaseDetail({
        knowledgeBaseId,
      }).then(res => {
        setBreadcrumbItems([
          {
            type: 'separator',
            separator: <Link to="/knowledge">
              <ArrowLeftOutlined />
            </Link>,
          },
          {
            title: '知识库',
            path: '/knowledge',
          }, {
            title: `${res?.name}`,
          }]);
      });
    }
  }, [knowledgeBaseId, getKnowledgeBaseDetail, setBreadcrumbItems]);

  useEffect(() => {
    if (knowledgeBaseId) {
      setTableLoading(true);
      getDocumentRes({
        knowledgeBaseId: knowledgeBaseId,
        ...docSearchParams,
        pageNo: (docSearchParams?.pageNo ?? 1) - 1,
      }).then(() => {
        setTableLoading(false);
      });
    }
  }, [knowledgeBaseId, docSearchParams, getDocumentRes]);

  const handleModifyStatus = (docId?: number, status?: DocumentVO['status']) => {
    if (docId && status) {
      setCurrDocId(docId);
      doModifyDocumentStatus({
        documentId: docId,
        status: status as any,
      }).then(res => {
        if (res) {
          message.success(`文档已${StatusTextMap[status]}`);
          setCurrDocId(undefined);
          refreshDocumentRes?.();
          // if (status === 'ACTIVE' || status === 'DISABLED') {
          //   // // 立刻改变文档的状态
          //   // mutateDocumentRes(prev => ({
          //   //   ...prev,
          //   //   items: prev?.items?.map(item => {
          //   //     if (item?.id === record?.id) {
          //   //       return {
          //   //         ...item,
          //   //         status,
          //   //       };
          //   //     }
          //   //     return item;
          //   //   }),
          //   // }));
          // } else {
          // }
        }
      });
    }
  };

  const columns = [
    {
      title: '文件名',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: DocumentVO) => (
        <Link
          to={record?.sourceUrl ?? ''}
          target="_blank"
        >
          <FileOutlined style={{ marginInlineEnd: token.marginXXS }} />
          {text}
        </Link>
      ),
    },
    {
      title: '文件来源',
      dataIndex: 'importSource',
      key: 'importSource',
      width: '10%',
      render: (text: string) => {
        return text && (DOC_SOURCE_MAP[text] ?? text);
      },
    },
    {
      title: '文件类型',
      dataIndex: 'docType',
      key: 'docType',
      width: '10%',
      render: (text: string) => {
        return text === 'SHEET' ? '表格' : '文档';
      },
    },
    {
      title: '上传时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      width: '15%',
      render: (text: string) => getTimeStr(text),
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      width: '15%',
      render: (text: string) => getTimeStr(text),
    },
    {
      title: '启用',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      render: (status: DocumentVO['status'], record: DocumentVO) => (
        // TODO: 目前只有active和disable两种状态,其他状态不做处理
        status ? ['ACTIVE', 'DISABLED'].includes(status) ? <Switch
          checked={status === 'ACTIVE'}
          id={`switch-${record?.id}`}
          onChange={(checked) => {
            if (record?.id) {
              handleModifyStatus(record?.id, checked ? 'ACTIVE' : 'DISABLED');
            }
          }}
          loading={currDocId === record?.id && modifyDocumentStatusLoading}
        /> : status : '--'
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '12%',
      render: (_: any, record: DocumentVO) => (
        <Flex
          align="center"
          gap={token.margin}
          className={styles.tableAction}
        >
          <UpdateKnowledgeBase
            knowledgeBaseDetail={knowledgeBaseDetail}
            type={record?.docType === 'SHEET' ? 'tableStructure' : 'chunkConfig'}
            documentDetail={record}
            onRefresh={refreshDocumentRes}
          />
          <Tooltip
            title="查看原文档"
          >
            <a
              href={record?.sourceUrl}
              target="_blank"
            >
              <LinkOutlined
                style={{ fontSize: token.fontSizeLG }}
              />
            </a>
          </Tooltip>
          <Tooltip
            title="移除"
          >
            <InlineButton
              type="text"
              onClick={() => {
                handleModifyStatus(record?.id, 'REMOVED');
              }}
              disabled={record?.status === 'REMOVED'}
            >
              <TrashOutlined
                style={{ fontSize: token.fontSizeLG }}
              />
            </InlineButton>
          </Tooltip>
        </Flex>
      ),
    },
  ];

  return (<Flex
    flex={1}
    vertical
  >
    <Skeleton active loading={loading}>
      <KnowledgeDetailHeader
        knowledgeBaseDetail={knowledgeBaseDetail}
        docCounts={documentRes?.totalCount}
        onSearch={(keyword) => {
          setDocSearchParams(prev => ({
            ...prev,
            title: keyword,
            pageNo: 1,
          }));
        }}
        onRefreshDetail={refreshKnowledgeBaseDetail}
        onRefreshDocs={() => {
          setDocSearchParams(prev => ({
            ...prev,
            pageNo: 1,
          }));
        }}
      />
      <Divider style={{ marginBlock: token.margin }} />
      <Table
        loading={tableLoading}
        columns={columns}
        dataSource={documentRes?.items ?? []}
        style={{
          border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,
          borderRadius: token.borderRadiusLG,
        }}
        pagination={{
          total: documentRes?.totalCount ?? 0,
          pageSize: docSearchParams?.pageSize,
          current: docSearchParams?.pageNo,
          showSizeChanger: true,
          showQuickJumper: true,
          size: 'small',
          style: {
            paddingInlineEnd: token.padding,
          },
          onChange: (page, size) => {
            setDocSearchParams(prev => ({
              ...prev,
              pageNo: page,
              pageSize: size,
            }));
          },
        }}
      />
    </Skeleton>
  </Flex>);
};
