import McpListLoading from '@/pages/mcp/components/McpListLoading';
import { ChevronDownOutlined } from '@ali/mc-icons';
import { Divider, Flex, theme } from 'antd';
import React from 'react';
import styles from './index.module.less';

const LoadMore = ({
  hasMore,
  loading,
  onLoadMore,
}: {
  hasMore: boolean;
  loading: boolean;
  onLoadMore: () => void;
}) => {
  const { token } = theme.useToken();
  return (hasMore ? (
    <Flex
      flex={1}
      align="center"
      justify="center"
      style={{
          marginTop: token.marginSM,
        }}
    >
      {loading ? (
        <McpListLoading />
        ) : (
          <Flex onClick={onLoadMore} className={styles.loadMoreStyle}>
            加载更多
            <ChevronDownOutlined />
          </Flex>
        )}
    </Flex>
    ) : (
      <Divider plain style={{ color: token.colorTextSecondary }}>
        没有更多了
      </Divider>
    ));
};

export default React.memo(LoadMore);
