import React, { useState } from 'react';
import { Flex, Button, List, theme, Typography } from 'antd';
import classNames from 'classnames';
import styles from './index.module.less';
import { secretBookData } from '../constants/secret-book';

type SecretBookCardProps = {
  onClick?: (question?: string) => void;
};

export default function SecretBookCard(props: SecretBookCardProps) {
  const { onClick = () => {} } = props;
  const [activeKey, setActiveKey] = useState<'newComer' | 'develop' | 'pmo'>('newComer');
  const { token } = theme.useToken();

  return (
    <Flex
      vertical
      gap={token.marginLG}
      className={styles.secretBookCard}
    >
      <Flex gap={token.margin} className={styles.secretBookCardHeader}>
        {Object.values(secretBookData)?.map((item: any) => (
          <Button
            key={item.key}
            className={classNames(styles.button, {
              [styles.activeButton]: activeKey === item.key,
              [styles.newComerButton]: item.key === 'newComer',
            })}
            onClick={() => setActiveKey(item.key)}
          >
            <Flex align="center" justify="center" className={styles.dot}>
              {item?.abbr}
            </Flex>
            {item.title}
          </Button>
        ))}
      </Flex>
      <Flex
        key={activeKey}
        vertical
        gap={token.margin}
        style={{
          height: '100%',
          overflowY: 'auto',
        }}
      >
        {secretBookData[activeKey] &&
          secretBookData[activeKey]?.list?.map((item) => (
            <List
              key={item.title}
              className={styles.itemCard}
              header={<div>{item?.title}</div>}
              bordered
              dataSource={item?.questions}
              renderItem={(question) => (
                <List.Item>
                  <Typography.Text ellipsis={{ tooltip: question }}>{question}</Typography.Text>
                  <Button className={styles.tryButton} onClick={() => onClick(question)}>
                    试一试
                  </Button>
                </List.Item>
              )}
            />
          ))}
      </Flex>
    </Flex>
  );
}
