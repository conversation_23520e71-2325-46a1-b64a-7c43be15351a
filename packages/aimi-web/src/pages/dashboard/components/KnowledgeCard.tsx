import { PageMcpServerMetaVO } from '@ali/mc-services/AiMiMcpDataContracts';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Link } from 'ice';
import { Avatar, Divider, Flex, Input, List, theme, Typography, Button } from 'antd';
import AimiUser from '@/components/AimiUser';
import { getTimeStr } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import { removeFalsy } from '@/utils/utils';
import styles from './index.module.less';
import { FileOutlined, ArrowRightOutlined, SearchOutlined } from '@ali/mc-icons';
import {
  AgentPageRequest,
  KnowledgeBasePageRequest,
  KnowledgeBaseVO,
  PageKnowledgeBaseVO,
} from '@ali/mc-services/AiMiDataContracts';
import { pageKnowledgeBase, PageKnowledgeBaseParams } from '@ali/mc-services/AiMiKnowledgeBase';
import { StatusTag } from '@ali/mc-uikit';
import { debounce } from 'lodash-es';
import LoadMore from './LoadMore';

const KnowledgeCard = ({ initData }: { initData?: PageMcpServerMetaVO }) => {
  const { token } = theme.useToken();

  const cardRef = useRef<any>(null);
  const observerRef = useRef<any>(null);

  const [containerHeight, setContainerHeight] = useState<string>();

  useEffect(() => {
    observerRef.current = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        const elementHeight = entry.borderBoxSize?.[0]?.blockSize;
        const upperCardHeight = elementHeight + 16 + 32 + 16;
        setContainerHeight(`calc(100% - ${upperCardHeight}px)`);
      });
    });

    if (cardRef.current) {
      observerRef.current.observe(cardRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  const [searchParams, setSearchParams] = useState<Omit<KnowledgeBasePageRequest, 'pageNo'>>({
    pageSize: 10,
  });

  const pageRef = useRef<number>(0);
  const [data, setData] = useState<KnowledgeBaseVO[]>(initData?.items ?? []);
  const [totalCount, setTotalCount] = useState<number>(initData?.totalCount || 0);
  const [initLoading, setInitLoading] = useState<boolean>(false);

  const { runAsync: getDataList, loading } = useRequest<PageKnowledgeBaseVO, [PageKnowledgeBaseParams]>(
    pageKnowledgeBase,
  );

  const doGetDataList = useCallback((params: AgentPageRequest) => {
    getDataList(
      removeFalsy({
        ...params,
      }),
    ).then((res) => {
      setInitLoading(false);
      setData(prev => [...(prev ?? []), ...res?.items ?? []]);
      if (totalCount !== res?.totalCount) {
        setTotalCount(res?.totalCount || 0);
      }
    });
  }, [getDataList, totalCount]);

  const onLoadMore = useCallback(() => {
    pageRef.current = pageRef.current + 1;
    doGetDataList({
      ...searchParams,
      pageNo: pageRef.current,
    });
  }, [searchParams, doGetDataList]);

  const onFilterChange = (filterName: 'name', filterValue: string) => {
    // 过滤的时候，需要重置当前页和历史数据；
    pageRef.current = 0;
    setData([]);
    setSearchParams((prev) => ({
      ...prev,
      [filterName]: filterValue,
    }));

    setInitLoading(true);
    doGetDataList({
      ...searchParams,
      [filterName]: filterValue,
      pageNo: pageRef.current,
    });
  };

  const hasMore = useMemo(() => {
    return totalCount > 0 && data?.length > 0 && totalCount > data.length;
  }, [data, totalCount]);

  return (
    <>
      <Flex
        justify="space-between"
        align="center"
        gap={token.marginXL}
        ref={cardRef}
        style={{
          border: `${token.lineWidth}px ${token.lineType} ${token.colorSplit}`,
          borderRadius: token.borderRadiusLG,
          paddingBlock: token.paddingLG,
          paddingInline: token.paddingLG,
          marginBottom: token.margin,
        }}
      >
        <Flex
          flex="auto"
          style={{
            color: token.colorTextSecondary,
            lineHeight: token.lineHeight,
          }}
        >
          <span>
            知识库通过整合并解析您的专属文本数据，为大型语言模型注入领域认知燃料，支持自定义新建知识库完成内容管理。
            <Link
              to="https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iv0x76DNJDOnGvpb?corpId=dingd8e1123006514592&utm_medium=im_card&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_scene=team_space&utm_source=im"
              target="knowledge_doc"
              style={{
                display: 'inline-block',
              }}
            >
              最佳实践 <ArrowRightOutlined />
            </Link>
          </span>
        </Flex>
        <Flex flex={1}>
          <Link to="/knowledge?createKnowledgeBase=true" target="_blank">
            <Button type="primary">立即新建</Button>
          </Link>
        </Flex>
      </Flex>
      <Flex
        align="center"
        style={{
          marginBottom: token.margin,
          width: 'fit-content',
        }}
      >
        <Input
          placeholder="输入知识库名称"
          allowClear
          prefix={
            <SearchOutlined
              style={{
                color: token.colorTextSecondary,
              }}
            />
          }
          onChange={debounce((e: any) => {
            onFilterChange('name', e?.target?.value);
          }, 300)}
        />
      </Flex>

      <List
        style={{
          height: containerHeight,
          overflowY: 'auto',
        }}
        size="small"
        grid={{
          xs: 1,
          sm: 1,
          md: 1,
          lg: 1,
          xl: 1,
          xxl: 1,
        }}
        loading={initLoading}
        itemLayout="vertical"
        loadMore={<LoadMore
          hasMore={hasMore}
          onLoadMore={onLoadMore}
          loading={loading}
          key="knowledge_loadMore"
        />}
        dataSource={data ?? []}
        renderItem={(item: KnowledgeBaseVO) => (
          <List.Item
            className={styles.listItemStyle}
            style={{
              border: `${token.lineWidth}px ${token.lineType} ${token.colorSplit}`,
              borderRadius: token.borderRadiusLG,
              paddingBlock: token.padding,
              paddingInline: token.paddingLG,
            }}
            key={`${item?.id}_knowledge_list_item`}
            extra={
              <Link to={`/knowledge/${item?.id}/detail`} target={`${item?.id}_knowledge_detail`}>
                <Button>查看详情</Button>
              </Link>
            }
          >
            <List.Item.Meta
              avatar={<Avatar icon={<FileOutlined />} size={42} shape="square" />}
              title={
                <Typography.Text
                  ellipsis={{
                    tooltip: item?.name,
                  }}
                >
                  {item?.name}
                </Typography.Text>
              }
              description={
                <Flex
                  align="center"
                  style={{
                    fontSize: token.fontSizeSM,
                    fontWeight: 'normal',
                  }}
                  gap={token.marginXS}
                >
                  <StatusTag
                    style={{
                      height: token.controlHeightSM - token.sizeUnit,
                      borderRadius: 10,
                      marginInlineEnd: 0,
                    }}
                  >
                    {item?.bizGroupName}
                  </StatusTag>
                  <Divider type="vertical" style={{ marginInline: 0 }} />
                  <div>{`${item?.documentCount} 文档`}</div>
                </Flex>
              }
            />
            <Typography.Paragraph
              style={{
                color: token.colorTextSecondary,
                fontSize: token.fontSizeSM,
              }}
              ellipsis={{
                tooltip: item?.description,
                rows: 2,
              }}
            >
              {item?.description}
            </Typography.Paragraph>
            <Divider style={{ marginBlockStart: 0, marginBlockEnd: token.margin }} />
            <Flex
              justify="space-between"
              style={{
                fontSize: token.fontSizeSM,
                color: token.colorTextSecondary,
              }}
            >
              <div>
                <AimiUser empIds={item?.creator} showAvatar size={20} />
              </div>
              <div>{item?.gmtCreate ? getTimeStr(item?.gmtCreate) : '--'}</div>
            </Flex>
          </List.Item>
        )}
      />
    </>
  );
};

export default React.memo(KnowledgeCard);
