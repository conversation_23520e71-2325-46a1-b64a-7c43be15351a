.listItemStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  :global {
    .aimi-list-item-extra {
      position: absolute;
      right: 24px;
      top: 24px;
    }
    .aimi-list-item-meta-title {
      margin-bottom: 0px !important;
    }
    .aimi-list-item-meta {
      margin-bottom: 8px !important;
    }
  }
}

.loadMoreStyle {
  cursor: pointer;
  color: var(--mc-color-text-secondary);
  gap: var(--mc-margin-xxs);
  // font-size: var(--mc-font-size-lg);
}

.aniButton {
  position: relative;
  color: var(--mc-color-text-secondary);
  font-weight: var(--mc-font-weight);
  background-color: transparent;
  border-radius: 32px;
  font-size: var(--mc-font-size-sm);
  height: 32px;
  gap: 0;
  overflow: hidden;
  padding: 0 var(--mc-padding-sm) 0 var(--mc-padding-xxs);
  &:hover {
    background-color: var(--mc-color-fill-content);
  }
  &::before {
    content: '';
    opacity: 0;
    border-radius: var(--radius-full);
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent 20%, #5a93fc4d 50%, transparent 80%);
    transform: skewX(-20deg);
    animation: shine_animation 9s ease-in-out infinite;
  }
  // 特殊动画
  .aniDots {
    position: relative;
    width: 24px;
    .smallDot {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #8250df;
      width: 4px;
      height: 4px;
      border-radius: 50%;
    }
    .bigDot {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: rgb(130, 80, 223);
      animation: pulse_animation 2s ease-out infinite;
    }
  }
}

.secretBookCard {
  height: 100%;
  .secretBookCardHeader {
    .button {
      position: relative;
      background-color: transparent;
      border-radius: 32px;
      height: 32px;
      padding-left: calc(32px + 14px);
      padding-right: 23px;
      background-color: #010409;;
      border-color: #3d444d;
      .dot {
        position: absolute;
        top: -1px;
        left: -1px;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
      }
      &:hover {
        background-color: rgba(101, 108, 118, 0.2);
      }
    }

    .activeButton {
      background-color: rgba(101, 108, 118, 0.5);
      border-color: #3d444d;
      .dot {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    .newComerButton {
      border-color: rgba(255, 98, 0, 0.5);
      background: #0d1117;
      .dot {
        background-color: rgba(255, 95, 0, 0.25);
      }
      &.activeButton {
        background-color: #0D1117;
        border-color: #FF5F00;
        .dot {
          background-color: #FF5F00;
        }
      }
      &:hover {
        background: rgba(255, 98, 0, 0.2);
        border-color: rgba(255, 98, 0, 0.5);
        .dot {
          background-color: rgba(255, 95, 0, 0.25);
        }
      }
    }
  }
  .itemCard {
    :global {
      .aimi-list-header {
        background-color: rgba(13, 17, 23, 0.7);
        backdrop-filter: blur(5px);
        border-top-left-radius: calc(var(--mc-border-radius) * 2);
        border-top-right-radius: calc(var(--mc-border-radius) * 2);
      }
      .aimi-list-items {
        background-color: rgba(1, 4, 9, 0.7);
        backdrop-filter: blur(5px);
        border-bottom-left-radius: calc(var(--mc-border-radius) * 2);
        border-bottom-right-radius: calc(var(--mc-border-radius) * 2);
      }
    }
    .tryButton {
      margin-left: var(--mc-margin-xs);
    }
  }
}

@keyframes pulse_animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes shine_animation {
  0% {
    left: -100%;
  }
  1% {
    left: -100%;
    opacity: 1;
  }
  15% {
    left: 100%;
    opacity: 1;
  }
  16% {
    opacity: 0;
  }
  100% {
    left: -100%;
  }
}
