// 文件位置: core/llm/llms/AIMI.ts

import { LLMOptions } from "../../index.js";
import { LLMConfigurationStatuses } from "../constants.js";
import OpenAI from "./OpenAI.js";
import { LlmApiRequestType } from "../openaiTypeConverters";

/**
 * AIMI模型实现
 *
 * 基于OpenAI API格式的内部模型服务
 */
class AIMI extends OpenAI {
  static providerName = "aimi";
  static defaultOptions: Partial<LLMOptions> = {
    apiBase: "https://idealab.alibaba-inc.com/api/openai/v1/",
    model: "gpt-4o-0806",
    apiKey: "8e25c3cf1b8f06af9f33bcb8eb4c06c2",
    contextLength: 8192, // GPT-4o的上下文长度
    maxEmbeddingBatchSize: 5,
    roles: ["chat", "edit", "apply"], // 默认角色
    capabilities: {
      tools: true,
      uploadImage: true,
    },
  };

  constructor(options: LLMOptions) {
    super(options);
    // 确保API基础URL以"/"结尾
    if (this.apiBase && !this.apiBase.endsWith("/")) {
      this.apiBase = `${this.apiBase}/`;
    }
  }

  protected useOpenAIAdapterFor: (LlmApiRequestType | "*")[] = [
    "chat",
    "embed",
    "list",
    "rerank",
    "streamChat",
    "streamComplete",
  ];

  /**
   * 检查配置状态
   */
  getConfigurationStatus() {
    if (!this.apiKey || !this.apiBase) {
      return LLMConfigurationStatuses.MISSING_API_KEY;
    }

    return LLMConfigurationStatuses.VALID;
  }

  supportsPrediction(): boolean {
    return true; // GPT-4o支持预测
  }

  /**
   * 检查是否支持流式补全
   */
  supportsFim(): boolean {
    return false; // GPT-4o支持流式补全
  }

  // 支持图像
  supportsImages(): boolean {
    return false; // GPT-4o支持图像
  }
}

export default AIMI;
