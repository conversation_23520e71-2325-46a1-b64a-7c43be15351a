---
title: Rules Blocks
sidebar_label: Rules
description: Guardrails for your AI coding assistants
keywords: [rules, blocks, standards, practices, guardrails]
sidebar_position: 5
---

Rules allow you to provide specific instructions that guide how the AI assistant behaves when working with your code. Instead of the AI making assumptions about your coding standards, architecture patterns, or project-specific requirements, you can explicitly define guidelines that ensure consistent, contextually appropriate responses.

Think of these as the guardrails for your AI coding assistants:

- **Enforce company-specific coding standards** and security practices
- **Implement quality checks** that match your engineering culture
- **Create paved paths** for developers to follow organizational best practices

By implementing rules, you transform the AI from a generic coding assistant into a knowledgeable team member that understands your project's unique requirements and constraints.

### How Rules Work

Your assistant detects rule blocks and applies the specified rules while in [Agent](../agent/how-to-use-it), [Chat](../chat/how-to-use-it), and [Edit](../edit/how-to-use-it) modes.

## Learn More

Learn more in the [rules deep dive](../customize/deep-dives/rules.md), and view [`rules`](../reference.md#rules) in the YAML Reference for more details.
