---
title: Amazon Bedrock
slug: ../bedrock
---

## 聊天模型

我们推荐配置 **Claude 3.7 Sonnet** 作为你的聊天模型。

```json title="config.json"
{
  "models": [
    {
      "title": "Claude 3.7 Sonnet",
      "provider": "bedrock",
      "model": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
      "region": "us-east-1",
      "profile": "bedrock"
    }
  ]
}
```

## 自动补全模型

Bedrock 当前不支持任何自动补全模型。

[点击这里](../../model-roles/autocomplete.md) 查看自动补全模型提供者列表。

## 嵌入模型

我们推荐配置 [`amazon.titan-embed-text-v2:0`](https://docs.aws.amazon.com/bedrock/latest/devguide/models.html#amazon.titan-embed-text-v2-0) 作为你的嵌入模型。

```json title="~/.continue/config.json"
{
  "embeddingsProvider": {
    "title": "Embeddings Model",
    "provider": "bedrock",
    "model": "amazon.titan-embed-text-v2:0",
    "region": "us-west-2"
  }
}
```

## 重排序模型

Bedrock 当前没有任何重排序模型。

[点击这里](../../model-roles/reranking.md) 查看重排序模型提供者列表。

## 提示词缓存

Bedrock 支持 Claude 模型的提示词缓存。 在 `defaultCompletionOptions`
中设置 `promptCaching: true` 即可缓存工具调用、系统消息和聊天消息。

提示词缓存目前适用于以下模型：

- Claude 3.7 Sonnet
- Claude 3.5 Haiku
- Amazon Nova Micro
- Amazon Nova Lite
- Amazon Nova Pro

在预览期间获准使用 Claude 3.5 Sonnet v2 的用户仍可继续使用提示词缓存，但该模型不会再向新的用户开放此功能。

```yaml title="config.yaml"
models:
  - name: Claude 3.7 Sonnet
    provider: bedrock
    model: us.anthropic.claude-3-7-sonnet-20250219-v1:0
    defaultCompletionOptions:
      promptCaching: true
```

提示词缓存不支持 JSON 配置文件，只能在 YAML 中启用。

## 认证

认证可以通过 `~/.aws/credentials` 配置属性中临时的或长期的证书，(例如 "bedrock") 。

```title="~/.aws/credentials
[bedrock]
aws_access_key_id = abcdefg
aws_secret_access_key = hijklmno
aws_session_token = pqrstuvwxyz # Optional: means short term creds.
```

## 定制导入模型

为了设置 Bedrock 使用定制导入模型，将下面的配置添加到你的 `config.json` 文件中：

```json title="config.json"
{
  "models": [
    {
      "title": "AWS Bedrock deepseek-coder-6.7b-instruct",
      "provider": "bedrockimport",
      "model": "deepseek-coder-6.7b-instruct",
      "modelArn": "arn:aws:bedrock:us-west-2:XXXXX:imported-model/XXXXXX",
      "region": "us-west-2",
      "profile": "bedrock"
    }
  ]
}
```

认证可以通过 `~/.aws/credentials` 配置属性中临时的或长期的证书，(例如 "bedrock") 。

```title="~/.aws/credentials
[bedrock]
aws_access_key_id = abcdefg
aws_secret_access_key = hijklmno
aws_session_token = pqrstuvwxyz # Optional: means short term creds.
```
