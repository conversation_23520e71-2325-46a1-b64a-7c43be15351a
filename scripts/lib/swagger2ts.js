const path = require('path');
const fs = require('fs');
const { generateApi } = require('swagger-typescript-api');
const { fixRefString, cleanRefString } = require('./utils');

module.exports.genAPI = (url, output, options = {}) => {
  const {
    modulePrefix = '',
    modules,
    routeNameAlias,
    routeConfig,
    baseUrl,
    jsonSuffix,
  } = options;

  return generateApi({
    output,
    url,
    baseUrl,
    jsonSuffix,
    generateClient: true,
    // generateRouteTypes: true,
    generateResponses: false,
    unwrapResponseData: true,
    extractRequestParams: false,
    extractRequestBody: false,
    extractEnums: false,
    enumNamesAsValues: true,
    // unionEnums: false,
    generateUnionEnums: true,

    modular: true,
    moduleNameFirstTag: true,

    // toJS: true,
    templates: path.resolve(process.cwd(), './scripts/templates'),
    fileNames: {
      dataContracts: `${modulePrefix}DataContracts`,
      httpClient: 'HttpClient',
    },
    hooks: {
      onCreateRouteName: (routeNameInfo, rawRouteInfo) => {
        const routeName = `${rawRouteInfo.method.toUpperCase()}:${rawRouteInfo.route}`;
        const alias = routeNameAlias?.[routeName];
        // 处理一下命名冲突的映射
        const _routeNameInfo = { ...routeNameInfo };
        if (alias) {
          _routeNameInfo.usage = alias;
          _routeNameInfo.original = alias;
        } else {
          _routeNameInfo.usage = routeNameInfo.usage.replace(/Using(Post|Get|Put|Delete)\d?$/, '');
          _routeNameInfo.original = routeNameInfo.original.replace(/Using(Post|Get|Put|Delete)\d?$/, '');
        }

        return _routeNameInfo;
      },
      onCreateRoute: (routeData) => {
        let mapped = false;
        if (routeData?.raw?.tags) {
          const tags = [];
          routeData?.raw?.tags.forEach(tag => {
            const cfg = modules[tag];
            if (cfg) {
              mapped = true;
              let newTag;
              if (typeof cfg === 'string') {
                newTag = cfg;
              } else if (cfg.alias) {
                newTag = cfg.alias;
              } else if (cfg.routeTagMap) {
                // API 层面的
                const tagName = cfg.routeTagMap[routeData.request.path];
                if (tagName) {
                  newTag = tagName;
                }
              } else {
                newTag = tag;
                mapped = false;
              }

              newTag && tags.push(`${modulePrefix}${newTag}`);
            }
          });
          // eslint-disable-next-line no-param-reassign
          routeData.raw.tags = [...tags];
        }

        // console.log('mapped..........', mapped);

        if (mapped) {
          // eslint-disable-next-line no-param-reassign
          routeData.namespace = routeData.raw.tags[0];
          // 处理返回类型的映射
          const returnTypeAlias = routeConfig[routeData.namespace]?.returnTypeAlias;

          // 有部分的接口生成的 Swagger 规范不一致（如AI），这里需要修补一下
          ['*/*', 'application/json'].forEach((key) => {
            let $ref = routeData.responseBodyInfo.success.schema?.content?.[key]?.schema.$ref;
            // console.log('$ref', $ref);
            if ($ref) {
              routeData.responseBodyInfo.success.schema.content[key].schema.$ref = fixRefString($ref);
            }
          });

          const ref = routeData.responseBodyInfo.success.schema?.content?.['*/*']?.schema.$ref
            || routeData.responseBodyInfo.success.schema?.content?.['application/json']?.schema.$ref;
          if (returnTypeAlias) {
            if (returnTypeAlias && ref) {
              // eslint-disable-next-line guard-for-in
              for (const key in returnTypeAlias) {
                const [routeName, returnType] = key.split('.');
                if (routeName === routeData.routeName.usage) {
                  // eslint-disable-next-line no-param-reassign
                  routeData.responseBodyInfo.success.schema.content['*/*'].schema.$ref = ref.replace(returnType, returnTypeAlias[key]);
                }
              }
            }
          }

          if (ref) {
            const returnOriginType = routeConfig[routeData.namespace]?.returnOriginType || [];
            if (returnOriginType.includes(routeData.routeName.usage)) {
              if (routeData.responseBodyInfo.success.schema?.content?.['*/*']) {
                // eslint-disable-next-line no-param-reassign
                routeData.responseBodyInfo.success.schema.content['*/*'].schema.returnOriginType = true;
              }

              if (routeData.responseBodyInfo.success.schema?.content?.['application/json']) {
                // eslint-disable-next-line no-param-reassign
                routeData.responseBodyInfo.success.schema.content['application/json'].schema.returnOriginType = true;
              }
            }
          }
          return routeData;
        }
        return false;
      },
      onCreateComponent: (component) => {
        if (component.componentName !== 'schemas') {
          // 这个自定义了一下mainEntity的泛型支持
          return component;
        }

        // if (component.typeName === 'IterationVO') {
        //   return {};
        // }

        const typeName = component.typeName;
        if (typeName && (typeName.startsWith('WebResult') ||
          typeName.includes('WebPaged') ||
          typeName.includes('WebList') ||
          (typeName.startsWith('BizResult') && typeName !== ('BizResult'))
        )) {
         if (!typeName.includes('«') && !['WebResult', 'WebPaged', 'WebList', 'BizResult'].includes(typeName)) {
          component.typeName = cleanRefString(typeName);
          component.$ref = fixRefString(component.$ref);
          // console.log(typeName, component.typeName, component);
         } else {
          return {
            $ref: '',
          };
         }
        }

        return component;
      },
    },
  })
    .then(({ files /* , configuration */ }) => {
      const pkgFile = path.resolve(process.cwd(), './packages/mc-services/package.json');
      const pkg = JSON.parse(fs.readFileSync(pkgFile, 'utf-8'));
      if (!pkg.exports) {
        pkg.exports = {
          '.': './esm/index.js',
        };
      }

      if (!pkg.typesVersions?.['*']) {
        pkg.typesVersions = {
          '*': {},
        };
      }

      // 添加/更新 exports
      for (const file of files) {
        const { fileName } = file;
        if (fileName !== 'DataContracts' && fileName !== 'HttpClient') {
          pkg.exports[`./${fileName}`] = `./esm/${fileName}.js`;
          pkg.exports[`./esm/${fileName}`] = `./esm/${fileName}.js`;
          pkg.exports[`./cjs/${fileName}`] = `./cjs/${fileName}.js`;
          pkg.typesVersions['*'][`${fileName}`] = [`./esm/${fileName}.d.ts`];
        }
      }

      // 删除已经不存在的
      if (pkg.exports) {
        for (const key in pkg.exports) {
          if (key !== '.' && !fs.existsSync(path.resolve(process.cwd(), `./packages/mc-services/src/${key.replace('./cjs', './').replace('./esm', './')}.ts`))) {
            pkg.exports[key] = undefined;
          }
        }
      }

      pkg.exports['./cjs/Request'] = './cjs/Request/index.js';
      if (pkg.typesVersions?.['*']) {
        for (const key in pkg.typesVersions['*']) {
          if (!fs.existsSync(path.resolve(process.cwd(), `./packages/mc-services/src/${key}.ts`))) {
            pkg.typesVersions['*'][key] = undefined;
          }
        }
      }

      fs.writeFileSync(pkgFile, JSON.stringify(pkg, null, 2));
    })
    .then(() => {
      process.exit();
    })
    .catch(_e => console.error(_e));
};
