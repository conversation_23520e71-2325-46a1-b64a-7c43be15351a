package com.taobao.mc.aimi.execution

import com.intellij.execution.process.ProcessAdapter
import com.intellij.execution.process.ProcessEvent
import com.intellij.execution.process.ProcessHandler
import com.intellij.execution.ui.RunContentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import kotlinx.coroutines.*

class AIMITerminalInfo(
    private val toolUserId: String,
    var terminalId: Long,
    var command: String,
    var workspaceDir: String,
) : ProcessAdapter() {

    // 存储命令执行的输出结果
    private val outputBuffer = StringBuilder()

    // 标记进程是否已终止
    @Volatile
    private var isTerminated = false

    // 进程退出代码
    @Volatile
    private var exitCode: Int? = null

    // 进程终止的延迟对象
    private val terminationDeferred = CompletableDeferred<Int>()

    /**
     * 获取命令执行的输出结果（非阻塞）
     * @return 当前命令执行的输出结果
     */
    @Synchronized
    fun getCurrentOutput(): String {
        return outputBuffer.toString()
    }

    /**
     * 获取命令执行的输出结果（支持等待进程终止）
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 命令执行的输出结果
     */
    suspend fun getOutput(timeoutMillis: Long = 30000): String {
        return try {
            // 如果进程已终止，直接返回输出
            if (isTerminated) {
                getCurrentOutput()
            } else {
                // 等待进程终止，最多等待指定的时间
                withTimeout(timeoutMillis) {
                    terminationDeferred.await()
                    withContext(Dispatchers.IO) {
                        getCurrentOutput()
                    }
                }
            }
        } catch (e: Throwable) {
            // 异常时返回当前的输出
            getCurrentOutput()
        }
    }

    /**
     * 清空输出缓冲区
     */
    @Synchronized
    fun clearOutput() {
        outputBuffer.clear()
    }

    // 处理进程输出
    @Synchronized
    override fun onTextAvailable(event: ProcessEvent, outputType: Key<*>) {
        // 将输出添加到缓冲区
        outputBuffer.append(event.text)
    }

    // 进程终止时的处理
    @Synchronized
    override fun processTerminated(event: ProcessEvent) {
        // 记录进程退出代码
        exitCode = event.exitCode
        // 标记进程已终止
        isTerminated = true
        // 完成延迟对象，通知等待的协程
        terminationDeferred.complete(event.exitCode)
    }

    /**
     * 获取进程是否已终止
     * @return 进程是否已终止
     */
    fun isTerminated(): Boolean = isTerminated

    /**
     * 获取进程退出代码
     * @return 进程退出代码，如果进程未终止则返回null
     */
    fun getExitCode(): Int? = exitCode

    /**
     * 等待进程终止（协程版本）
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 是否成功等待进程终止（true表示进程已终止，false表示超时）
     */
    suspend fun waitForTermination(timeoutMillis: Long = 30000): Boolean {
        if (isTerminated) return true

        return try {
            // 等待进程终止，最多等待指定的时间
            withTimeout(timeoutMillis) {
                terminationDeferred.await()
                true
            }
        } catch (e: Exception) {
            when (e) {
                is CancellationException -> {
                    // 协程被取消
                    isTerminated
                }

                else -> {
                    // 其他异常
                    false
                }
            }
        }
    }

    /**
     * 等待进程终止（非协程版本，兼容旧代码）
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 是否成功等待进程终止（true表示进程已终止，false表示超时）
     */
    fun waitForTerminationBlocking(timeoutMillis: Long = 30000): Boolean {
        if (isTerminated) return true

        val endTime = System.currentTimeMillis() + timeoutMillis
        while (!isTerminated && System.currentTimeMillis() < endTime) {
            try {
                Thread.sleep(100) // 等待100毫秒后再次检查
            } catch (e: InterruptedException) {
                // 如果被中断，则返回当前状态
                return isTerminated
            }
        }

        return isTerminated
    }

    /**
     * 获取带有退出状态的输出结果（协程版本）
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 包含输出内容和退出状态的结果对象
     */
    suspend fun getOutputWithStatus(timeoutMillis: Long = 30000): TerminalResult {
        val output = getOutput(timeoutMillis)
        return TerminalResult(output, isTerminated, exitCode)
    }

    /**
     * 获取带有退出状态的输出结果（非协程版本，兼容旧代码）
     * @param waitForTermination 是否等待进程终止
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 包含输出内容和退出状态的结果对象
     */
    fun getOutputWithStatusBlocking(waitForTermination: Boolean = false, timeoutMillis: Long = 30000): TerminalResult {
        if (waitForTermination) {
            waitForTerminationBlocking(timeoutMillis)
        }
        return TerminalResult(getCurrentOutput(), isTerminated, exitCode)
    }

    /**
     * 将终端信息附加到进程处理器
     * @param processHandler 进程处理器
     */
    fun attach(processHandler: ProcessHandler) {
        // 设置用户数据
        TERMINAL_INFO_KEY.set(processHandler, this)

        // 添加进程监听器
        processHandler.addProcessListener(this)
    }

    companion object {
        // 使用更具体的名称来避免冲突
        val TERMINAL_INFO_KEY = Key<AIMITerminalInfo>("com.taobao.mc.aimi.execution.terminal-info")

        fun findActiveTerminal(project: Project): AIMITerminalInfo? {
            val runManager = RunContentManager.getInstance(project)
            // 首先尝试从当前选中的内容获取终端信息
            runManager.selectedContent?.processHandler?.getUserData(TERMINAL_INFO_KEY)?.let {
                return it
            }

            // 如果没有找到，则从所有描述符中查找第一个有效的终端信息
            return runManager.allDescriptors
                .firstNotNullOfOrNull { descriptor ->
                    descriptor?.processHandler?.getUserData(TERMINAL_INFO_KEY)
                }
        }
    }
}