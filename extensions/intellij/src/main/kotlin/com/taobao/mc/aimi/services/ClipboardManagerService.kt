package com.taobao.mc.aimi.services

import com.github.continuedev.continueintellijextension.editor.EditorUtils
import com.github.continuedev.continueintellijextension.editor.RangeInFileWithContents
import com.intellij.openapi.application.EDT
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.*
import java.awt.Toolkit
import java.awt.datatransfer.DataFlavor
import java.awt.datatransfer.UnsupportedFlavorException
import java.io.IOException
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 剪切板管理服务
 * 实现对全局剪切板的监听，记录剪切板内容变更历史
 * 当内容来自当前IDE时，同时记录文件路径、行号等详细信息
 */
@Service(Service.Level.PROJECT)
class ClipboardManagerService(private val project: Project) {
    private val logger = LoggerManager.getLogger(ClipboardManagerService::class.java)
    private val systemClipboard = Toolkit.getDefaultToolkit().systemClipboard
    private val isMonitoring = AtomicBoolean(false)

    // 创建服务级别的协程作用域
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 剪切板历史记录
    private val clipboardHistory = ConcurrentLinkedQueue<ClipboardEntry>()
    private val maxHistorySize = 10

    // 当前剪切板状态
    private val lastClipboardContent = AtomicReference<String>()
    private val lastEditorSelection = AtomicReference<RangeInFileWithContents?>()

    // 监听间隔（毫秒）
    private val pollingInterval = 500L

    companion object {
        fun getInstance(project: Project): ClipboardManagerService = project.service()
    }

    /**
     * 剪切板条目数据类
     */
    data class ClipboardEntry(
        val content: String,
        val timestamp: Long,
        val source: ClipboardSource,
        val rangeInFile: RangeInFileWithContents? = null
    ) {
        fun isIdentyTo(other: ClipboardEntry): Boolean {
            // 主要比较内容是否相同，忽略时间戳
            return this.content == other.content &&
                    this.source == other.source &&
                    (this.rangeInFile?.filepath == other.rangeInFile?.filepath &&
                            this.rangeInFile?.range == other.rangeInFile?.range)
        }
    }

    /**
     * 剪切板内容来源
     */
    enum class ClipboardSource {
        IDE,  // IDE内部复制
        EXTERNAL       // 外部应用复制
    }

    /**
     * 启动剪切板监听
     */
    fun startMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            logger.info("启动剪切板监听服务")

            // 初始化当前剪切板内容
            initializeClipboardContent()

            // 启动轮询监听
            startPollingClipboard()

            // 注册编辑器监听器
            registerEditorListeners()
        }
    }

    /**
     * 停止剪切板监听
     */
    fun stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            logger.info("停止剪切板监听服务")
            serviceScope.cancel()
        }
    }

    /**
     * 初始化剪切板内容
     */
    private fun initializeClipboardContent() {
        try {
            val content = getClipboardText()
            if (content != null) {
                lastClipboardContent.set(content)
            }
        } catch (e: Exception) {
            logger.warn("初始化剪切板内容失败: ${e.message}")
        }
    }

    /**
     * 启动轮询监听剪切板
     */
    private fun startPollingClipboard() {
        serviceScope.launch {
            while (isMonitoring.get()) {
                try {
                    checkClipboardChanges()
                    delay(pollingInterval)
                } catch (e: Exception) {
                    logger.error("轮询剪切板时发生异常: ${e.message}")
                    delay(pollingInterval * 2) // 出错时延长间隔
                }
            }
        }
    }

    /**
     * 检查剪切板内容变化
     */
    private suspend fun checkClipboardChanges() {
        val currentContent = getClipboardText() ?: return
        val lastContent = lastClipboardContent.get()

        if (currentContent != lastContent && currentContent.isNotEmpty()) {
            lastClipboardContent.set(currentContent)
            handleClipboardContentChanged(currentContent)
        }
    }

    /**
     * 处理剪切板内容变化
     */
    private suspend fun handleClipboardContentChanged(content: String) {
        withContext(Dispatchers.EDT) {
            try {
                val currentSelection = getCurrentEditorSelection()
                val source = determineClipboardSource(content, currentSelection)

                val entry = ClipboardEntry(
                    content = content,
                    timestamp = System.currentTimeMillis(),
                    source = source,
                    rangeInFile = if (source == ClipboardSource.IDE) currentSelection else null
                )

                logger.info("剪切板内容变更: 来源=${source.name}, 长度=${content.length}")

                if (source == ClipboardSource.IDE && currentSelection != null) {
                    logger.info("IDE内复制: 文件=${currentSelection.filepath}, 行号=${currentSelection.range}")
                }

                addToHistory(entry)
            } catch (e: Exception) {
                logger.error("处理剪切板内容变化时发生异常: ${e.message}")
            }
        }
    }

    /**
     * 获取剪切板文本内容
     */
    private fun getClipboardText(): String? {
        return try {
            if (systemClipboard.isDataFlavorAvailable(DataFlavor.stringFlavor)) {
                systemClipboard.getData(DataFlavor.stringFlavor) as? String
            } else {
                null
            }
        } catch (e: UnsupportedFlavorException) {
            null
        } catch (e: IOException) {
            null
        } catch (e: Exception) {
            logger.warn("获取剪切板内容失败: ${e.message}")
            null
        }
    }

    /**
     * 获取当前编辑器选择内容
     */
    private fun getCurrentEditorSelection(): RangeInFileWithContents? {
        return try {
            EditorUtils.getEditor(project)?.getHighlightedRIF()
        } catch (e: Exception) {
            logger.debug("获取编辑器选择内容失败: ${e.message}")
            null
        }
    }

    /**
     * 判断剪切板内容来源
     */
    private fun determineClipboardSource(
        clipboardContent: String,
        currentSelection: RangeInFileWithContents?
    ): ClipboardSource {
        return if (currentSelection != null && currentSelection.contents == clipboardContent) {
            ClipboardSource.IDE
        } else {
            ClipboardSource.EXTERNAL
        }
    }

    /**
     * 添加到历史记录
     */
    private fun addToHistory(entry: ClipboardEntry) {
        // 避免重复添加相同内容
        val lastEntry = clipboardHistory.peek()
        if (lastEntry?.isIdentyTo(entry) == true) {
            return
        }
        logger.info("添加到剪切板历史记录: source:${entry.source}, range=${entry.rangeInFile?.range}, file=${entry.rangeInFile?.filepath}")

        clipboardHistory.offer(entry)

        // 限制历史记录大小
        while (clipboardHistory.size > maxHistorySize) {
            clipboardHistory.poll()
        }
    }

    /**
     * 注册编辑器监听器
     */
    private fun registerEditorListeners() {
        val connection = project.messageBus.connect(serviceScope)

        connection.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, object : FileEditorManagerListener {
            override fun selectionChanged(event: FileEditorManagerEvent) {
                // 当编辑器选择变化时，更新当前选择状态
                serviceScope.launch(Dispatchers.EDT) {
                    try {
                        val currentSelection = getCurrentEditorSelection()
                        lastEditorSelection.set(currentSelection)
                    } catch (e: Exception) {
                        logger.debug("更新编辑器选择状态失败: ${e.message}")
                    }
                }
            }
        })
    }

    // ==================== 公共API方法 ====================

    /**
     * 获取剪切板历史记录
     */
    fun getClipboardHistory(): List<ClipboardEntry> {
        return clipboardHistory.toList().reversed() // 最新的在前
    }

    /**
     * 获取最近的剪切板条目
     */
    fun getLatestClipboardEntry(): ClipboardEntry? {
        return clipboardHistory.peek()
    }

    /**
     * 获取最近的IDE内复制条目
     */
    fun getLatestIdeClipboardEntry(): ClipboardEntry? {
        return clipboardHistory.toList()
            .lastOrNull { it.source == ClipboardSource.IDE }
    }

    /**
     * 获取指定数量的最近剪切板条目
     */
    fun getRecentClipboardEntries(count: Int): List<ClipboardEntry> {
        return clipboardHistory.toList()
            .takeLast(count)
            .reversed()
    }

    /**
     * 清空剪切板历史记录
     */
    fun clearHistory() {
        clipboardHistory.clear()
        logger.info("剪切板历史记录已清空")
    }

    /**
     * 获取当前剪切板内容
     */
    fun getCurrentClipboardContent(): String? {
        return lastClipboardContent.get()
    }

    /**
     * 检查是否正在监听
     */
    fun isMonitoring(): Boolean {
        return isMonitoring.get()
    }

    // ==================== 兼容性方法 ====================

    /**
     * 获取最后复制的内容（兼容旧接口）
     */
    val lastContent: String?
        get() = getLatestClipboardEntry()?.content

    /**
     * 获取最后的RIF信息（兼容旧接口）
     */
    val lastRIF: RangeInFileWithContents?
        get() = getLatestClipboardEntry()?.rangeInFile

    /**
     * 获取最后复制时间（兼容旧接口）
     */
    val lastCopiedTime: Long
        get() = getLatestClipboardEntry()?.timestamp ?: System.currentTimeMillis()
}