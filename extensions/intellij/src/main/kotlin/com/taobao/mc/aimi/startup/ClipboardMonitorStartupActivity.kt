package com.taobao.mc.aimi.startup

import com.intellij.openapi.Disposable
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import com.taobao.mc.aimi.services.ClipboardManagerService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ClipboardMonitorStartupActivity : ProjectActivity, Disposable, DumbAware {
    private var service: ClipboardManagerService? = null

    override suspend fun execute(project: Project) {
        // 获取并启动剪贴板监听服务
        service = ClipboardManagerService.getInstance(project)
        withContext(Dispatchers.IO) {
            service?.startMonitoring()
        }
    }

    override fun dispose() {
        service?.stopMonitoring()
        service = null
    }
}