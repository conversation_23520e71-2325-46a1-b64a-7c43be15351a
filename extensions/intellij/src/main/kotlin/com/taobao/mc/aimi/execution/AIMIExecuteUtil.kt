package com.taobao.mc.aimi.execution

import com.github.continuedev.continueintellijextension.protocol.TerminalOptions
import com.intellij.execution.ExecutionManager
import com.intellij.execution.executors.DefaultRunExecutor
import com.intellij.execution.runners.ExecutionEnvironmentBuilder
import com.intellij.openapi.project.Project
import kotlinx.coroutines.delay

suspend fun executeTerminal(
    project: Project,
    command: String,
    options: TerminalOptions,
    toolUserId: String,
    terminalId: Long,
    timeoutMillis: Long = 30000
): String {
    val factory = AIMIConfigurationFactory(AIMIConfigurationType())
    val configuration = factory.createTemplateConfiguration(project)

    // 配置参数
    configuration.command = command
    configuration.workingDirectory = project.basePath ?: project.baseDir.path
    configuration.toolUserId = toolUserId
    configuration.terminalId = terminalId

    // 执行配置
    val executor = DefaultRunExecutor.getRunExecutorInstance()
    val builder = ExecutionEnvironmentBuilder.create(executor, configuration)
    val environment = builder.build()

    ExecutionManager.getInstance(project).restartRunProfile(environment)

    // 等待一小段时间，确保进程已启动
    delay(1000) // 等待1秒

    // 获取终端输出
    return if (options.waitForCompletion) {
        val result = AIMITerminalUtil.getTerminalOutputWithStatus(project, terminalId, timeoutMillis)
        result.output
    } else {
        AIMITerminalUtil.getCurrentTerminalOutput(project, terminalId) ?: ""
    }
}